# 仓库管理系统 Docker 部署指南

本文档提供了使用 Docker 部署仓库管理系统的详细步骤。

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB 内存
- 至少 10GB 磁盘空间

## 快速部署

### 1. 克隆代码库

```bash
git clone https://github.com/waynelw/storehouse_manage.git
cd storehouse_manage/storehouse_web
```

### 2. 运行部署脚本

```bash
# 赋予脚本执行权限
chmod +x docker_deploy.sh

# 运行部署脚本
./docker_deploy.sh
```

部署完成后，可以通过以下地址访问系统：

- 前端: http://localhost
- 后端API: http://localhost/api

### 3. 使用自定义配置部署

```bash
# 使用自定义MySQL密码
./docker_deploy.sh --db-password your_password

# 使用自己的SSL证书
./docker_deploy.sh --ssl-cert /path/to/cert.pem --ssl-key /path/to/key.pem

# 不使用SSL
./docker_deploy.sh --no-ssl

# 重新构建所有容器
./docker_deploy.sh --rebuild
```

## 管理容器

### 查看容器状态

```bash
docker compose ps
```

### 查看容器日志

```bash
# 查看所有容器的日志
docker compose logs

# 实时查看日志
docker compose logs -f

# 查看特定服务的日志
docker compose logs backend
docker compose logs frontend
docker compose logs db
```

### 重启容器

```bash
# 重启所有容器
./docker_deploy.sh --restart

# 重启特定容器
docker compose restart backend
```

### 停止容器

```bash
# 停止所有容器
./docker_deploy.sh --stop

# 停止并移除所有容器
./docker_deploy.sh --down
```

## 数据持久化

数据库数据存储在Docker卷中，即使容器被删除，数据也不会丢失。

如果需要备份数据，可以使用以下命令：

```bash
# 备份数据库
docker exec storehouse-db mysqldump -u root -p storehouse > backup.sql

# 恢复数据库
cat backup.sql | docker exec -i storehouse-db mysql -u root -p storehouse
```

## 故障排除

### 1. 容器无法启动

检查日志以获取详细错误信息：

```bash
docker compose logs
```

### 2. 无法连接到数据库

确保数据库容器正在运行：

```bash
docker compose ps db
```

检查数据库日志：

```bash
docker compose logs db
```

### 3. 前端无法访问

检查Nginx配置和日志：

```bash
docker compose logs frontend
```

### 4. 后端API无法访问

检查后端日志：

```bash
docker compose logs backend
```

## 默认账户

系统初始化后会创建一个默认管理员账户：

- 用户名: admin
- 密码: admin123

首次登录后请立即修改密码。
