# 仓库管理系统部署指南

本文档提供了仓库管理系统的部署说明，包括手动部署和使用Jenkins CI/CD自动部署。

## 部署环境信息

- 远程服务器: *************
- 用户名: root
- 密码: NUtft_!5r2Mfmeu
- 域名: storehouse-manage.top, www.storehouse-manage.top
- 部署路径: /var/www/storehouse-manage.top

## 手动部署

### 前提条件

- 确保本地环境已安装以下工具:
  - Git
  - Node.js (v16+)
  - Go (v1.20+)
  - SSH客户端
  - rsync

### 使用部署脚本

项目提供了三个部署脚本:

1. `deploy-frontend.sh`: 部署前端应用
2. `deploy-backend.sh`: 部署后端应用
3. `deploy-to-remote.sh`: 主部署脚本，可以选择部署前端、后端或两者

#### 部署前端和后端

```bash
./deploy-to-remote.sh -a
# 或者
./deploy-to-remote.sh --all
# 或者直接运行(默认部署前端和后端)
./deploy-to-remote.sh
```

#### 仅部署前端

```bash
./deploy-to-remote.sh -f
# 或者
./deploy-to-remote.sh --frontend-only
```

#### 仅部署后端

```bash
./deploy-to-remote.sh -b
# 或者
./deploy-to-remote.sh --backend-only
```

### 手动部署步骤详解

如果不使用部署脚本，可以按照以下步骤手动部署:

#### 前端部署

1. 进入前端目录:
   ```bash
   cd frontend
   ```

2. 安装依赖:
   ```bash
   npm install
   ```

3. 构建前端:
   ```bash
   npm run build
   ```

4. 将构建结果部署到远程服务器:
   ```bash
   rsync -avz --delete dist/ root@*************:/var/www/storehouse-manage.top/html/
   ```

#### 后端部署

1. 进入后端目录:
   ```bash
   cd backend
   ```

2. 安装依赖:
   ```bash
   go mod tidy
   ```

3. 构建后端:
   ```bash
   GOOS=linux GOARCH=amd64 go build -o storehouse-api cmd/api/main.go
   ```

4. 将构建结果和配置文件部署到远程服务器:
   ```bash
   scp storehouse-api root@*************:/var/www/storehouse-manage.top/backend/
   scp config/config.yaml root@*************:/var/www/storehouse-manage.top/backend/config/
   ```

5. 重启后端服务:
   ```bash
   ssh root@************* "systemctl restart storehouse-api"
   ```

## Jenkins CI/CD自动部署

项目已配置Jenkins CI/CD流程，支持自动部署前端和后端应用。

### Jenkins配置

1. Jenkins服务器已配置在本地WSL环境中
2. 已配置SSH公钥登录远程服务器
3. Jenkins Pipeline使用项目根目录下的`Jenkinsfile`配置

### 触发部署

1. 在Jenkins界面中找到`Storehouse-Deploy`项目
2. 点击`Build Now`按钮触发构建和部署
3. 等待部署完成，查看构建日志确认部署状态

### 部署流程

Jenkins部署流程包括以下阶段:

1. **Checkout**: 检出代码
2. **Build & Deploy Frontend**: 构建并部署前端
3. **Build & Deploy Backend**: 构建并部署后端
4. **Verify Deployment**: 验证部署是否成功

## 部署后验证

部署完成后，可以通过以下URL访问应用:

- 前端: https://storehouse-manage.top 或 https://www.storehouse-manage.top
- 后端API: https://storehouse-manage.top/api

## 故障排除

如果部署过程中遇到问题，请检查:

1. 网络连接是否正常
2. SSH连接是否成功
3. 远程服务器上的Nginx和MySQL服务是否正常运行
4. 查看远程服务器上的日志:
   - Nginx日志: `/var/log/nginx/error.log`
   - 后端服务日志: `journalctl -u storehouse-api`

## 注意事项

- 确保远程服务器上的MySQL已正确配置
- 确保远程服务器上的Nginx已正确配置
- 部署前请备份重要数据
