# 仓库管理系统 (Web版)

基于Vue.js + Golang + MySQL的现代化仓库管理系统，采用前后端分离架构设计。

## 项目结构

```
storehouse_web/
├── frontend/           # Vue.js前端项目
│   ├── public/         # 静态资源
│   ├── src/            # 源代码
│   │   ├── assets/     # 静态资源
│   │   ├── components/ # 组件
│   │   ├── views/      # 页面
│   │   ├── router/     # 路由
│   │   ├── store/      # 状态管理
│   │   ├── api/        # API调用
│   │   └── utils/      # 工具函数
│   └── ...             # 其他配置文件
│
└── backend/            # Golang后端项目
    ├── cmd/            # 应用入口
    ├── config/         # 配置文件
    ├── internal/       # 内部包
    │   ├── api/        # API层
    │   ├── domain/     # 领域层
    │   └── infrastructure/ # 基础设施层
    └── ...             # 其他文件和目录
```

## 功能特性

- 出入库管理
  - 入库操作（采购入库、退货入库等）
  - 出库操作（销售出库、报损出库等）
  - 库存调拨
- 多仓库支持
  - 多仓库数据管理
  - 仓库间调拨
  - 不同仓库权限管理
- 库存管理
  - 实时库存查询
  - 库存预警
  - 库存盘点
- 报表功能
  - 出入库报表
  - 库存状态报表
  - 预警报表
  - 数据导出
- 用户权限管理
  - 基于角色的权限控制
  - 用户管理
  - 权限分配

## 技术栈

### 前端
- Vue.js 3
- Vue Router
- Pinia (状态管理)
- Element Plus (UI组件库)
- Axios (HTTP请求)
- ECharts (图表库)

### 后端
- Golang
- Gin Web框架
- GORM ORM框架
- MySQL数据库
- JWT认证
- Swagger API文档

## 开发环境要求

- Node.js 16+
- Go 1.20+
- MySQL 8.0+

## 快速开始

### 前端开发

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 后端开发

```bash
# 进入后端目录
cd backend

# 安装依赖
go mod tidy

# 启动开发服务器
go run cmd/api/main.go
```

### 数据库配置

1. 创建MySQL数据库
```sql
CREATE DATABASE storehouse CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改后端配置文件 `backend/config/config.yaml`

## 部署

### 前端部署

```bash
# 构建前端
cd frontend
npm run build

# 将dist目录部署到Web服务器
```

### 后端部署

```bash
# 构建后端
cd backend
go build -o storehouse-api cmd/api/main.go

# 运行后端服务
./storehouse-api
```

## 许可证

MIT
