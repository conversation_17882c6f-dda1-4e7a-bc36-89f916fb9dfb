-- 使用数据库
USE storehouse;

-- 创建支付记录表
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_date DATETIME NOT NULL,
    remarks VARCHAR(500),
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME,
    INDEX idx_order_id (order_id)
);

-- 更新订单表，添加新字段
ALTER TABLE orders
ADD COLUMN is_shipped BOOLEAN DEFAULT FALSE,
ADD COLUMN shipped_date DATETIME DEFAULT NULL,
ADD COLUMN is_delivered BOOLEAN DEFAULT FALSE,
ADD COLUMN delivered_date DATETIME DEFAULT NULL,
ADD COLUMN is_outbound_confirmed BOOLEAN DEFAULT FALSE,
ADD COLUMN outbound_date DATETIME DEFAULT NULL,
ADD COLUMN payment_method VARCHAR(50) DEFAULT NULL;
