package main

import (
	"fmt"
	"log"
	"testing"

	"golang.org/x/crypto/bcrypt"
)

func Test_encrypt(t *testing.T) {
	// 测试密码
	password := "lwdxl123"

	// 生成哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("密码: %s\n", password)
	fmt.Printf("哈希: %s\n", hashedPassword)

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte("$2a$10$ofvhrdkfZyB7PonTPZYLwe1j.kLwwb5OJM88j8buvY74CdrAXJ0yK"), []byte(password))
	if err != nil {
		fmt.Println("密码验证失败:", err)
	} else {
		fmt.Println("密码验证成功!")
	}
}
