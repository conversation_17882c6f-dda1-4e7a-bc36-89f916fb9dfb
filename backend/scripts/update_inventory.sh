#!/bin/bash

# 设置变量
SQL_FILE="./scripts/update_inventory.sql"

# 检查SQL文件是否存在
if [ ! -f "$SQL_FILE" ]; then
    echo "Error: SQL file not found: $SQL_FILE"
    exit 1
fi

# 执行SQL脚本
echo "Updating inventory data..."
mysql -uroot -proot < $SQL_FILE

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "Inventory data updated successfully."
else
    echo "Error: Failed to update inventory data."
    exit 1
fi

echo "Inventory update completed."
