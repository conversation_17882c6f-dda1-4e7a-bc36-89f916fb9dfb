-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: storehouse
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact_person` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `province` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tax_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_account` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `type` int DEFAULT '1',
  `level` int DEFAULT '3',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES (1,'C001','客户A','王五','***********',NULL,'广州市天河区XX路XX号',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,1,1,'2025-05-13 23:52:56',NULL,'2025-05-14 22:14:45'),(2,'C002','客户B','赵六','***********',NULL,'深圳市南山区XX路XX号',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,2,1,'2025-05-13 23:52:56',NULL,'2025-05-14 22:14:42'),(3,'1111','邓先生','1111111111111','18777777777','<EMAIL>','ddddddddddd','','','','','','','',1,3,1,'2025-05-14 22:25:51','2025-05-14 22:25:51',NULL);
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventories`
--

DROP TABLE IF EXISTS `inventories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` bigint unsigned NOT NULL,
  `warehouse_id` bigint unsigned NOT NULL,
  `quantity` decimal(18,4) DEFAULT '0.0000',
  `location` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reorder_point` decimal(18,4) DEFAULT NULL,
  `batch_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expiry_date` datetime(3) DEFAULT NULL,
  `last_check_date` datetime(3) DEFAULT NULL,
  `minimum_quantity` decimal(18,4) DEFAULT NULL,
  `maximum_quantity` decimal(18,4) DEFAULT NULL,
  `status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_warehouse_id` (`warehouse_id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventories`
--

LOCK TABLES `inventories` WRITE;
/*!40000 ALTER TABLE `inventories` DISABLE KEYS */;
INSERT INTO `inventories` VALUES (37,4,3,104.0000,'',0.0000,'',NULL,NULL,0.0000,0.0000,'','2025-05-14 23:38:23.312','2025-05-15 22:58:19.023',NULL),(38,5,3,16.0000,'',0.0000,'',NULL,NULL,0.0000,0.0000,'','2025-05-14 23:54:17.944','2025-05-14 23:54:17.944',NULL),(39,7,3,10.0000,'',0.0000,'',NULL,NULL,0.0000,0.0000,'','2025-05-14 23:56:10.718','2025-05-14 23:56:10.718',NULL);
/*!40000 ALTER TABLE `inventories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `order_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` decimal(18,4) NOT NULL,
  `unit_price` decimal(18,2) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `batch_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remarks` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_items`
--

LOCK TABLES `order_items` WRITE;
/*!40000 ALTER TABLE `order_items` DISABLE KEYS */;
INSERT INTO `order_items` VALUES (1,1,9,8.0000,2500.00,20000.00,'','','2025-05-14 23:15:39','2025-05-14 23:15:39',NULL),(2,1,8,6.0000,2000.00,12000.00,'','','2025-05-14 23:15:39','2025-05-14 23:15:39',NULL),(3,1,7,5.0000,500.00,2500.00,'','','2025-05-14 23:15:39','2025-05-14 23:15:39',NULL),(4,1,6,3.0000,1000.00,3000.00,'','','2025-05-14 23:15:39','2025-05-14 23:15:39',NULL),(5,1,5,3.0000,1000.00,3000.00,'','','2025-05-14 23:15:39','2025-05-14 23:15:39',NULL),(6,2,9,8.0000,2500.00,20000.00,'','','2025-05-14 23:16:23','2025-05-14 23:16:23',NULL),(7,2,8,6.0000,2000.00,12000.00,'','','2025-05-14 23:16:23','2025-05-14 23:16:23',NULL),(8,2,7,5.0000,500.00,2500.00,'','','2025-05-14 23:16:23','2025-05-14 23:16:23',NULL),(9,2,6,3.0000,1000.00,3000.00,'','','2025-05-14 23:16:23','2025-05-14 23:16:23',NULL),(10,2,5,3.0000,1000.00,3000.00,'','','2025-05-14 23:16:23','2025-05-14 23:16:23',NULL),(11,3,7,3.0000,500.00,1500.00,'','','2025-05-14 23:20:16','2025-05-14 23:20:16',NULL),(12,4,4,1.0000,1701.30,1701.30,'','','2025-05-15 23:20:06','2025-05-15 23:20:06',NULL),(13,5,4,1.0000,1700.40,1700.40,'','','2025-05-15 23:45:06','2025-05-15 23:45:06',NULL),(14,6,4,1.0000,1700.40,1700.40,'','','2025-05-15 23:45:13','2025-05-15 23:45:13',NULL),(15,7,5,1.0000,1000.00,1000.00,'','','2025-05-15 23:59:00','2025-05-15 23:59:00',NULL);
/*!40000 ALTER TABLE `order_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_id` int NOT NULL,
  `order_date` datetime NOT NULL,
  `total_amount` decimal(18,2) NOT NULL,
  `paid_amount` decimal(18,2) DEFAULT '0.00',
  `status` int DEFAULT '1',
  `remarks` text COLLATE utf8mb4_unicode_ci,
  `delivery_date` datetime DEFAULT NULL,
  `delivery_address` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_person` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `need_invoice` tinyint(1) DEFAULT '0',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `company` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
INSERT INTO `orders` VALUES (1,'ORD20250514263784',3,'2025-05-14 08:00:00',40500.00,0.00,1,'',NULL,'','','',0,'2025-05-14 23:15:39','2025-05-14 23:15:39',NULL,NULL),(2,'ORD20250514393527',3,'2025-05-14 08:00:00',40500.00,0.00,1,'',NULL,'','','',0,'2025-05-14 23:16:23','2025-05-14 23:16:23',NULL,NULL),(3,'ORD20250514756787',3,'2025-05-14 08:00:00',1500.00,0.00,1,'',NULL,'','','',0,'2025-05-14 23:20:16','2025-05-14 23:20:16',NULL,NULL),(4,'ORD20250515595366',3,'2025-05-15 08:00:00',1701.30,0.00,1,'',NULL,'','','',1,'2025-05-15 23:20:06','2025-05-15 23:20:06',NULL,NULL),(5,'ORD2025051586638',3,'2025-05-15 08:00:00',1700.40,0.00,1,'',NULL,'','','',1,'2025-05-15 23:45:06','2025-05-15 23:45:06',NULL,NULL),(6,'ORD20250515368693',3,'2025-05-15 08:00:00',1700.40,0.00,1,'',NULL,'','','',1,'2025-05-15 23:45:13','2025-05-15 23:45:13',NULL,NULL),(7,'ORD20250515110522',3,'2025-05-15 08:00:00',1000.00,0.00,1,'',NULL,'','','',0,'2025-05-15 23:59:00','2025-05-15 23:59:00',NULL,'宜宾市翠屏区固砂建材经营部(工商户)');
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` bigint DEFAULT '1',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `idx_permissions_code` (`code`),
  KEY `idx_permissions_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'用户创建','user:create','创建用户权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(2,'用户更新','user:update','更新用户权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(3,'用户删除','user:delete','删除用户权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(4,'产品创建','product:create','创建产品权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(5,'产品更新','product:update','更新产品权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(6,'产品删除','product:delete','删除产品权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(7,'仓库创建','warehouse:create','创建仓库权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(8,'仓库更新','warehouse:update','更新仓库权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(9,'仓库删除','warehouse:delete','删除仓库权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(10,'库存更新','inventory:update','更新库存权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(11,'入库操作','stock:in','入库操作权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(12,'出库操作','stock:out','出库操作权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(13,'库存调拨','stock:transfer','库存调拨权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(14,'客户创建','customer:create','创建客户权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(15,'客户更新','customer:update','更新客户权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(16,'客户删除','customer:delete','删除客户权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(17,'订单创建','order:create','创建订单权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(18,'订单更新','order:update','更新订单权限',1,'2025-05-13 23:52:56.000',NULL,NULL),(19,'订单删除','order:delete','删除订单权限',1,'2025-05-13 23:52:56.000',NULL,NULL);
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `specification` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unit` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `min_stock` decimal(18,2) DEFAULT '0.00',
  `max_stock` decimal(18,2) DEFAULT '0.00',
  `is_active` tinyint(1) DEFAULT '1',
  `barcode` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `price` decimal(18,2) DEFAULT '0.00',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `idx_products_code` (`code`),
  KEY `idx_products_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES (4,'SAM9211','高强型自粘沥青防水卷材-湿铺膜基类',NULL,'20','m²/卷',10.00,50.00,1,NULL,'防水材料',1700.00,'2025-05-14 21:24:13.000','2025-05-14 22:48:21.381',NULL),(5,'IPYPE3-1','弹性体sbs沥青卷材',NULL,'10','m²/卷',15.00,60.00,1,NULL,'防水材料',1000.00,'2025-05-14 21:24:13.000',NULL,NULL),(6,'IPYPE4-1','弹性体（SBS）沥青卷材',NULL,'10','m²/卷',12.00,55.00,1,NULL,'防水材料',1000.00,'2025-05-14 21:24:13.000',NULL,NULL),(7,'IPYPE3-2','弹性体（SBS）沥青卷材（改性）',NULL,'10','m²/卷',10.00,50.00,1,NULL,'防水材料',500.00,'2025-05-14 21:24:13.000',NULL,NULL),(8,'PBC328','非固化橡胶沥青防水涂料',NULL,'20','kg/桶',20.00,80.00,1,NULL,'防水涂料',2000.00,'2025-05-14 21:24:13.000',NULL,NULL),(9,'SPU301','25单组分聚氨酯防水涂料',NULL,'25','kg/桶',25.00,100.00,1,NULL,'防水涂料',2500.00,'2025-05-14 21:24:13.000',NULL,NULL),(10,'JSA101-1','聚合物水泥防水涂料-II-20',NULL,'20','kg/桶',15.00,70.00,1,NULL,'防水涂料',2000.00,'2025-05-14 21:24:13.000',NULL,NULL),(11,'JSA101-2','聚合物水泥防水涂料-II-24',NULL,'24','kg/袋',18.00,75.00,1,NULL,'防水涂料',2400.00,'2025-05-14 21:24:13.000',NULL,NULL),(12,'ABC-701','聚合物改性沥青化学固化型柔韧性防水材料',NULL,'10','kg/桶',10.00,50.00,1,NULL,'防水材料',1500.00,'2025-05-14 21:24:13.000',NULL,NULL),(13,'QT3080-1','高密度聚乙烯自粘胶膜防水卷材',NULL,'20','m²/卷',20.00,80.00,1,NULL,'防水卷材',1800.00,'2025-05-14 21:24:13.000',NULL,NULL),(14,'QT3080-2','QT3080-1.5-1.2x20',NULL,'20','m²/卷',15.00,60.00,1,NULL,'防水卷材',1600.00,'2025-05-14 21:24:13.000',NULL,NULL),(15,'SAM-930','自粘聚合物改性沥青防水卷材',NULL,'10','m²/卷',12.00,55.00,1,NULL,'防水卷材',1200.00,'2025-05-14 21:24:13.000',NULL,NULL),(16,'HD1981','外墙多彩弹性内外墙防水涂料',NULL,'20','kg',20.00,80.00,1,NULL,'防水涂料',1800.00,'2025-05-14 21:24:13.000',NULL,NULL),(17,'ZYY-01','注浆液',NULL,'','kg',30.00,120.00,1,NULL,'防水辅料',1000.00,'2025-05-14 21:24:13.000',NULL,NULL),(18,'ZMB-01','堵漏宝',NULL,'','kg',25.00,100.00,1,NULL,'防水辅料',800.00,'2025-05-14 21:24:13.000',NULL,NULL);
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` bigint unsigned NOT NULL,
  `permission_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_role_permissions_role_id` (`role_id`),
  KEY `idx_role_permissions_permission_id` (`permission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (1,1,14),(2,1,16),(3,1,15),(4,1,10),(5,1,17),(6,1,19),(7,1,18),(8,1,4),(9,1,6),(10,1,5),(11,1,11),(12,1,12),(13,1,13),(14,1,1),(15,1,3),(16,1,2),(17,1,7),(18,1,9),(19,1,8),(32,2,14),(33,2,15),(34,2,10),(35,2,17),(36,2,18),(37,2,4),(38,2,5),(39,2,11),(40,2,12);
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` bigint DEFAULT '1',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `idx_roles_name` (`name`),
  KEY `idx_roles_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'管理员','系统管理员角色',1,'2025-05-13 23:52:56.000',NULL,NULL),(2,'操作员','普通操作员角色',1,'2025-05-13 23:52:56.000',NULL,NULL);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stock_movements`
--

DROP TABLE IF EXISTS `stock_movements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stock_movements` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `source_warehouse_id` int NOT NULL,
  `destination_warehouse_id` int DEFAULT NULL,
  `quantity` decimal(18,4) NOT NULL,
  `unit_price` decimal(18,2) DEFAULT '0.00',
  `movement_type` int NOT NULL,
  `reason` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reference` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remarks` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `batch_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `movement_date` datetime NOT NULL,
  `customer_id` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `company` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_source_warehouse_id` (`source_warehouse_id`),
  KEY `idx_destination_warehouse_id` (`destination_warehouse_id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stock_movements`
--

LOCK TABLES `stock_movements` WRITE;
/*!40000 ALTER TABLE `stock_movements` DISABLE KEYS */;
INSERT INTO `stock_movements` VALUES (9,4,3,NULL,100.0000,10.00,1,'采购入库','','1','1','2025-05-14 08:00:00',NULL,'2025-05-14 23:38:23','2025-05-14 23:38:23',NULL,NULL),(10,5,3,NULL,16.0000,0.00,1,'采购入库','','','1','2025-05-14 08:00:00',NULL,'2025-05-14 23:54:18','2025-05-14 23:54:18',NULL,NULL),(11,7,3,NULL,10.0000,9.00,1,'采购入库','','','1','2025-05-14 08:00:00',NULL,'2025-05-14 23:56:11','2025-05-14 23:56:11',NULL,NULL),(12,4,3,NULL,7.0000,0.00,1,'采购入库','','','20250515','2025-05-15 08:00:00',NULL,'2025-05-15 21:53:15','2025-05-15 21:53:15',NULL,NULL),(13,4,3,NULL,5.0000,4.00,1,'采购入库','','','20250515','2025-05-15 08:00:00',NULL,'2025-05-15 21:55:00','2025-05-15 21:55:00',NULL,NULL),(14,4,3,NULL,8.0000,1695.00,2,'销售出库','','111','默认批次','2025-05-15 08:00:00',3,'2025-05-15 22:58:19','2025-05-15 22:58:19',NULL,NULL);
/*!40000 ALTER TABLE `stock_movements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_user_roles_user_id` (`user_id`),
  KEY `idx_user_roles_role_id` (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1,1);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `real_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` bigint DEFAULT '1',
  `last_login_at` datetime(3) DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `idx_users_username` (`username`),
  KEY `idx_users_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','$2a$10$ofvhrdkfZyB7PonTPZYLwe1j.kLwwb5OJM88j8buvY74CdrAXJ0yK','系统管理员',NULL,NULL,NULL,1,'2025-05-15 23:43:00.774','2025-05-13 23:52:56.000','2025-05-15 23:43:00.774',NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `warehouses`
--

DROP TABLE IF EXISTS `warehouses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `warehouses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `address` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact_person` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `idx_warehouses_code` (`code`),
  KEY `idx_warehouses_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `warehouses`
--

LOCK TABLES `warehouses` WRITE;
/*!40000 ALTER TABLE `warehouses` DISABLE KEYS */;
INSERT INTO `warehouses` VALUES (3,'W001','宜宾仓库',NULL,'东方雨虹(宜宾)仓储批发中心','邓女士','18408241074',1,'2025-05-14 22:47:15.000',NULL,NULL);
/*!40000 ALTER TABLE `warehouses` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16  0:18:27
