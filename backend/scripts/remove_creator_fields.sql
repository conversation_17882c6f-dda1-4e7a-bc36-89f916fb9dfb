-- 使用数据库
USE storehouse;

-- 检查并从产品表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'products' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE products DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'products' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE products DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从仓库表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'warehouses' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE warehouses DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'warehouses' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE warehouses DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从库存表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'inventories' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE inventories DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'inventories' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE inventories DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从客户表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'customers' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE customers DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'customers' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE customers DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从订单表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'orders' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE orders DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'orders' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE orders DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从订单项表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'order_items' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE order_items DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'order_items' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE order_items DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从出入库记录表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'stock_movements' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE stock_movements DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'stock_movements' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE stock_movements DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从用户表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'users' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE users DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'users' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE users DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从角色表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'roles' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE roles DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'roles' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE roles DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并从权限表中移除字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'permissions' AND column_name = 'created_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE permissions DROP COLUMN created_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns WHERE table_schema = 'storehouse' AND table_name = 'permissions' AND column_name = 'updated_by';
SET @drop_stmt = IF(@col_exists > 0, 'ALTER TABLE permissions DROP COLUMN updated_by', 'SELECT 1');
PREPARE stmt FROM @drop_stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
