-- 创建数据库
CREATE DATABASE IF NOT EXISTS storehouse CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE storehouse;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    real_name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    department VARCHAR(50),
    status INT DEFAULT 1,
    last_login_at DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME
);

-- 创建角色表
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(200),
    status INT DEFAULT 1,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME
);

-- 创建权限表
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(200),
    status INT DEFAULT 1,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME
);

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
);

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
);

-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    specification VARCHAR(200),
    unit VARCHAR(20),
    min_stock DECIMAL(18,2) DEFAULT 0,
    max_stock DECIMAL(18,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    barcode VARCHAR(50),
    category VARCHAR(50),
    price DECIMAL(18,2) DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME
);

-- 创建仓库表
CREATE TABLE IF NOT EXISTS warehouses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    address VARCHAR(200) NOT NULL,
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME
);

-- 创建库存表
CREATE TABLE IF NOT EXISTS inventories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    warehouse_id INT NOT NULL,
    quantity DECIMAL(18,4) NOT NULL DEFAULT 0,
    location VARCHAR(50),
    reorder_point DECIMAL(18,4),
    batch_number VARCHAR(50),
    expiry_date DATETIME,
    last_check_date DATETIME,
    minimum_quantity DECIMAL(18,4),
    maximum_quantity DECIMAL(18,4),
    status VARCHAR(50),
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME,
    INDEX idx_product_id (product_id),
    INDEX idx_warehouse_id (warehouse_id)
);

-- 创建客户表
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    email VARCHAR(100),
    address VARCHAR(200),
    city VARCHAR(50),
    province VARCHAR(50),
    postal_code VARCHAR(20),
    tax_id VARCHAR(50),
    bank_account VARCHAR(50),
    bank_name VARCHAR(100),
    notes TEXT,
    type INT DEFAULT 1,
    level INT DEFAULT 3,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME,
    created_by INT,
    updated_by INT
);

-- 创建出入库记录表
CREATE TABLE IF NOT EXISTS stock_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    source_warehouse_id INT NOT NULL,
    destination_warehouse_id INT,
    quantity DECIMAL(18,4) NOT NULL,
    unit_price DECIMAL(18,2) DEFAULT 0,
    movement_type INT NOT NULL,
    reason VARCHAR(100),
    reference VARCHAR(100),
    remarks VARCHAR(500),
    batch_number VARCHAR(50),
    movement_date DATETIME NOT NULL,
    customer_id INT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME,
    INDEX idx_product_id (product_id),
    INDEX idx_source_warehouse_id (source_warehouse_id),
    INDEX idx_destination_warehouse_id (destination_warehouse_id),
    INDEX idx_customer_id (customer_id)
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INT NOT NULL,
    order_date DATETIME NOT NULL,
    total_amount DECIMAL(18,2) NOT NULL,
    paid_amount DECIMAL(18,2) DEFAULT 0,
    status INT DEFAULT 1,
    remarks TEXT,
    delivery_date DATETIME,
    delivery_address VARCHAR(200),
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    need_invoice BOOLEAN DEFAULT FALSE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME,
    INDEX idx_customer_id (customer_id)
);

-- 创建订单项表
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(18,4) NOT NULL,
    unit_price DECIMAL(18,2) NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    batch_number VARCHAR(50),
    remarks VARCHAR(200),
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    deleted_at DATETIME,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
);

-- 插入初始管理员用户（密码：admin123）
INSERT INTO users (username, password, real_name, status, created_at)
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '系统管理员', 1, NOW());

-- 插入初始角色
INSERT INTO roles (name, description, status, created_at)
VALUES ('管理员', '系统管理员角色', 1, NOW()),
       ('操作员', '普通操作员角色', 1, NOW());

-- 插入初始权限
INSERT INTO permissions (name, code, description, status, created_at)
VALUES
('用户创建', 'user:create', '创建用户权限', 1, NOW()),
('用户更新', 'user:update', '更新用户权限', 1, NOW()),
('用户删除', 'user:delete', '删除用户权限', 1, NOW()),
('产品创建', 'product:create', '创建产品权限', 1, NOW()),
('产品更新', 'product:update', '更新产品权限', 1, NOW()),
('产品删除', 'product:delete', '删除产品权限', 1, NOW()),
('仓库创建', 'warehouse:create', '创建仓库权限', 1, NOW()),
('仓库更新', 'warehouse:update', '更新仓库权限', 1, NOW()),
('仓库删除', 'warehouse:delete', '删除仓库权限', 1, NOW()),
('库存更新', 'inventory:update', '更新库存权限', 1, NOW()),
('入库操作', 'stock:in', '入库操作权限', 1, NOW()),
('出库操作', 'stock:out', '出库操作权限', 1, NOW()),
('库存调拨', 'stock:transfer', '库存调拨权限', 1, NOW()),
('客户创建', 'customer:create', '创建客户权限', 1, NOW()),
('客户更新', 'customer:update', '更新客户权限', 1, NOW()),
('客户删除', 'customer:delete', '删除客户权限', 1, NOW()),
('订单创建', 'order:create', '创建订单权限', 1, NOW()),
('订单更新', 'order:update', '更新订单权限', 1, NOW()),
('订单删除', 'order:delete', '删除订单权限', 1, NOW());

-- 为管理员角色分配所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- 为操作员角色分配部分权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE code IN ('product:create', 'product:update', 'inventory:update', 'stock:in', 'stock:out', 'customer:create', 'customer:update', 'order:create', 'order:update');

-- 为管理员用户分配管理员角色
INSERT INTO user_roles (user_id, role_id)
VALUES (1, 1);

-- 插入示例仓库数据
INSERT INTO warehouses (code, name, address, contact_person, contact_phone, is_active, created_at)
VALUES
('W001', '宜宾仓库', '东方雨虹(宜宾)仓储批发中心', '邓女士', '18408241074', true, NOW());

-- 插入示例库存数据
INSERT INTO inventories (product_id, warehouse_id, quantity, location, created_at)
VALUES
(1, 1, 10, 'A区-01-01', NOW()),
(2, 1, 5, 'A区-01-02', NOW()),
(3, 1, 25, 'A区-01-03', NOW()),
(1, 2, 15, 'B区-01-01', NOW()),
(2, 2, 10, 'B区-01-02', NOW()),
(3, 2, 20, 'B区-01-03', NOW());

-- 插入示例客户数据
INSERT INTO customers (code, name, contact_person, contact_phone, address, type, level, is_active, created_at)
VALUES
('C001', '客户A', '王五', '13800138003', '广州市天河区XX路XX号', 1, 1, true, NOW()),
('C002', '客户B', '赵六', '13800138004', '深圳市南山区XX路XX号', 1, 2, true, NOW());

-- 插入示例出入库记录
INSERT INTO stock_movements (product_id, source_warehouse_id, quantity, unit_price, movement_type, reason, movement_date, created_at)
VALUES
(1, 1, 100, 45.00, 1, '采购入库', NOW(), NOW()),
(2, 1, 50, 25.00, 1, '采购入库', NOW(), NOW()),
(3, 1, 30, 40.00, 2, '销售出库', NOW(), NOW());

-- 插入防水材料产品数据
INSERT INTO products (code, name, specification, unit, price, category, created_at)
VALUES
('SAM9211', '高强型自粘沥青防水卷材-湿铺膜基类', '20', 'm²/卷', 1700.00, '防水材料', NOW()),
('IPYPE3-1', '弹性体sbs沥青卷材', '10', 'm²/卷', 1000.00, '防水材料', NOW()),
('IPYPE4-1', '弹性体（SBS）沥青卷材', '10', 'm²/卷', 1000.00, '防水材料', NOW()),
('IPYPE3-2', '弹性体（SBS）沥青卷材（改性）', '10', 'm²/卷', 500.00, '防水材料', NOW()),
('PBC328', '非固化橡胶沥青防水涂料', '20', 'kg/桶', 2000.00, '防水涂料', NOW()),
('SPU301', '25单组分聚氨酯防水涂料', '25', 'kg/桶', 2500.00, '防水涂料', NOW()),
('JSA101-1', '聚合物水泥防水涂料-II-20', '20', 'kg/桶', 2000.00, '防水涂料', NOW()),
('JSA101-2', '聚合物水泥防水涂料-II-24', '24', 'kg/袋', 2400.00, '防水涂料', NOW()),
('ABC-701', '聚合物改性沥青化学固化型柔韧性防水材料', '10', 'kg/桶', 1500.00, '防水材料', NOW()),
('QT3080-1', '高密度聚乙烯自粘胶膜防水卷材', '20', 'm²/卷', 1800.00, '防水卷材', NOW()),
('QT3080-2', 'QT3080-1.5-1.2x20', '20', 'm²/卷', 1600.00, '防水卷材', NOW()),
('SAM-930', '自粘聚合物改性沥青防水卷材', '10', 'm²/卷', 1200.00, '防水卷材', NOW()),
('HD1981', '外墙多彩弹性内外墙防水涂料', '20', 'kg', 1800.00, '防水涂料', NOW()),
('ZYY-01', '注浆液', '', 'kg', 1000.00, '防水辅料', NOW()),
('ZMB-01', '堵漏宝', '', 'kg', 800.00, '防水辅料', NOW());