#!/bin/bash

# 设置工作目录
cd "$(dirname "$0")/.."

# 检查配置文件是否存在
if [ ! -f "./config/config.yaml" ]; then
    echo "Error: config.yaml not found!"
    exit 1
fi

# 设置Go代理
echo "Setting Go proxy..."
go env -w GOPROXY=https://goproxy.cn,direct

# 安装依赖
echo "Installing dependencies..."
go mod tidy

# 构建项目
echo "Building project..."
go build -o ./bin/storehouse-api ./cmd/api/main.go

# 启动服务
echo "Starting API server..."
./bin/storehouse-api
