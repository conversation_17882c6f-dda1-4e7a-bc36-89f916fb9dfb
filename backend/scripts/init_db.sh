#!/bin/bash

# 设置变量
DB_NAME="storehouse"
SQL_FILE="./scripts/init_db.sql"

# 检查SQL文件是否存在
if [ ! -f "$SQL_FILE" ]; then
    echo "Error: SQL file not found: $SQL_FILE"
    exit 1
fi

# 创建数据库和导入数据
echo "Creating database and importing data..."
sudo mysql < $SQL_FILE

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "Database initialization completed successfully."
else
    echo "Error: Failed to initialize database."
    exit 1
fi

echo "Database setup completed."
