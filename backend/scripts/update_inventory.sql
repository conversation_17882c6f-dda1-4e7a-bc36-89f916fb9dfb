-- 使用数据库
USE storehouse;

-- 更新产品的最小和最大库存
UPDATE products SET min_stock = 10, max_stock = 50 WHERE code = 'SAM9211';
UPDATE products SET min_stock = 15, max_stock = 60 WHERE code = 'IPYPE3-1';
UPDATE products SET min_stock = 12, max_stock = 55 WHERE code = 'IPYPE4-1';
UPDATE products SET min_stock = 10, max_stock = 50 WHERE code = 'IPYPE3-2';
UPDATE products SET min_stock = 20, max_stock = 80 WHERE code = 'PBC328';
UPDATE products SET min_stock = 25, max_stock = 100 WHERE code = 'SPU301';
UPDATE products SET min_stock = 15, max_stock = 70 WHERE code = 'JSA101-1';
UPDATE products SET min_stock = 18, max_stock = 75 WHERE code = 'JSA101-2';
UPDATE products SET min_stock = 10, max_stock = 50 WHERE code = 'ABC-701';
UPDATE products SET min_stock = 20, max_stock = 80 WHERE code = 'QT3080-1';
UPDATE products SET min_stock = 15, max_stock = 60 WHERE code = 'QT3080-2';
UPDATE products SET min_stock = 12, max_stock = 55 WHERE code = 'SAM-930';
UPDATE products SET min_stock = 20, max_stock = 80 WHERE code = 'HD1981';
UPDATE products SET min_stock = 30, max_stock = 120 WHERE code = 'ZYY-01';
UPDATE products SET min_stock = 25, max_stock = 100 WHERE code = 'ZMB-01';

-- 清空现有库存数据（如果需要重新插入）
-- DELETE FROM inventories;

-- 插入库存数据 - 主仓库(id=1)
INSERT INTO inventories (product_id, warehouse_id, quantity, location, created_at) VALUES
(4, 1, 20, 'A区-01-01', NOW()),
(5, 1, 10, 'A区-01-02', NOW()),
(6, 1, 13, 'A区-01-03', NOW()),
(7, 1, 20, 'A区-01-04', NOW()),
(8, 1, 25, 'A区-02-01', NOW()),
(9, 1, 30, 'A区-02-02', NOW()),
(10, 1, 15, 'A区-02-03', NOW()),
(11, 1, 18, 'A区-02-04', NOW()),
(12, 1, 10, 'A区-03-01', NOW()),
(13, 1, 22, 'A区-03-02', NOW()),
(14, 1, 15, 'A区-03-03', NOW()),
(15, 1, 12, 'A区-03-04', NOW()),
(16, 1, 20, 'A区-04-01', NOW()),
(17, 1, 30, 'A区-04-02', NOW()),
(18, 1, 25, 'A区-04-03', NOW());

-- 插入库存数据 - 分仓库(id=2)
INSERT INTO inventories (product_id, warehouse_id, quantity, location, created_at) VALUES
(4, 2, 15, 'B区-01-01', NOW()),
(5, 2, 20, 'B区-01-02', NOW()),
(6, 2, 15, 'B区-01-03', NOW()),
(7, 2, 10, 'B区-01-04', NOW()),
(8, 2, 15, 'B区-02-01', NOW()),
(9, 2, 20, 'B区-02-02', NOW()),
(10, 2, 25, 'B区-02-03', NOW()),
(11, 2, 20, 'B区-02-04', NOW()),
(12, 2, 15, 'B区-03-01', NOW()),
(13, 2, 18, 'B区-03-02', NOW()),
(14, 2, 20, 'B区-03-03', NOW()),
(15, 2, 15, 'B区-03-04', NOW()),
(16, 2, 25, 'B区-04-01', NOW()),
(17, 2, 35, 'B区-04-02', NOW()),
(18, 2, 30, 'B区-04-03', NOW());
