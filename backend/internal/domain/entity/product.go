package entity

// Product 产品实体
type Product struct {
	ID           uint    `json:"id" gorm:"primaryKey"`
	Code         string  `json:"code" gorm:"column:code;size:50;not null;uniqueIndex"`
	Name         string  `json:"name" gorm:"column:name;size:100;not null"`
	Description  string  `json:"description" gorm:"column:description;type:text"`
	Specification string  `json:"specification" gorm:"column:specification;size:200"`
	Unit         string  `json:"unit" gorm:"column:unit;size:20"`
	MinStock     float64 `json:"minStock" gorm:"column:min_stock;type:decimal(18,2);default:0"`
	MaxStock     float64 `json:"maxStock" gorm:"column:max_stock;type:decimal(18,2);default:0"`
	IsActive     bool    `json:"isActive" gorm:"column:is_active;default:true"`
	Barcode      string  `json:"barcode" gorm:"column:barcode;size:50"`
	Category     string  `json:"category" gorm:"column:category;size:50"`
	Price        float64 `json:"price" gorm:"column:price;type:decimal(18,2);default:0"`
	BaseEntity

	// 关联
	Inventories    []Inventory     `json:"inventories" gorm:"foreignKey:ProductID"`
	StockMovements []StockMovement `json:"stockMovements" gorm:"foreignKey:ProductID"`
}
