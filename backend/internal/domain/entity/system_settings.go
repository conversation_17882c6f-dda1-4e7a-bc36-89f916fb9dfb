package entity

// SystemSettings 系统设置实体
type SystemSettings struct {
	ID             uint   `json:"id" gorm:"primaryKey"`
	SystemName     string `json:"systemName" gorm:"column:system_name;size:100;not null;default:'仓库管理系统'"`
	CompanyName    string `json:"companyName" gorm:"column:company_name;size:100"`
	ContactPhone   string `json:"contactPhone" gorm:"column:contact_phone;size:20"`
	ContactEmail   string `json:"contactEmail" gorm:"column:contact_email;size:100"`
	Address        string `json:"address" gorm:"column:address;size:200"`
	OrderPrefix    string `json:"orderPrefix" gorm:"column:order_prefix;size:10;default:'ORD'"`
	ProductPrefix  string `json:"productPrefix" gorm:"column:product_prefix;size:10;default:'PRD'"`
	CustomerPrefix string `json:"customerPrefix" gorm:"column:customer_prefix;size:10;default:'CUS'"`
	PageSize       int    `json:"pageSize" gorm:"column:page_size;default:10"`
	BaseEntity
}
