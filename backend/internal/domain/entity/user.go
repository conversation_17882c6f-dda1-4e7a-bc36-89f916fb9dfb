package entity

import "time"

// User 用户实体
type User struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	Username     string    `json:"username" gorm:"column:username;size:50;not null;uniqueIndex"`
	Password     string    `json:"-" gorm:"column:password;size:100;not null"`
	RealName     string    `json:"realName" gorm:"column:real_name;size:50"`
	Email        string    `json:"email" gorm:"column:email;size:100"`
	Phone        string    `json:"phone" gorm:"column:phone;size:20"`
	Department   string    `json:"department" gorm:"column:department;size:50"`
	Status       int       `json:"status" gorm:"column:status;default:1"` // 1: 启用, 0: 禁用
	LastLoginAt  time.Time `json:"lastLoginAt" gorm:"column:last_login_at"`
	BaseEntity

	// 关联
	Roles []Role `json:"roles" gorm:"many2many:user_roles"`
}

// Role 角色实体
type Role struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"column:name;size:50;not null;uniqueIndex"`
	Description string `json:"description" gorm:"column:description;size:200"`
	Status      int    `json:"status" gorm:"column:status;default:1"` // 1: 启用, 0: 禁用
	BaseEntity

	// 关联
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions"`
	Users       []User       `json:"users" gorm:"many2many:user_roles"`
}

// Permission 权限实体
type Permission struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"column:name;size:50;not null"`
	Code        string `json:"code" gorm:"column:code;size:50;not null;uniqueIndex"`
	Description string `json:"description" gorm:"column:description;size:200"`
	Status      int    `json:"status" gorm:"column:status;default:1"` // 1: 启用, 0: 禁用
	BaseEntity

	// 关联
	Roles []Role `json:"roles" gorm:"many2many:role_permissions"`
}

// UserRole 用户角色关联
type UserRole struct {
	ID     uint `json:"id" gorm:"primaryKey"`
	UserID uint `json:"userId" gorm:"column:user_id;not null;index"`
	RoleID uint `json:"roleId" gorm:"column:role_id;not null;index"`
}

// RolePermission 角色权限关联
type RolePermission struct {
	ID           uint `json:"id" gorm:"primaryKey"`
	RoleID       uint `json:"roleId" gorm:"column:role_id;not null;index"`
	PermissionID uint `json:"permissionId" gorm:"column:permission_id;not null;index"`
}
