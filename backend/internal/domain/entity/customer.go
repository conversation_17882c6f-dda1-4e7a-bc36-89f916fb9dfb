package entity

// CustomerType 客户类型
type CustomerType int

const (
	Regular     CustomerType = 1 // 普通客户
	Distributor CustomerType = 2 // 经销商
	Supplier    CustomerType = 3 // 供应商
	Partner     CustomerType = 4 // 合作伙伴
)

// CustomerLevel 客户等级
type CustomerLevel int

const (
	VIP       CustomerLevel = 1 // VIP客户
	Premium   CustomerLevel = 2 // 高级客户
	Normal    CustomerLevel = 3 // 普通客户
	Potential CustomerLevel = 4 // 潜在客户
)

// Customer 客户实体
type Customer struct {
	ID            uint          `json:"id" gorm:"primaryKey"`
	Code          string        `json:"code" gorm:"column:code;size:50;not null;uniqueIndex"`
	Name          string        `json:"name" gorm:"column:name;size:100;not null"`
	ContactPerson string        `json:"contactPerson" gorm:"column:contact_person;size:50"`
	ContactPhone  string        `json:"contactPhone" gorm:"column:contact_phone;size:20"`
	Email         string        `json:"email" gorm:"column:email;size:100"`
	Address       string        `json:"address" gorm:"column:address;size:200"`
	City          string        `json:"city" gorm:"column:city;size:50"`
	Province      string        `json:"province" gorm:"column:province;size:50"`
	PostalCode    string        `json:"postalCode" gorm:"column:postal_code;size:20"`
	TaxID         string        `json:"taxId" gorm:"column:tax_id;size:50"`
	BankAccount   string        `json:"bankAccount" gorm:"column:bank_account;size:50"`
	BankName      string        `json:"bankName" gorm:"column:bank_name;size:100"`
	Notes         string        `json:"notes" gorm:"column:notes;type:text"`
	Type          CustomerType  `json:"type" gorm:"column:type;default:1"`
	Level         CustomerLevel `json:"level" gorm:"column:level;default:3"`
	IsActive      bool          `json:"isActive" gorm:"column:is_active;default:true"`
	SalesPersonID *uint         `json:"salesPersonId" gorm:"column:sales_person_id;index"`
	BaseEntity

	// 关联
	StockMovements []StockMovement `json:"stockMovements" gorm:"foreignKey:CustomerID"`
	Orders         []Order         `json:"orders" gorm:"foreignKey:CustomerID"`
	SalesPerson    *User           `json:"salesPerson" gorm:"foreignKey:SalesPersonID"`
}
