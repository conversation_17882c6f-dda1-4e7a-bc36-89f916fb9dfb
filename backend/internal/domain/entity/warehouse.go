package entity

// Warehouse 仓库实体
type Warehouse struct {
	ID           uint   `json:"id" gorm:"primaryKey"`
	Code         string `json:"code" gorm:"column:code;size:50;not null;uniqueIndex"`
	Name         string `json:"name" gorm:"column:name;size:100;not null"`
	Description  string `json:"description" gorm:"column:description;type:text"`
	Address      string `json:"address" gorm:"column:address;size:200;not null"`
	ContactPerson string `json:"contactPerson" gorm:"column:contact_person;size:50"`
	ContactPhone string `json:"contactPhone" gorm:"column:contact_phone;size:20"`
	IsActive     bool   `json:"isActive" gorm:"column:is_active;default:true"`
	BaseEntity

	// 关联
	Inventories []Inventory `json:"inventories" gorm:"foreignKey:WarehouseID"`
}
