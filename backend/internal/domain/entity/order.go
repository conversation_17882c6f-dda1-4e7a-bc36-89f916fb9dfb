package entity

import "time"

// OrderStatus 订单状态
type OrderStatus int

const (
	Created    OrderStatus = 1 // 已创建
	Confirmed  OrderStatus = 2 // 已确认
	Processing OrderStatus = 3 // 处理中
	Shipped    OrderStatus = 4 // 已发货
	Delivered  OrderStatus = 5 // 已交付
	Completed  OrderStatus = 6 // 已完成
	Cancelled  OrderStatus = 7 // 已取消
)

// Order 订单实体
type Order struct {
	ID                  uint        `json:"id" gorm:"primaryKey"`
	OrderNumber         string      `json:"orderNumber" gorm:"column:order_number;size:50;not null;uniqueIndex"`
	CustomerID          uint        `json:"customerId" gorm:"column:customer_id;not null;index"`
	OrderDate           time.Time   `json:"orderDate" gorm:"column:order_date;not null"`
	TotalAmount         float64     `json:"totalAmount" gorm:"column:total_amount;type:decimal(18,2);not null"`
	PaidAmount          float64     `json:"paidAmount" gorm:"column:paid_amount;type:decimal(18,2);default:0"`
	Status              OrderStatus `json:"status" gorm:"column:status;default:1"`
	Remarks             string      `json:"remarks" gorm:"column:remarks;type:text"`
	DeliveryDate        *time.Time  `json:"deliveryDate" gorm:"column:delivery_date"`
	DeliveryAddress     string      `json:"deliveryAddress" gorm:"column:delivery_address;size:200"`
	ContactPerson       string      `json:"contactPerson" gorm:"column:contact_person;size:50"`
	ContactPhone        string      `json:"contactPhone" gorm:"column:contact_phone;size:20"`
	NeedInvoice         bool        `json:"needInvoice" gorm:"column:need_invoice;default:false"`
	Company             string      `json:"company" gorm:"column:company;size:100"`
	IsShipped           bool        `json:"isShipped" gorm:"column:is_shipped;default:false"`
	ShippedDate         *time.Time  `json:"shippedDate" gorm:"column:shipped_date"`
	IsDelivered         bool        `json:"isDelivered" gorm:"column:is_delivered;default:false"`
	DeliveredDate       *time.Time  `json:"deliveredDate" gorm:"column:delivered_date"`
	IsOutboundConfirmed bool        `json:"isOutboundConfirmed" gorm:"column:is_outbound_confirmed;default:false"`
	OutboundDate        *time.Time  `json:"outboundDate" gorm:"column:outbound_date"`
	PaymentMethod       string      `json:"paymentMethod" gorm:"column:payment_method;size:50"`
	BaseEntity

	// 关联
	Customer Customer    `json:"customer" gorm:"foreignKey:CustomerID"`
	Items    []OrderItem `json:"items" gorm:"foreignKey:OrderID"`
	Payments []Payment   `json:"payments" gorm:"foreignKey:OrderID"`
}

// OrderItem 订单项
type OrderItem struct {
	ID          uint    `json:"id" gorm:"primaryKey"`
	OrderID     uint    `json:"orderId" gorm:"column:order_id;not null;index"`
	ProductID   uint    `json:"productId" gorm:"column:product_id;not null;index"`
	Quantity    float64 `json:"quantity" gorm:"column:quantity;type:decimal(18,4);not null"`
	UnitPrice   float64 `json:"unitPrice" gorm:"column:unit_price;type:decimal(18,2);not null"`
	Amount      float64 `json:"amount" gorm:"column:amount;type:decimal(18,2);not null"`
	BatchNumber string  `json:"batchNumber" gorm:"column:batch_number;size:50"`
	Remarks     string  `json:"remarks" gorm:"column:remarks;size:200"`
	BaseEntity

	// 关联
	Order   Order   `json:"order" gorm:"foreignKey:OrderID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// PaymentMethod 支付方式
type PaymentMethod string

const (
	PaymentMethodCorporate PaymentMethod = "corporate" // 对公
	PaymentMethodPersonal  PaymentMethod = "personal"  // 对私
	PaymentMethodWechat    PaymentMethod = "wechat"    // 微信
	PaymentMethodAlipay    PaymentMethod = "alipay"    // 支付宝
)

// Payment 支付记录
type Payment struct {
	ID            uint          `json:"id" gorm:"primaryKey"`
	OrderID       uint          `json:"orderId" gorm:"column:order_id;not null;index"`
	Amount        float64       `json:"amount" gorm:"column:amount;type:decimal(18,2);not null"`
	PaymentMethod PaymentMethod `json:"paymentMethod" gorm:"column:payment_method;size:50;not null"`
	PaymentDate   time.Time     `json:"paymentDate" gorm:"column:payment_date;not null"`
	Remarks       string        `json:"remarks" gorm:"column:remarks;size:200"`
	BaseEntity

	// 关联
	Order Order `json:"order" gorm:"foreignKey:OrderID"`
}
