package entity

import "time"

// MovementType 出入库类型
type MovementType int

const (
	StockIn    MovementType = 1 // 入库
	StockOut   MovementType = 2 // 出库
	Transfer   MovementType = 3 // 调拨
	Adjustment MovementType = 4 // 调整
)

// StockMovement 出入库记录
type StockMovement struct {
	ID                     uint         `json:"id" gorm:"primaryKey"`
	ProductID              uint         `json:"productId" gorm:"column:product_id;not null;index"`
	SourceWarehouseID      uint         `json:"sourceWarehouseId" gorm:"column:source_warehouse_id;not null;index"`
	DestinationWarehouseID *uint        `json:"destinationWarehouseId" gorm:"column:destination_warehouse_id;index"`
	Quantity               float64      `json:"quantity" gorm:"column:quantity;type:decimal(18,4);not null"`
	UnitPrice              float64      `json:"unitPrice" gorm:"column:unit_price;type:decimal(18,2);default:0"`
	MovementType           MovementType `json:"movementType" gorm:"column:movement_type;not null"`
	Reason                 string       `json:"reason" gorm:"column:reason;size:100"`
	Reference              string       `json:"reference" gorm:"column:reference;size:100"`
	Remarks                string       `json:"remarks" gorm:"column:remarks;size:500"`
	BatchNumber            string       `json:"batchNumber" gorm:"column:batch_number;size:50"`
	MovementDate           time.Time    `json:"movementDate" gorm:"column:movement_date;not null"`
	CustomerID             *uint        `json:"customerId" gorm:"column:customer_id;index"`
	Company                string       `json:"company" gorm:"column:company;size:100"`
	BaseEntity

	// 关联
	Product              Product    `json:"product" gorm:"foreignKey:ProductID"`
	SourceWarehouse      Warehouse  `json:"sourceWarehouse" gorm:"foreignKey:SourceWarehouseID"`
	DestinationWarehouse *Warehouse `json:"destinationWarehouse" gorm:"foreignKey:DestinationWarehouseID"`
	Customer             *Customer  `json:"customer" gorm:"foreignKey:CustomerID"`
}
