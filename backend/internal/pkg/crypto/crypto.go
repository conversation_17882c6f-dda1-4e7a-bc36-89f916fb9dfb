package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"errors"
	"strings"

	"github.com/yourusername/storehouse/internal/pkg/logger"
)

// 固定的加密密钥和盐值 (生产环境应从配置中获取)
const (
	// AES-256 需要32字节密钥
	secretKey = "storehouse-secure-key-2023-secret32"
	salt      = "storehouse-salt-value"
	// 固定的初始化向量 (IV) - 16字节
	iv = "storehouse-iv-123"
)

// 获取32字节的AES密钥
func getAESKey() []byte {
	// 确保密钥长度为32字节(AES-256)
	key := []byte(secretKey)
	if len(key) > 32 {
		return key[:32]
	}

	// 如果密钥不足32字节，使用填充
	result := make([]byte, 32)
	copy(result, key)
	for i := len(key); i < 32; i++ {
		result[i] = byte(i % 256)
	}
	return result
}

// 获取16字节的初始化向量
func getIV() []byte {
	// 确保IV长度为16字节(AES块大小)
	ivBytes := []byte(iv)
	if len(ivBytes) > 16 {
		return ivBytes[:16]
	}

	// 如果IV不足16字节，使用填充
	result := make([]byte, 16)
	copy(result, ivBytes)
	for i := len(ivBytes); i < 16; i++ {
		result[i] = byte(i % 256)
	}
	return result
}

// DecryptPassword 解密前端加密的密码
func DecryptPassword(encryptedPassword string) (string, error) {
	if encryptedPassword == "" {
		return "", errors.New("encrypted password is empty")
	}

	// 解码Base64
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedPassword)
	if err != nil {
		logger.Error("Failed to decode base64", err)
		return "", err
	}

	// 创建AES解密器，使用32字节密钥
	block, err := aes.NewCipher(getAESKey())
	if err != nil {
		logger.Error("Failed to create AES cipher", err)
		return "", err
	}

	// 创建CBC解密器，使用固定IV
	mode := cipher.NewCBCDecrypter(block, getIV())

	// 创建明文缓冲区
	plaintext := make([]byte, len(ciphertext))
	copy(plaintext, ciphertext)

	// 解密
	mode.CryptBlocks(plaintext, plaintext)

	// 去除PKCS#7填充
	paddingLen := int(plaintext[len(plaintext)-1])
	if paddingLen > aes.BlockSize || paddingLen == 0 {
		return "", errors.New("invalid padding")
	}

	// 验证填充
	for i := len(plaintext) - paddingLen; i < len(plaintext); i++ {
		if plaintext[i] != byte(paddingLen) {
			return "", errors.New("invalid padding")
		}
	}

	// 移除填充
	plaintext = plaintext[:len(plaintext)-paddingLen]

	// 转换为字符串并移除盐值
	decrypted := string(plaintext)
	if strings.HasSuffix(decrypted, salt) {
		decrypted = decrypted[:len(decrypted)-len(salt)]
		return decrypted, nil
	}

	return decrypted, nil
}
