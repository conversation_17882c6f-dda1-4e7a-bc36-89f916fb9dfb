package logger

import (
	"errors"
	"fmt"
	"os"
	"runtime"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/pkg/config"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var log *zap.Logger

// InitLogger 初始化日志
func InitLogger(cfg config.LogConfig) error {
	var level zapcore.Level
	switch cfg.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	var writeSyncer zapcore.WriteSyncer
	if cfg.Output == "file" {
		file, err := os.OpenFile(cfg.FilePath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return err
		}
		writeSyncer = zapcore.AddSync(file)
	} else {
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	core := zapcore.NewCore(encoder, writeSyncer, level)
	log = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))

	return nil
}

// Debug 输出debug级别日志
func Debug(msg string, fields ...zap.Field) {
	log.Debug(msg, fields...)
}

// DebugWithContext 输出带有上下文的debug级别日志
func DebugWithContext(c *gin.Context, msg string, fields ...zap.Field) {
	// 添加请求ID
	if requestID, exists := c.Get("X-Request-ID"); exists {
		fields = append(fields, zap.String("request_id", requestID.(string)))
	}

	// 添加请求路径
	fields = append(fields, zap.String("path", c.Request.URL.Path))

	// 添加请求方法
	fields = append(fields, zap.String("method", c.Request.Method))

	// 添加客户端IP
	fields = append(fields, zap.String("client_ip", c.ClientIP()))

	log.Debug(msg, fields...)
}

// Info 输出info级别日志
func Info(msg string, fields ...zap.Field) {
	log.Info(msg, fields...)
}

// InfoWithContext 输出带有上下文的info级别日志
func InfoWithContext(c *gin.Context, msg string, fields ...zap.Field) {
	// 添加请求ID
	if requestID, exists := c.Get("X-Request-ID"); exists {
		fields = append(fields, zap.String("request_id", requestID.(string)))
	}

	// 添加请求路径
	fields = append(fields, zap.String("path", c.Request.URL.Path))

	// 添加请求方法
	fields = append(fields, zap.String("method", c.Request.Method))

	// 添加客户端IP
	fields = append(fields, zap.String("client_ip", c.ClientIP()))

	log.Info(msg, fields...)
}

// Warn 输出warn级别日志
func Warn(msg string, fields ...zap.Field) {
	log.Warn(msg, fields...)
}

// Error 输出error级别日志
func Error(msg string, err error, fields ...zap.Field) {
	if err != nil {
		fields = append(fields, zap.Error(err))

		// 添加错误类型信息
		fields = append(fields, zap.String("error_type", fmt.Sprintf("%T", err)))

		// 如果错误实现了 Unwrap 方法，添加原始错误
		if unwrapped := errors.Unwrap(err); unwrapped != nil {
			fields = append(fields, zap.String("unwrapped_error", unwrapped.Error()))
		}
	}

	// 添加调用栈信息
	fields = append(fields, zap.Stack("stack_trace"))

	log.Error(msg, fields...)
}

// ErrorWithContext 输出带有上下文的error级别日志
func ErrorWithContext(c *gin.Context, msg string, err error, fields ...zap.Field) {
	if err != nil {
		fields = append(fields, zap.Error(err))
	}

	// 添加请求ID
	if requestID, exists := c.Get("X-Request-ID"); exists {
		fields = append(fields, zap.String("request_id", requestID.(string)))
	}

	// 添加请求路径
	fields = append(fields, zap.String("path", c.Request.URL.Path))

	// 添加请求方法
	fields = append(fields, zap.String("method", c.Request.Method))

	// 添加客户端IP
	fields = append(fields, zap.String("client_ip", c.ClientIP()))

	log.Error(msg, fields...)
}

// Fatal 输出fatal级别日志
func Fatal(msg string, err error, fields ...zap.Field) {
	if err != nil {
		fields = append(fields, zap.Error(err))
	}
	log.Fatal(msg, fields...)
}

// FatalWithContext 输出带有上下文的fatal级别日志
func FatalWithContext(c *gin.Context, msg string, err error, fields ...zap.Field) {
	if err != nil {
		fields = append(fields, zap.Error(err))
	}

	// 添加请求ID
	if requestID, exists := c.Get("X-Request-ID"); exists {
		fields = append(fields, zap.String("request_id", requestID.(string)))
	}

	// 添加请求路径
	fields = append(fields, zap.String("path", c.Request.URL.Path))

	// 添加请求方法
	fields = append(fields, zap.String("method", c.Request.Method))

	// 添加客户端IP
	fields = append(fields, zap.String("client_ip", c.ClientIP()))

	log.Fatal(msg, fields...)
}

// With 创建带有指定字段的Logger
func With(fields ...zap.Field) *zap.Logger {
	return log.With(fields...)
}

// ErrorDetail 输出详细的错误信息，包括文件名、行号和函数名
// skip 参数指定要跳过的调用栈帧数，通常为1（直接调用者）
func ErrorDetail(msg string, err error, skip int) {
	if err == nil {
		return
	}

	// 获取调用者信息
	pc, file, line, ok := runtime.Caller(skip)
	details := runtime.FuncForPC(pc)

	fields := []zap.Field{
		zap.Error(err),
		zap.String("error_type", fmt.Sprintf("%T", err)),
	}

	// 添加调用者信息
	if ok && details != nil {
		fields = append(fields, zap.String("file", file))
		fields = append(fields, zap.Int("line", line))
		fields = append(fields, zap.String("function", details.Name()))
	}

	// 添加错误链信息
	var errChain []string
	currentErr := err
	for currentErr != nil {
		errChain = append(errChain, fmt.Sprintf("%v", currentErr))
		currentErr = errors.Unwrap(currentErr)
	}
	if len(errChain) > 1 {
		fields = append(fields, zap.Strings("error_chain", errChain))
	}

	// 添加调用栈
	fields = append(fields, zap.Stack("stack_trace"))

	log.Error(msg, fields...)
}

// ErrorTrace 简化版的ErrorDetail，默认跳过1个调用栈帧
func ErrorTrace(msg string, err error) {
	ErrorDetail(msg, err, 1)
}

// SQLError 记录SQL错误，包含SQL语句和参数
func SQLError(msg string, err error, sql string, params ...any) {
	if err == nil {
		return
	}

	fields := []zap.Field{
		zap.Error(err),
		zap.String("sql", sql),
	}

	// 添加SQL参数
	if len(params) > 0 {
		paramValues := make([]string, len(params))
		for i, param := range params {
			paramValues[i] = fmt.Sprintf("%v", param)
		}
		fields = append(fields, zap.Strings("params", paramValues))
	}

	// 添加调用栈
	fields = append(fields, zap.Stack("stack_trace"))

	log.Error(msg, fields...)
}

// LogError 简洁地记录错误，自动添加文件名、行号和函数名
// 用法: logger.LogError(err, "操作描述", "参数1", 值1, "参数2", 值2, ...)
func LogError(err error, operation string, keyValues ...any) {
	if err == nil {
		return
	}

	// 获取调用者信息
	pc, file, line, ok := runtime.Caller(1)
	details := runtime.FuncForPC(pc)

	fields := []zap.Field{
		zap.Error(err),
		zap.String("operation", operation),
	}

	// 添加调用者信息
	if ok && details != nil {
		fields = append(fields, zap.String("file", file))
		fields = append(fields, zap.Int("line", line))
		fields = append(fields, zap.String("function", details.Name()))
	}

	// 添加额外的键值对
	if len(keyValues) > 0 && len(keyValues)%2 == 0 {
		for i := 0; i < len(keyValues); i += 2 {
			key, ok := keyValues[i].(string)
			if !ok {
				continue
			}
			value := keyValues[i+1]
			fields = append(fields, zap.Any(key, value))
		}
	}

	log.Error("错误发生", fields...)
}

// DBError 简洁地记录数据库操作错误
// 用法: logger.DBError(err, "查询客户", "id", id, "name", name)
func DBError(err error, operation string, keyValues ...any) {
	if err == nil {
		return
	}

	// 获取调用者信息
	pc, file, line, ok := runtime.Caller(1)
	details := runtime.FuncForPC(pc)

	fields := []zap.Field{
		zap.Error(err),
		zap.String("db_operation", operation),
		zap.String("error_type", fmt.Sprintf("%T", err)),
	}

	// 添加调用者信息
	if ok && details != nil {
		fields = append(fields, zap.String("file", file))
		fields = append(fields, zap.Int("line", line))
		fields = append(fields, zap.String("function", details.Name()))
	}

	// 添加额外的键值对
	if len(keyValues) > 0 && len(keyValues)%2 == 0 {
		for i := 0; i < len(keyValues); i += 2 {
			key, ok := keyValues[i].(string)
			if !ok {
				continue
			}
			value := keyValues[i+1]
			fields = append(fields, zap.Any(key, value))
		}
	}

	log.Error("数据库操作失败", fields...)
}
