package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// CheckPermission 权限检查中间件
func CheckPermission(permissionCode string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取用户ID
		userID, exists := c.Get("userID")
		if !exists {
			c.JSON(401, gin.H{
				"code":    401,
				"message": "Unauthorized",
			})
			c.Abort()
			return
		}

		// 获取数据库连接
		db, exists := c.MustGet("db").(*gorm.DB)
		if !exists {
			logger.Error("Database connection not found in context", nil)
			c.JSON(500, gin.H{
				"code":    500,
				"message": "Internal server error",
			})
			c.Abort()
			return
		}

		// 查询用户权限
		var permissions []entity.Permission
		err := db.Model(&entity.User{}).
			Where("users.id = ?", userID).
			Joins("JOIN user_roles ON users.id = user_roles.user_id").
			Joins("JOIN roles ON user_roles.role_id = roles.id").
			Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
			Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
			Where("permissions.code = ? AND permissions.status = 1", permissionCode).
			Select("permissions.*").
			Find(&permissions).Error

		if err != nil {
			logger.Error("Failed to check permission", err)
			c.JSON(500, gin.H{
				"code":    500,
				"message": "Internal server error",
			})
			c.Abort()
			return
		}

		// 检查是否有权限
		if len(permissions) == 0 {
			c.JSON(403, gin.H{
				"code":    403,
				"message": "Permission denied",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
