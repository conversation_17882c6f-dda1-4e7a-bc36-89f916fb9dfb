package handler

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// StockMovementHandler 出入库记录处理器
type StockMovementHandler struct {
	db *gorm.DB
}

// NewStockMovementHandler 创建出入库记录处理器
func NewStockMovementHandler(db *gorm.DB) *StockMovementHandler {
	return &StockMovementHandler{db: db}
}

// StockMovementResponse 出入库记录响应
type StockMovementResponse struct {
	ID                     uint                `json:"id"`
	ProductID              uint                `json:"productId"`
	Product                ProductInfo         `json:"product"`
	SourceWarehouseID      uint                `json:"sourceWarehouseId"`
	SourceWarehouse        WarehouseInfo       `json:"sourceWarehouse"`
	DestinationWarehouseID *uint               `json:"destinationWarehouseId,omitempty"`
	DestinationWarehouse   *WarehouseInfo      `json:"destinationWarehouse,omitempty"`
	Quantity               float64             `json:"quantity"`
	UnitPrice              float64             `json:"unitPrice"`
	MovementType           entity.MovementType `json:"movementType"`
	MovementTypeText       string              `json:"movementTypeText"`
	Reason                 string              `json:"reason"`
	Reference              string              `json:"reference"`
	Remarks                string              `json:"remarks"`
	BatchNumber            string              `json:"batchNumber"`
	MovementDate           string              `json:"movementDate"`
	CustomerID             *uint               `json:"customerId,omitempty"`
	Customer               *CustomerInfo       `json:"customer,omitempty"`
	Company                string              `json:"company"`
	CreatedAt              string              `json:"createdAt"`
	UpdatedAt              string              `json:"updatedAt"`
}

// StockMovementListResponse 出入库记录列表响应
type StockMovementListResponse struct {
	Total int64                   `json:"total"`
	Data  []StockMovementResponse `json:"data"`
}

// CreateStockMovementRequest 创建出入库记录请求
type CreateStockMovementRequest struct {
	ProductID              uint                `json:"productId" binding:"required"`
	SourceWarehouseID      uint                `json:"sourceWarehouseId" binding:"required"`
	DestinationWarehouseID *uint               `json:"destinationWarehouseId"`
	Quantity               float64             `json:"quantity" binding:"required,gt=0"`
	UnitPrice              float64             `json:"unitPrice"`
	MovementType           entity.MovementType `json:"movementType" binding:"required"`
	Reason                 string              `json:"reason"`
	Reference              string              `json:"reference"`
	Remarks                string              `json:"remarks"`
	BatchNumber            string              `json:"batchNumber"`
	MovementDate           string              `json:"movementDate" binding:"required"`
	CustomerID             *uint               `json:"customerId"`
	Company                string              `json:"company"`
}

// GetMovementTypeText 获取出入库类型文本
func GetMovementTypeText(movementType entity.MovementType) string {
	switch movementType {
	case entity.StockIn:
		return "入库"
	case entity.StockOut:
		return "出库"
	case entity.Transfer:
		return "调拨"
	case entity.Adjustment:
		return "调整"
	default:
		return "未知类型"
	}
}

// GetStockMovements 获取出入库记录列表
// @Summary 获取出入库记录列表
// @Description 获取出入库记录列表，支持分页、搜索和过滤
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param productId query int false "产品ID"
// @Param warehouseId query int false "仓库ID"
// @Param movementType query int false "出入库类型"
// @Param startDate query string false "开始日期，格式：YYYY-MM-DD"
// @Param endDate query string false "结束日期，格式：YYYY-MM-DD"
// @Success 200 {object} StockMovementListResponse "出入库记录列表"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /stock-movements [get]
func (h *StockMovementHandler) GetStockMovements(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	productIDStr := c.Query("productId")
	warehouseIDStr := c.Query("warehouseId")
	movementTypeStr := c.Query("movementType")
	startDate := c.Query("startDate")
	endDate := c.Query("endDate")

	// 构建查询
	db := h.db.Model(&entity.StockMovement{})

	// 添加过滤条件
	if productIDStr != "" {
		productID, _ := strconv.Atoi(productIDStr)
		db = db.Where("product_id = ?", productID)
	}

	if warehouseIDStr != "" {
		warehouseID, _ := strconv.Atoi(warehouseIDStr)
		db = db.Where("source_warehouse_id = ? OR destination_warehouse_id = ?", warehouseID, warehouseID)
	}

	if movementTypeStr != "" {
		movementType, _ := strconv.Atoi(movementTypeStr)
		db = db.Where("movement_type = ?", movementType)
	}

	// 添加日期范围过滤
	if startDate != "" {
		db = db.Where("movement_date >= ?", startDate+" 00:00:00")
	}

	if endDate != "" {
		db = db.Where("movement_date <= ?", endDate+" 23:59:59")
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count stock movements", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get stock movements",
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var stockMovements []entity.StockMovement
	if err := db.Preload("Product").Preload("SourceWarehouse").
		Preload("DestinationWarehouse").Preload("Customer").
		Order("movement_date DESC").
		Offset(offset).Limit(pageSize).Find(&stockMovements).Error; err != nil {
		logger.Error("Failed to get stock movements", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get stock movements",
		})
		return
	}

	// 构建响应
	var response []StockMovementResponse
	for _, movement := range stockMovements {
		// 构建目标仓库信息（如果有）
		var destinationWarehouse *WarehouseInfo
		if movement.DestinationWarehouseID != nil && movement.DestinationWarehouse.ID > 0 {
			destinationWarehouse = &WarehouseInfo{
				ID:   movement.DestinationWarehouse.ID,
				Code: movement.DestinationWarehouse.Code,
				Name: movement.DestinationWarehouse.Name,
			}
		}

		// 构建客户信息（如果有）
		var customer *CustomerInfo
		if movement.CustomerID != nil && movement.Customer != nil && movement.Customer.ID > 0 {
			customer = &CustomerInfo{
				ID:            movement.Customer.ID,
				Code:          movement.Customer.Code,
				Name:          movement.Customer.Name,
				ContactPerson: movement.Customer.ContactPerson,
				ContactPhone:  movement.Customer.ContactPhone,
			}
		}

		// 构建出入库记录响应
		movementResponse := StockMovementResponse{
			ID:        movement.ID,
			ProductID: movement.ProductID,
			Product: ProductInfo{
				ID:    movement.Product.ID,
				Code:  movement.Product.Code,
				Name:  movement.Product.Name,
				Unit:  movement.Product.Unit,
				Price: movement.Product.Price,
			},
			SourceWarehouseID: movement.SourceWarehouseID,
			SourceWarehouse: WarehouseInfo{
				ID:   movement.SourceWarehouse.ID,
				Code: movement.SourceWarehouse.Code,
				Name: movement.SourceWarehouse.Name,
			},
			DestinationWarehouseID: movement.DestinationWarehouseID,
			DestinationWarehouse:   destinationWarehouse,
			Quantity:               movement.Quantity,
			UnitPrice:              movement.UnitPrice,
			MovementType:           movement.MovementType,
			MovementTypeText:       GetMovementTypeText(movement.MovementType),
			Reason:                 movement.Reason,
			Reference:              movement.Reference,
			Remarks:                movement.Remarks,
			BatchNumber:            movement.BatchNumber,
			MovementDate:           movement.MovementDate.Format(time.RFC3339),
			CustomerID:             movement.CustomerID,
			Customer:               customer,
			Company:                movement.Company,
			CreatedAt:              movement.CreatedAt.Format(time.RFC3339),
			UpdatedAt:              movement.UpdatedAt.Format(time.RFC3339),
		}

		response = append(response, movementResponse)
	}

	c.JSON(http.StatusOK, StockMovementListResponse{
		Total: total,
		Data:  response,
	})
}

// GetStockMovement 获取出入库记录详情
// @Summary 获取出入库记录详情
// @Description 根据ID获取出入库记录详情
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param id path int true "出入库记录ID"
// @Success 200 {object} StockMovementResponse "出入库记录详情"
// @Failure 404 {object} ErrorResponse "记录不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /stock-movements/{id} [get]
func (h *StockMovementHandler) GetStockMovement(c *gin.Context) {
	// 获取记录ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid stock movement ID",
		})
		return
	}

	// 查询记录
	var movement entity.StockMovement
	if err := h.db.Preload("Product").Preload("SourceWarehouse").
		Preload("DestinationWarehouse").Preload("Customer").
		First(&movement, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Stock movement not found",
			})
			return
		}
		logger.Error("Failed to get stock movement", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get stock movement",
		})
		return
	}

	// 构建目标仓库信息（如果有）
	var destinationWarehouse *WarehouseInfo
	if movement.DestinationWarehouseID != nil && movement.DestinationWarehouse.ID > 0 {
		destinationWarehouse = &WarehouseInfo{
			ID:   movement.DestinationWarehouse.ID,
			Code: movement.DestinationWarehouse.Code,
			Name: movement.DestinationWarehouse.Name,
		}
	}

	// 构建客户信息（如果有）
	var customer *CustomerInfo
	if movement.CustomerID != nil && movement.Customer != nil && movement.Customer.ID > 0 {
		customer = &CustomerInfo{
			ID:            movement.Customer.ID,
			Code:          movement.Customer.Code,
			Name:          movement.Customer.Name,
			ContactPerson: movement.Customer.ContactPerson,
			ContactPhone:  movement.Customer.ContactPhone,
		}
	}

	// 构建响应
	response := StockMovementResponse{
		ID:        movement.ID,
		ProductID: movement.ProductID,
		Product: ProductInfo{
			ID:    movement.Product.ID,
			Code:  movement.Product.Code,
			Name:  movement.Product.Name,
			Unit:  movement.Product.Unit,
			Price: movement.Product.Price,
		},
		SourceWarehouseID: movement.SourceWarehouseID,
		SourceWarehouse: WarehouseInfo{
			ID:   movement.SourceWarehouse.ID,
			Code: movement.SourceWarehouse.Code,
			Name: movement.SourceWarehouse.Name,
		},
		DestinationWarehouseID: movement.DestinationWarehouseID,
		DestinationWarehouse:   destinationWarehouse,
		Quantity:               movement.Quantity,
		UnitPrice:              movement.UnitPrice,
		MovementType:           movement.MovementType,
		MovementTypeText:       GetMovementTypeText(movement.MovementType),
		Reason:                 movement.Reason,
		Reference:              movement.Reference,
		Remarks:                movement.Remarks,
		BatchNumber:            movement.BatchNumber,
		MovementDate:           movement.MovementDate.Format(time.RFC3339),
		CustomerID:             movement.CustomerID,
		Customer:               customer,
		Company:                movement.Company,
		CreatedAt:              movement.CreatedAt.Format(time.RFC3339),
		UpdatedAt:              movement.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// CreateStockMovement 创建出入库记录
// @Summary 创建出入库记录
// @Description 创建新的出入库记录并更新库存
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param request body CreateStockMovementRequest true "出入库记录信息"
// @Success 201 {object} StockMovementResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /stock-movements [post]
func (h *StockMovementHandler) CreateStockMovement(c *gin.Context) {
	// 获取请求体原始数据并记录
	requestBody, _ := c.GetRawData()
	logger.Info("创建库存移动请求体: " + string(requestBody))

	// 重新设置请求体，因为GetRawData会消耗请求体
	c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))

	var req CreateStockMovementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("请求参数绑定失败", err)
		logger.Info("请求参数: " + string(requestBody))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 记录解析后的请求参数
	logger.Info(fmt.Sprintf("解析后的请求参数: ProductID=%d, SourceWarehouseID=%d, Quantity=%.2f, MovementType=%d",
		req.ProductID, req.SourceWarehouseID, req.Quantity, req.MovementType))

	// 解析移动日期
	movementDate, err := time.Parse("2006-01-02", req.MovementDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid movement date format. Use YYYY-MM-DD",
		})
		return
	}

	// 验证产品是否存在
	var product entity.Product
	if err := h.db.First(&product, req.ProductID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Product not found",
			})
			return
		}
		logger.Error("Failed to get product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create stock movement",
		})
		return
	}

	// 验证源仓库是否存在
	var sourceWarehouse entity.Warehouse
	if err := h.db.First(&sourceWarehouse, req.SourceWarehouseID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Source warehouse not found",
			})
			return
		}
		logger.Error("Failed to get source warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create stock movement",
		})
		return
	}

	// 验证目标仓库是否存在（如果有）
	if req.DestinationWarehouseID != nil {
		var destinationWarehouse entity.Warehouse
		if err := h.db.First(&destinationWarehouse, *req.DestinationWarehouseID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "Destination warehouse not found",
				})
				return
			}
			logger.Error("Failed to get destination warehouse", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to create stock movement",
			})
			return
		}
	}

	// 验证客户是否存在（如果有）
	if req.CustomerID != nil {
		var customer entity.Customer
		if err := h.db.First(&customer, *req.CustomerID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "Customer not found",
				})
				return
			}
			logger.Error("Failed to get customer", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to create stock movement",
			})
			return
		}
	}

	// 开始事务
	tx := h.db.Begin()
	if tx.Error != nil {
		logger.Error("开始事务失败", tx.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create stock movement: " + tx.Error.Error(),
		})
		return
	}

	logger.Info("开始事务成功")

	// 创建出入库记录
	movement := entity.StockMovement{
		ProductID:              req.ProductID,
		SourceWarehouseID:      req.SourceWarehouseID,
		DestinationWarehouseID: req.DestinationWarehouseID,
		Quantity:               req.Quantity,
		UnitPrice:              req.UnitPrice,
		MovementType:           req.MovementType,
		Reason:                 req.Reason,
		Reference:              req.Reference,
		Remarks:                req.Remarks,
		BatchNumber:            req.BatchNumber,
		MovementDate:           movementDate,
		CustomerID:             req.CustomerID,
		Company:                req.Company,
	}

	// 不再需要设置CreatedBy字段

	// 保存出入库记录
	if err := tx.Create(&movement).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to create stock movement", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create stock movement",
		})
		return
	}

	// 更新库存
	switch req.MovementType {
	case entity.StockIn:
		// 入库：增加源仓库库存
		var inventory entity.Inventory
		result := tx.Where("product_id = ? AND warehouse_id = ?", req.ProductID, req.SourceWarehouseID).First(&inventory)
		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// 如果库存记录不存在，创建新记录
				inventory = entity.Inventory{
					ProductID:   req.ProductID,
					WarehouseID: req.SourceWarehouseID,
					Quantity:    req.Quantity,
					// 设置时间字段为NULL值
					ExpiryDate:    nil,
					LastCheckDate: nil,
					// 不再需要设置BaseEntity
				}

				// 记录详细的库存创建信息
				logger.Info(fmt.Sprintf("创建新库存记录: ProductID=%d, WarehouseID=%d, Quantity=%.2f",
					req.ProductID, req.SourceWarehouseID, req.Quantity))
				if err := tx.Create(&inventory).Error; err != nil {
					tx.Rollback()
					logger.Error("Failed to create inventory", err)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update inventory",
					})
					return
				}
			} else {
				tx.Rollback()
				logger.Error("Failed to get inventory", result.Error)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update inventory",
				})
				return
			}
		} else {
			// 更新现有库存
			if err := tx.Model(&inventory).Update("quantity", inventory.Quantity+req.Quantity).Error; err != nil {
				tx.Rollback()
				logger.Error("Failed to update inventory", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update inventory",
				})
				return
			}
		}

	case entity.StockOut:
		// 出库：减少源仓库库存
		var inventory entity.Inventory
		logger.Info(fmt.Sprintf("开始出库操作: ProductID=%d, SourceWarehouseID=%d, Quantity=%.2f",
			req.ProductID, req.SourceWarehouseID, req.Quantity))

		result := tx.Where("product_id = ? AND warehouse_id = ?", req.ProductID, req.SourceWarehouseID).First(&inventory)
		if result.Error != nil {
			tx.Rollback()
			if result.Error == gorm.ErrRecordNotFound {
				logger.Error(fmt.Sprintf("未找到库存记录: ProductID=%d, SourceWarehouseID=%d",
					req.ProductID, req.SourceWarehouseID), result.Error)
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "未找到该产品在源仓库的库存记录",
				})
			} else {
				logger.Error(fmt.Sprintf("获取库存记录失败: ProductID=%d, SourceWarehouseID=%d",
					req.ProductID, req.SourceWarehouseID), result.Error)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "更新库存失败: " + result.Error.Error(),
				})
			}
			return
		}

		// 检查库存是否足够
		logger.Info(fmt.Sprintf("检查库存: 当前库存=%.2f, 出库数量=%.2f", inventory.Quantity, req.Quantity))
		if inventory.Quantity < req.Quantity {
			tx.Rollback()
			logger.Error(fmt.Sprintf("库存不足: 当前库存=%.2f, 出库数量=%.2f",
				inventory.Quantity, req.Quantity), nil)
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": fmt.Sprintf("库存不足: 当前库存%.2f, 需要出库%.2f", inventory.Quantity, req.Quantity),
			})
			return
		}

		// 更新库存
		newQuantity := inventory.Quantity - req.Quantity
		logger.Info(fmt.Sprintf("更新库存: 原库存=%.2f, 出库数量=%.2f, 新库存=%.2f",
			inventory.Quantity, req.Quantity, newQuantity))

		if err := tx.Model(&inventory).Update("quantity", newQuantity).Error; err != nil {
			tx.Rollback()
			logger.Error(fmt.Sprintf("更新库存失败: ProductID=%d, SourceWarehouseID=%d",
				req.ProductID, req.SourceWarehouseID), err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新库存失败: " + err.Error(),
			})
			return
		}

		logger.Info(fmt.Sprintf("出库操作成功: ProductID=%d, SourceWarehouseID=%d, Quantity=%.2f, NewQuantity=%.2f",
			req.ProductID, req.SourceWarehouseID, req.Quantity, newQuantity))

	case entity.Transfer:
		// 调拨：减少源仓库库存，增加目标仓库库存
		if req.DestinationWarehouseID == nil {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Destination warehouse is required for transfer",
			})
			return
		}

		// 检查源仓库库存
		var sourceInventory entity.Inventory
		result := tx.Where("product_id = ? AND warehouse_id = ?", req.ProductID, req.SourceWarehouseID).First(&sourceInventory)
		if result.Error != nil {
			tx.Rollback()
			if result.Error == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "No inventory found for this product in the source warehouse",
				})
			} else {
				logger.Error("Failed to get source inventory", result.Error)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update inventory",
				})
			}
			return
		}

		// 检查库存是否足够
		if sourceInventory.Quantity < req.Quantity {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Insufficient inventory in source warehouse",
			})
			return
		}

		// 更新源仓库库存
		if err := tx.Model(&sourceInventory).Update("quantity", sourceInventory.Quantity-req.Quantity).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to update source inventory", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to update inventory",
			})
			return
		}

		// 更新目标仓库库存
		var destInventory entity.Inventory
		result = tx.Where("product_id = ? AND warehouse_id = ?", req.ProductID, *req.DestinationWarehouseID).First(&destInventory)
		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// 如果目标仓库没有该产品的库存记录，创建新记录
				destInventory = entity.Inventory{
					ProductID:   req.ProductID,
					WarehouseID: *req.DestinationWarehouseID,
					Quantity:    req.Quantity,
					// 设置时间字段为NULL值
					ExpiryDate:    nil,
					LastCheckDate: nil,
					// 不再需要设置BaseEntity
				}

				// 记录详细的目标库存创建信息
				logger.Info(fmt.Sprintf("创建目标仓库新库存记录: ProductID=%d, WarehouseID=%d, Quantity=%.2f",
					req.ProductID, *req.DestinationWarehouseID, req.Quantity))
				if err := tx.Create(&destInventory).Error; err != nil {
					tx.Rollback()
					logger.Error("Failed to create destination inventory", err)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update inventory",
					})
					return
				}
			} else {
				tx.Rollback()
				logger.Error("Failed to get destination inventory", result.Error)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update inventory",
				})
				return
			}
		} else {
			// 更新现有库存
			if err := tx.Model(&destInventory).Update("quantity", destInventory.Quantity+req.Quantity).Error; err != nil {
				tx.Rollback()
				logger.Error("Failed to update destination inventory", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update inventory",
				})
				return
			}
		}

	case entity.Adjustment:
		// 调整：直接设置源仓库库存
		var inventory entity.Inventory
		result := tx.Where("product_id = ? AND warehouse_id = ?", req.ProductID, req.SourceWarehouseID).First(&inventory)
		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// 如果库存记录不存在，创建新记录
				inventory = entity.Inventory{
					ProductID:   req.ProductID,
					WarehouseID: req.SourceWarehouseID,
					Quantity:    req.Quantity,
					// 设置时间字段为NULL值
					ExpiryDate:    nil,
					LastCheckDate: nil,
					// 不再需要设置BaseEntity
				}

				// 记录详细的调整库存创建信息
				logger.Info(fmt.Sprintf("调整操作创建新库存记录: ProductID=%d, WarehouseID=%d, Quantity=%.2f",
					req.ProductID, req.SourceWarehouseID, req.Quantity))
				if err := tx.Create(&inventory).Error; err != nil {
					tx.Rollback()
					logger.Error("Failed to create inventory", err)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update inventory",
					})
					return
				}
			} else {
				tx.Rollback()
				logger.Error("Failed to get inventory", result.Error)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update inventory",
				})
				return
			}
		} else {
			// 更新现有库存
			if err := tx.Model(&inventory).Update("quantity", req.Quantity).Error; err != nil {
				tx.Rollback()
				logger.Error("Failed to update inventory", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update inventory",
				})
				return
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create stock movement",
		})
		return
	}

	// 重新查询完整的出入库记录信息
	var createdMovement entity.StockMovement
	if err := h.db.Preload("Product").Preload("SourceWarehouse").
		Preload("DestinationWarehouse").Preload("Customer").
		First(&createdMovement, movement.ID).Error; err != nil {
		logger.Error("Failed to get created stock movement", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Stock movement created but failed to retrieve details",
		})
		return
	}

	// 构建目标仓库信息（如果有）
	var destinationWarehouse *WarehouseInfo
	if createdMovement.DestinationWarehouseID != nil && createdMovement.DestinationWarehouse.ID > 0 {
		destinationWarehouse = &WarehouseInfo{
			ID:   createdMovement.DestinationWarehouse.ID,
			Code: createdMovement.DestinationWarehouse.Code,
			Name: createdMovement.DestinationWarehouse.Name,
		}
	}

	// 构建客户信息（如果有）
	var customer *CustomerInfo
	if createdMovement.CustomerID != nil && createdMovement.Customer != nil && createdMovement.Customer.ID > 0 {
		customer = &CustomerInfo{
			ID:            createdMovement.Customer.ID,
			Code:          createdMovement.Customer.Code,
			Name:          createdMovement.Customer.Name,
			ContactPerson: createdMovement.Customer.ContactPerson,
			ContactPhone:  createdMovement.Customer.ContactPhone,
		}
	}

	// 构建响应
	response := StockMovementResponse{
		ID:        createdMovement.ID,
		ProductID: createdMovement.ProductID,
		Product: ProductInfo{
			ID:    createdMovement.Product.ID,
			Code:  createdMovement.Product.Code,
			Name:  createdMovement.Product.Name,
			Unit:  createdMovement.Product.Unit,
			Price: createdMovement.Product.Price,
		},
		SourceWarehouseID: createdMovement.SourceWarehouseID,
		SourceWarehouse: WarehouseInfo{
			ID:   createdMovement.SourceWarehouse.ID,
			Code: createdMovement.SourceWarehouse.Code,
			Name: createdMovement.SourceWarehouse.Name,
		},
		DestinationWarehouseID: createdMovement.DestinationWarehouseID,
		DestinationWarehouse:   destinationWarehouse,
		Quantity:               createdMovement.Quantity,
		UnitPrice:              createdMovement.UnitPrice,
		MovementType:           createdMovement.MovementType,
		MovementTypeText:       GetMovementTypeText(createdMovement.MovementType),
		Reason:                 createdMovement.Reason,
		Reference:              createdMovement.Reference,
		Remarks:                createdMovement.Remarks,
		BatchNumber:            createdMovement.BatchNumber,
		MovementDate:           createdMovement.MovementDate.Format(time.RFC3339),
		CustomerID:             createdMovement.CustomerID,
		Customer:               customer,
		CreatedAt:              createdMovement.CreatedAt.Format(time.RFC3339),
		UpdatedAt:              createdMovement.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

// ExportStockMovements 导出出入库记录
// @Summary 导出出入库记录
// @Description 导出出入库记录为CSV文件
// @Tags 库存管理
// @Accept json
// @Produce text/csv
// @Param productId query int false "产品ID"
// @Param warehouseId query int false "仓库ID"
// @Param movementType query int false "出入库类型"
// @Param startDate query string false "开始日期，格式：YYYY-MM-DD"
// @Param endDate query string false "结束日期，格式：YYYY-MM-DD"
// @Success 200 {file} file "导出的CSV文件"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /stock-movements/export [get]
func (h *StockMovementHandler) ExportStockMovements(c *gin.Context) {
	// 获取查询参数
	productIDStr := c.Query("productId")
	warehouseIDStr := c.Query("warehouseId")
	movementTypeStr := c.Query("movementType")
	startDate := c.Query("startDate")
	endDate := c.Query("endDate")

	// 构建查询
	db := h.db.Model(&entity.StockMovement{})

	// 添加过滤条件
	if productIDStr != "" {
		productID, _ := strconv.Atoi(productIDStr)
		db = db.Where("product_id = ?", productID)
	}

	if warehouseIDStr != "" {
		warehouseID, _ := strconv.Atoi(warehouseIDStr)
		db = db.Where("source_warehouse_id = ? OR destination_warehouse_id = ?", warehouseID, warehouseID)
	}

	if movementTypeStr != "" {
		movementType, _ := strconv.Atoi(movementTypeStr)
		db = db.Where("movement_type = ?", movementType)
	}

	// 添加日期范围过滤
	if startDate != "" {
		db = db.Where("movement_date >= ?", startDate+" 00:00:00")
	}

	if endDate != "" {
		db = db.Where("movement_date <= ?", endDate+" 23:59:59")
	}

	// 查询数据
	var stockMovements []entity.StockMovement
	if err := db.Preload("Product").Preload("SourceWarehouse").
		Preload("DestinationWarehouse").Preload("Customer").
		Order("movement_date DESC").
		Find(&stockMovements).Error; err != nil {
		logger.Error("Failed to get stock movements for export", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to export stock movements",
		})
		return
	}

	// 设置响应头
	fileName := "stock_movements_" + time.Now().Format("20060102_150405") + ".csv"
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", "attachment; filename=\""+fileName+"\"")
	c.Header("Access-Control-Expose-Headers", "Content-Disposition")

	// 记录响应头信息
	logger.Info(fmt.Sprintf("设置导出响应头: Content-Type=%s, Content-Disposition=%s",
		"text/csv; charset=utf-8", "attachment; filename=\""+fileName+"\""))

	// 创建CSV写入器
	writer := csv.NewWriter(c.Writer)
	defer writer.Flush()

	// 写入CSV头
	headers := []string{
		"ID", "类型", "产品编码", "产品名称", "源仓库", "目标仓库",
		"数量", "单位", "单价", "批次号", "日期", "原因", "备注",
	}
	if err := writer.Write(headers); err != nil {
		logger.Error("Failed to write CSV headers", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to export stock movements",
		})
		return
	}

	// 写入数据行
	for _, movement := range stockMovements {
		// 获取目标仓库名称（如果有）
		destWarehouseName := ""
		if movement.DestinationWarehouseID != nil && movement.DestinationWarehouse.ID > 0 {
			destWarehouseName = movement.DestinationWarehouse.Name
		}

		// 构建CSV行
		row := []string{
			strconv.FormatUint(uint64(movement.ID), 10),
			GetMovementTypeText(movement.MovementType),
			movement.Product.Code,
			movement.Product.Name,
			movement.SourceWarehouse.Name,
			destWarehouseName,
			strconv.FormatFloat(movement.Quantity, 'f', 2, 64),
			movement.Product.Unit,
			strconv.FormatFloat(movement.UnitPrice, 'f', 2, 64),
			movement.BatchNumber,
			movement.MovementDate.Format("2006-01-02"),
			movement.Reason,
			movement.Remarks,
		}

		if err := writer.Write(row); err != nil {
			logger.Error("Failed to write CSV row", err)
			continue
		}
	}
}
