package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// WarehouseHandler 仓库处理器
type WarehouseHandler struct {
	db *gorm.DB
}

// NewWarehouseHandler 创建仓库处理器
func NewWarehouseHandler(db *gorm.DB) *WarehouseHandler {
	return &WarehouseHandler{db: db}
}

// WarehouseResponse 仓库响应
type WarehouseResponse struct {
	ID            uint   `json:"id"`
	Code          string `json:"code"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	Address       string `json:"address"`
	ContactPerson string `json:"contactPerson"`
	ContactPhone  string `json:"contactPhone"`
	IsActive      bool   `json:"isActive"`
	CreatedAt     string `json:"createdAt"`
	UpdatedAt     string `json:"updatedAt"`
}

// WarehouseListResponse 仓库列表响应
type WarehouseListResponse struct {
	Total int64               `json:"total"`
	Data  []WarehouseResponse `json:"data"`
}

// CreateWarehouseRequest 创建仓库请求
type CreateWarehouseRequest struct {
	Code          string `json:"code" binding:"required"`
	Name          string `json:"name" binding:"required"`
	Description   string `json:"description"`
	Address       string `json:"address" binding:"required"`
	ContactPerson string `json:"contactPerson"`
	ContactPhone  string `json:"contactPhone"`
	IsActive      bool   `json:"isActive"`
}

// UpdateWarehouseRequest 更新仓库请求
type UpdateWarehouseRequest struct {
	Code          string `json:"code"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	Address       string `json:"address"`
	ContactPerson string `json:"contactPerson"`
	ContactPhone  string `json:"contactPhone"`
	IsActive      *bool  `json:"isActive"`
}

// GetWarehouses 获取仓库列表
// @Summary 获取仓库列表
// @Description 获取仓库列表，支持分页、搜索和过滤
// @Tags 仓库
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键字"
// @Param isActive query string false "是否启用，true或false"
// @Success 200 {object} WarehouseListResponse "仓库列表"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /warehouses [get]
func (h *WarehouseHandler) GetWarehouses(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	query := c.Query("query")
	isActiveStr := c.Query("isActive")

	// 构建查询
	db := h.db.Model(&entity.Warehouse{})

	// 添加过滤条件
	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ? OR address LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if isActiveStr != "" {
		isActive := isActiveStr == "true"
		db = db.Where("is_active = ?", isActive)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count warehouses", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get warehouses",
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var warehouses []entity.Warehouse
	if err := db.Order("created_at DESC").
		Offset(offset).Limit(pageSize).Find(&warehouses).Error; err != nil {
		logger.Error("Failed to get warehouses", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get warehouses",
		})
		return
	}

	// 构建响应
	var response []WarehouseResponse
	for _, warehouse := range warehouses {
		response = append(response, WarehouseResponse{
			ID:            warehouse.ID,
			Code:          warehouse.Code,
			Name:          warehouse.Name,
			Description:   warehouse.Description,
			Address:       warehouse.Address,
			ContactPerson: warehouse.ContactPerson,
			ContactPhone:  warehouse.ContactPhone,
			IsActive:      warehouse.IsActive,
			CreatedAt:     warehouse.CreatedAt.Format(time.RFC3339),
			UpdatedAt:     warehouse.UpdatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, WarehouseListResponse{
		Total: total,
		Data:  response,
	})
}

// GetWarehouse 获取仓库详情
// @Summary 获取仓库详情
// @Description 根据ID获取仓库详情
// @Tags 仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Success 200 {object} WarehouseResponse "仓库详情"
// @Failure 404 {object} ErrorResponse "仓库不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /warehouses/{id} [get]
func (h *WarehouseHandler) GetWarehouse(c *gin.Context) {
	// 获取仓库ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid warehouse ID",
		})
		return
	}

	// 查询仓库
	var warehouse entity.Warehouse
	if err := h.db.First(&warehouse, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Warehouse not found",
			})
			return
		}
		logger.Error("Failed to get warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get warehouse",
		})
		return
	}

	// 构建响应
	response := WarehouseResponse{
		ID:            warehouse.ID,
		Code:          warehouse.Code,
		Name:          warehouse.Name,
		Description:   warehouse.Description,
		Address:       warehouse.Address,
		ContactPerson: warehouse.ContactPerson,
		ContactPhone:  warehouse.ContactPhone,
		IsActive:      warehouse.IsActive,
		CreatedAt:     warehouse.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     warehouse.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// CreateWarehouse 创建仓库
// @Summary 创建仓库
// @Description 创建新仓库
// @Tags 仓库
// @Accept json
// @Produce json
// @Param request body CreateWarehouseRequest true "仓库信息"
// @Success 201 {object} WarehouseResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /warehouses [post]
func (h *WarehouseHandler) CreateWarehouse(c *gin.Context) {
	var req CreateWarehouseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 检查编码是否已存在
	var count int64
	if err := h.db.Model(&entity.Warehouse{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		logger.Error("Failed to check warehouse code", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create warehouse",
		})
		return
	}

	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Warehouse code already exists",
		})
		return
	}

	// 创建仓库
	warehouse := entity.Warehouse{
		Code:          req.Code,
		Name:          req.Name,
		Description:   req.Description,
		Address:       req.Address,
		ContactPerson: req.ContactPerson,
		ContactPhone:  req.ContactPhone,
		IsActive:      req.IsActive,
	}

	// 从上下文中获取用户ID（如果有）
	// if userID, exists := c.Get("userID"); exists {
	// 	warehouse.CreatedBy = userID.(uint)
	// }

	// 保存仓库
	if err := h.db.Create(&warehouse).Error; err != nil {
		logger.Error("Failed to create warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create warehouse",
		})
		return
	}

	// 构建响应
	response := WarehouseResponse{
		ID:            warehouse.ID,
		Code:          warehouse.Code,
		Name:          warehouse.Name,
		Description:   warehouse.Description,
		Address:       warehouse.Address,
		ContactPerson: warehouse.ContactPerson,
		ContactPhone:  warehouse.ContactPhone,
		IsActive:      warehouse.IsActive,
		CreatedAt:     warehouse.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     warehouse.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateWarehouse 更新仓库
// @Summary 更新仓库
// @Description 更新仓库信息
// @Tags 仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Param request body UpdateWarehouseRequest true "仓库信息"
// @Success 200 {object} WarehouseResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "仓库不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /warehouses/{id} [put]
func (h *WarehouseHandler) UpdateWarehouse(c *gin.Context) {
	// 获取仓库ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid warehouse ID",
		})
		return
	}

	var req UpdateWarehouseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 查询仓库是否存在
	var warehouse entity.Warehouse
	if err := h.db.First(&warehouse, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Warehouse not found",
			})
			return
		}
		logger.Error("Failed to get warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update warehouse",
		})
		return
	}

	// 如果更新了编码，检查是否与其他仓库冲突
	if req.Code != "" && req.Code != warehouse.Code {
		var count int64
		if err := h.db.Model(&entity.Warehouse{}).Where("code = ? AND id != ?", req.Code, id).Count(&count).Error; err != nil {
			logger.Error("Failed to check warehouse code", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to update warehouse",
			})
			return
		}

		if count > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Warehouse code already exists",
			})
			return
		}
		warehouse.Code = req.Code
	}

	// 更新仓库信息
	if req.Name != "" {
		warehouse.Name = req.Name
	}
	warehouse.Description = req.Description
	if req.Address != "" {
		warehouse.Address = req.Address
	}
	warehouse.ContactPerson = req.ContactPerson
	warehouse.ContactPhone = req.ContactPhone
	if req.IsActive != nil {
		warehouse.IsActive = *req.IsActive
	}

	// 从上下文中获取用户ID（如果有）
	// if userID, exists := c.Get("userID"); exists {
	// 	warehouse.UpdatedBy = userID.(uint)
	// }

	// 保存更新
	if err := h.db.Save(&warehouse).Error; err != nil {
		logger.Error("Failed to update warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update warehouse",
		})
		return
	}

	// 构建响应
	response := WarehouseResponse{
		ID:            warehouse.ID,
		Code:          warehouse.Code,
		Name:          warehouse.Name,
		Description:   warehouse.Description,
		Address:       warehouse.Address,
		ContactPerson: warehouse.ContactPerson,
		ContactPhone:  warehouse.ContactPhone,
		IsActive:      warehouse.IsActive,
		CreatedAt:     warehouse.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     warehouse.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// DeleteWarehouse 删除仓库
// @Summary 删除仓库
// @Description 根据ID删除仓库
// @Tags 仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Success 204 "删除成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "仓库不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /warehouses/{id} [delete]
func (h *WarehouseHandler) DeleteWarehouse(c *gin.Context) {
	// 获取仓库ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid warehouse ID",
		})
		return
	}

	// 查询仓库
	var warehouse entity.Warehouse
	if err := h.db.First(&warehouse, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Warehouse not found",
			})
			return
		}
		logger.Error("Failed to get warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete warehouse",
		})
		return
	}

	// 检查是否有关联的库存
	var inventoryCount int64
	if err := h.db.Model(&entity.Inventory{}).Where("warehouse_id = ?", id).Count(&inventoryCount).Error; err != nil {
		logger.Error("Failed to check related inventories", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete warehouse",
		})
		return
	}

	if inventoryCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Cannot delete warehouse with related inventories",
		})
		return
	}

	// 删除仓库
	if err := h.db.Delete(&warehouse).Error; err != nil {
		logger.Error("Failed to delete warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete warehouse",
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetWarehouseInventory 获取仓库库存
// @Summary 获取仓库库存
// @Description 获取指定仓库的库存列表
// @Tags 仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Success 200 {array} gin.H "仓库库存列表"
// @Failure 404 {object} ErrorResponse "仓库不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /warehouses/{id}/inventory [get]
func (h *WarehouseHandler) GetWarehouseInventory(c *gin.Context) {
	// 获取仓库ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid warehouse ID",
		})
		return
	}

	// 查询仓库是否存在
	var warehouse entity.Warehouse
	if err := h.db.First(&warehouse, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Warehouse not found",
			})
			return
		}
		logger.Error("Failed to get warehouse", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get warehouse inventory",
		})
		return
	}

	// 查询仓库库存
	var inventories []entity.Inventory
	if err := h.db.Where("warehouse_id = ?", id).Preload("Product").Find(&inventories).Error; err != nil {
		logger.Error("Failed to get warehouse inventory", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get warehouse inventory",
		})
		return
	}

	// 构建响应
	var response []gin.H
	for _, inventory := range inventories {
		response = append(response, gin.H{
			"id":          inventory.ID,
			"productId":   inventory.ProductID,
			"warehouseId": inventory.WarehouseID,
			"product": gin.H{
				"id":   inventory.Product.ID,
				"code": inventory.Product.Code,
				"name": inventory.Product.Name,
				"unit": inventory.Product.Unit,
			},
			"quantity":  inventory.Quantity,
			"location":  inventory.Location,
			"createdAt": inventory.CreatedAt.Format(time.RFC3339),
			"updatedAt": inventory.UpdatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, response)
}
