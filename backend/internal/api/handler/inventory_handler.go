package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// InventoryHandler 库存处理器
type InventoryHandler struct {
	db *gorm.DB
}

// NewInventoryHandler 创建库存处理器
func NewInventoryHandler(db *gorm.DB) *InventoryHandler {
	return &InventoryHandler{db: db}
}

// InventoryResponse 库存响应
type InventoryResponse struct {
	ID              uint          `json:"id"`
	ProductID       uint          `json:"productId"`
	WarehouseID     uint          `json:"warehouseId"`
	Product         ProductInfo   `json:"product"`
	Warehouse       WarehouseInfo `json:"warehouse"`
	Quantity        float64       `json:"quantity"`
	Location        string        `json:"location"`
	ReorderPoint    float64       `json:"reorderPoint"`
	BatchNumber     string        `json:"batchNumber"`
	ExpiryDate      string        `json:"expiryDate,omitempty"`
	LastCheckDate   string        `json:"lastCheckDate,omitempty"`
	MinimumQuantity float64       `json:"minimumQuantity"`
	MaximumQuantity float64       `json:"maximumQuantity"`
	Status          string        `json:"status"`
	CreatedAt       string        `json:"createdAt"`
	UpdatedAt       string        `json:"updatedAt"`
}

// ProductInfo 产品信息
type ProductInfo struct {
	ID    uint    `json:"id"`
	Code  string  `json:"code"`
	Name  string  `json:"name"`
	Unit  string  `json:"unit"`
	Price float64 `json:"price"`
}

// WarehouseInfo 仓库信息
type WarehouseInfo struct {
	ID   uint   `json:"id"`
	Code string `json:"code"`
	Name string `json:"name"`
}

// InventoryListResponse 库存列表响应
type InventoryListResponse struct {
	Total int64               `json:"total"`
	Data  []InventoryResponse `json:"data"`
}

// GetInventories 获取库存列表
// @Summary 获取库存列表
// @Description 获取库存列表，支持分页、搜索和过滤
// @Tags 库存
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param productId query int false "产品ID"
// @Param warehouseId query int false "仓库ID"
// @Param query query string false "搜索关键字"
// @Success 200 {object} InventoryListResponse "库存列表"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /inventories [get]
func (h *InventoryHandler) GetInventories(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	productIDStr := c.Query("productId")
	warehouseIDStr := c.Query("warehouseId")
	query := c.Query("query")

	// 构建查询
	db := h.db.Model(&entity.Inventory{})

	// 添加过滤条件
	if productIDStr != "" {
		productID, _ := strconv.Atoi(productIDStr)
		db = db.Where("product_id = ?", productID)
	}

	if warehouseIDStr != "" {
		warehouseID, _ := strconv.Atoi(warehouseIDStr)
		db = db.Where("warehouse_id = ?", warehouseID)
	}

	// 添加搜索条件（通过关联查询）
	if query != "" {
		db = db.Joins("JOIN products ON inventories.product_id = products.id").
			Joins("JOIN warehouses ON inventories.warehouse_id = warehouses.id").
			Where("products.code LIKE ? OR products.name LIKE ? OR warehouses.name LIKE ?",
				"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count inventories", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get inventories",
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var inventories []entity.Inventory
	if err := db.Preload("Product").Preload("Warehouse").
		Offset(offset).Limit(pageSize).Find(&inventories).Error; err != nil {
		logger.Error("Failed to get inventories", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get inventories",
		})
		return
	}

	// 构建响应
	var response []InventoryResponse
	for _, inventory := range inventories {
		expiryDate := ""
		if inventory.ExpiryDate != nil && !inventory.ExpiryDate.IsZero() {
			expiryDate = inventory.ExpiryDate.Format(time.RFC3339)
		}

		lastCheckDate := ""
		if inventory.LastCheckDate != nil && !inventory.LastCheckDate.IsZero() {
			lastCheckDate = inventory.LastCheckDate.Format(time.RFC3339)
		}

		response = append(response, InventoryResponse{
			ID:          inventory.ID,
			ProductID:   inventory.ProductID,
			WarehouseID: inventory.WarehouseID,
			Product: ProductInfo{
				ID:    inventory.Product.ID,
				Code:  inventory.Product.Code,
				Name:  inventory.Product.Name,
				Unit:  inventory.Product.Unit,
				Price: inventory.Product.Price,
			},
			Warehouse: WarehouseInfo{
				ID:   inventory.Warehouse.ID,
				Code: inventory.Warehouse.Code,
				Name: inventory.Warehouse.Name,
			},
			Quantity:        inventory.Quantity,
			Location:        inventory.Location,
			ReorderPoint:    inventory.ReorderPoint,
			BatchNumber:     inventory.BatchNumber,
			ExpiryDate:      expiryDate,
			LastCheckDate:   lastCheckDate,
			MinimumQuantity: inventory.MinimumQuantity,
			MaximumQuantity: inventory.MaximumQuantity,
			Status:          inventory.Status,
			CreatedAt:       inventory.CreatedAt.Format(time.RFC3339),
			UpdatedAt:       inventory.UpdatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, InventoryListResponse{
		Total: total,
		Data:  response,
	})
}

// GetInventory 获取库存详情
// @Summary 获取库存详情
// @Description 根据ID获取库存详情
// @Tags 库存
// @Accept json
// @Produce json
// @Param id path int true "库存ID"
// @Success 200 {object} InventoryResponse "库存详情"
// @Failure 404 {object} ErrorResponse "库存不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /inventories/{id} [get]
func (h *InventoryHandler) GetInventory(c *gin.Context) {
	// 获取库存ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid inventory ID",
		})
		return
	}

	// 查询库存
	var inventory entity.Inventory
	if err := h.db.Preload("Product").Preload("Warehouse").First(&inventory, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Inventory not found",
			})
			return
		}
		logger.Error("Failed to get inventory", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get inventory",
		})
		return
	}

	// 构建响应
	expiryDate := ""
	if inventory.ExpiryDate != nil && !inventory.ExpiryDate.IsZero() {
		expiryDate = inventory.ExpiryDate.Format(time.RFC3339)
	}

	lastCheckDate := ""
	if inventory.LastCheckDate != nil && !inventory.LastCheckDate.IsZero() {
		lastCheckDate = inventory.LastCheckDate.Format(time.RFC3339)
	}

	response := InventoryResponse{
		ID:          inventory.ID,
		ProductID:   inventory.ProductID,
		WarehouseID: inventory.WarehouseID,
		Product: ProductInfo{
			ID:    inventory.Product.ID,
			Code:  inventory.Product.Code,
			Name:  inventory.Product.Name,
			Unit:  inventory.Product.Unit,
			Price: inventory.Product.Price,
		},
		Warehouse: WarehouseInfo{
			ID:   inventory.Warehouse.ID,
			Code: inventory.Warehouse.Code,
			Name: inventory.Warehouse.Name,
		},
		Quantity:        inventory.Quantity,
		Location:        inventory.Location,
		ReorderPoint:    inventory.ReorderPoint,
		BatchNumber:     inventory.BatchNumber,
		ExpiryDate:      expiryDate,
		LastCheckDate:   lastCheckDate,
		MinimumQuantity: inventory.MinimumQuantity,
		MaximumQuantity: inventory.MaximumQuantity,
		Status:          inventory.Status,
		CreatedAt:       inventory.CreatedAt.Format(time.RFC3339),
		UpdatedAt:       inventory.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}
