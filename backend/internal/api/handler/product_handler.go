package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// ProductHandler 产品处理器
type ProductHandler struct {
	db *gorm.DB
}

// NewProductHandler 创建产品处理器
func NewProductHandler(db *gorm.DB) *ProductHandler {
	return &ProductHandler{db: db}
}

// ProductResponse 产品响应
type ProductResponse struct {
	ID            uint    `json:"id"`
	Code          string  `json:"code"`
	Name          string  `json:"name"`
	Description   string  `json:"description"`
	Specification string  `json:"specification"`
	Unit          string  `json:"unit"`
	MinStock      float64 `json:"minStock"`
	MaxStock      float64 `json:"maxStock"`
	IsActive      bool    `json:"isActive"`
	Barcode       string  `json:"barcode"`
	Category      string  `json:"category"`
	Price         float64 `json:"price"`
	CreatedAt     string  `json:"createdAt"`
	UpdatedAt     string  `json:"updatedAt"`
}

// ProductListResponse 产品列表响应
type ProductListResponse struct {
	Total int64             `json:"total"`
	Data  []ProductResponse `json:"data"`
}

// GetProducts 获取产品列表
// @Summary 获取产品列表
// @Description 获取产品列表，支持分页、搜索和过滤
// @Tags 产品
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键字"
// @Param isActive query string false "是否启用，true或false"
// @Success 200 {object} ProductListResponse "产品列表"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /products [get]
func (h *ProductHandler) GetProducts(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	query := c.Query("query")
	isActiveStr := c.Query("isActive")

	// 构建查询
	db := h.db.Model(&entity.Product{})

	// 添加搜索条件
	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ? OR category LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 添加过滤条件
	if isActiveStr != "" {
		isActive := isActiveStr == "true"
		db = db.Where("is_active = ?", isActive)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count products", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get products",
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var products []entity.Product
	if err := db.Offset(offset).Limit(pageSize).Find(&products).Error; err != nil {
		logger.Error("Failed to get products", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get products",
		})
		return
	}

	// 构建响应
	var response []ProductResponse
	for _, product := range products {
		response = append(response, ProductResponse{
			ID:            product.ID,
			Code:          product.Code,
			Name:          product.Name,
			Description:   product.Description,
			Specification: product.Specification,
			Unit:          product.Unit,
			MinStock:      product.MinStock,
			MaxStock:      product.MaxStock,
			IsActive:      product.IsActive,
			Barcode:       product.Barcode,
			Category:      product.Category,
			Price:         product.Price,
			CreatedAt:     product.CreatedAt.Format(time.RFC3339),
			UpdatedAt:     product.UpdatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, ProductListResponse{
		Total: total,
		Data:  response,
	})
}

// GetProduct 获取产品详情
// @Summary 获取产品详情
// @Description 根据ID获取产品详情
// @Tags 产品
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Success 200 {object} ProductResponse "产品详情"
// @Failure 404 {object} ErrorResponse "产品不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /products/{id} [get]
func (h *ProductHandler) GetProduct(c *gin.Context) {
	// 获取产品ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid product ID",
		})
		return
	}

	// 查询产品
	var product entity.Product
	if err := h.db.First(&product, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Product not found",
			})
			return
		}
		logger.Error("Failed to get product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get product",
		})
		return
	}

	// 构建响应
	response := ProductResponse{
		ID:            product.ID,
		Code:          product.Code,
		Name:          product.Name,
		Description:   product.Description,
		Specification: product.Specification,
		Unit:          product.Unit,
		MinStock:      product.MinStock,
		MaxStock:      product.MaxStock,
		IsActive:      product.IsActive,
		Barcode:       product.Barcode,
		Category:      product.Category,
		Price:         product.Price,
		CreatedAt:     product.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     product.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// CreateProductRequest 创建产品请求
type CreateProductRequest struct {
	Code          string  `json:"code" binding:"required"`
	Name          string  `json:"name" binding:"required"`
	Description   string  `json:"description"`
	Specification string  `json:"specification"`
	Unit          string  `json:"unit"`
	MinStock      float64 `json:"minStock"`
	MaxStock      float64 `json:"maxStock"`
	IsActive      bool    `json:"isActive"`
	Barcode       string  `json:"barcode"`
	Category      string  `json:"category"`
	Price         float64 `json:"price"`
}

// UpdateProductRequest 更新产品请求
type UpdateProductRequest struct {
	Name          string   `json:"name"`
	Description   string   `json:"description"`
	Specification string   `json:"specification"`
	Unit          string   `json:"unit"`
	MinStock      *float64 `json:"minStock"`
	MaxStock      *float64 `json:"maxStock"`
	IsActive      *bool    `json:"isActive"`
	Barcode       string   `json:"barcode"`
	Category      string   `json:"category"`
	Price         *float64 `json:"price"`
}

// CreateProduct 创建产品
// @Summary 创建产品
// @Description 创建新产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param request body CreateProductRequest true "产品信息"
// @Success 201 {object} ProductResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /products [post]
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	var req CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 检查产品编码是否已存在
	var count int64
	if err := h.db.Model(&entity.Product{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		logger.Error("Failed to check product code", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create product",
		})
		return
	}

	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Product code already exists",
		})
		return
	}

	// 创建产品
	product := entity.Product{
		Code:          req.Code,
		Name:          req.Name,
		Description:   req.Description,
		Specification: req.Specification,
		Unit:          req.Unit,
		MinStock:      req.MinStock,
		MaxStock:      req.MaxStock,
		IsActive:      req.IsActive,
		Barcode:       req.Barcode,
		Category:      req.Category,
		Price:         req.Price,
	}

	// 不再需要设置CreatedBy字段

	// 保存产品
	if err := h.db.Create(&product).Error; err != nil {
		logger.Error("Failed to create product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create product",
		})
		return
	}

	// 构建响应
	response := ProductResponse{
		ID:            product.ID,
		Code:          product.Code,
		Name:          product.Name,
		Description:   product.Description,
		Specification: product.Specification,
		Unit:          product.Unit,
		MinStock:      product.MinStock,
		MaxStock:      product.MaxStock,
		IsActive:      product.IsActive,
		Barcode:       product.Barcode,
		Category:      product.Category,
		Price:         product.Price,
		CreatedAt:     product.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     product.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateProduct 更新产品
// @Summary 更新产品
// @Description 更新产品信息
// @Tags 产品
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Param request body UpdateProductRequest true "产品信息"
// @Success 200 {object} ProductResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "产品不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /products/{id} [put]
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	// 获取产品ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid product ID",
		})
		return
	}

	var req UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 查询产品
	var product entity.Product
	if err := h.db.First(&product, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Product not found",
			})
			return
		}
		logger.Error("Failed to get product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update product",
		})
		return
	}

	// 更新产品信息
	updates := map[string]interface{}{}

	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Specification != "" {
		updates["specification"] = req.Specification
	}
	if req.Unit != "" {
		updates["unit"] = req.Unit
	}
	if req.MinStock != nil {
		updates["min_stock"] = *req.MinStock
	}
	if req.MaxStock != nil {
		updates["max_stock"] = *req.MaxStock
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	if req.Barcode != "" {
		updates["barcode"] = req.Barcode
	}
	if req.Category != "" {
		updates["category"] = req.Category
	}
	if req.Price != nil {
		updates["price"] = *req.Price
	}

	// 不再需要设置updated_by字段

	// 保存更新
	if err := h.db.Model(&product).Updates(updates).Error; err != nil {
		logger.Error("Failed to update product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update product",
		})
		return
	}

	// 重新查询产品
	if err := h.db.First(&product, id).Error; err != nil {
		logger.Error("Failed to get updated product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Product updated but failed to retrieve details",
		})
		return
	}

	// 构建响应
	response := ProductResponse{
		ID:            product.ID,
		Code:          product.Code,
		Name:          product.Name,
		Description:   product.Description,
		Specification: product.Specification,
		Unit:          product.Unit,
		MinStock:      product.MinStock,
		MaxStock:      product.MaxStock,
		IsActive:      product.IsActive,
		Barcode:       product.Barcode,
		Category:      product.Category,
		Price:         product.Price,
		CreatedAt:     product.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     product.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// DeleteProduct 删除产品
// @Summary 删除产品
// @Description 删除产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "产品不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /products/{id} [delete]
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	// 获取产品ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid product ID",
		})
		return
	}

	// 查询产品
	var product entity.Product
	if err := h.db.First(&product, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Product not found",
			})
			return
		}
		logger.Error("Failed to get product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete product",
		})
		return
	}

	// 检查是否有关联的库存
	var inventoryCount int64
	if err := h.db.Model(&entity.Inventory{}).Where("product_id = ?", id).Count(&inventoryCount).Error; err != nil {
		logger.Error("Failed to check related inventories", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete product",
		})
		return
	}

	if inventoryCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Cannot delete product with related inventories",
		})
		return
	}

	// 检查是否有关联的订单项
	var orderItemCount int64
	if err := h.db.Model(&entity.OrderItem{}).Where("product_id = ?", id).Count(&orderItemCount).Error; err != nil {
		logger.Error("Failed to check related order items", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete product",
		})
		return
	}

	if orderItemCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Cannot delete product with related order items",
		})
		return
	}

	// 删除产品（软删除）
	if err := h.db.Delete(&product).Error; err != nil {
		logger.Error("Failed to delete product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete product",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Product deleted successfully",
	})
}

// GetProductInventory 获取产品库存
// @Summary 获取产品库存
// @Description 根据产品ID获取所有仓库中的库存情况
// @Tags 产品
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Success 200 {array} InventoryResponse "产品库存列表"
// @Failure 404 {object} ErrorResponse "产品不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /products/{id}/inventory [get]
func (h *ProductHandler) GetProductInventory(c *gin.Context) {
	// 获取产品ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid product ID",
		})
		return
	}

	// 查询产品是否存在
	var product entity.Product
	if err := h.db.First(&product, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Product not found",
			})
			return
		}
		logger.Error("Failed to get product", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get product",
		})
		return
	}

	// 查询产品库存
	var inventories []entity.Inventory
	if err := h.db.Where("product_id = ?", id).Find(&inventories).Error; err != nil {
		logger.Error("Failed to get product inventory", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get product inventory",
		})
		return
	}

	// 构建响应
	var response []gin.H
	for _, inventory := range inventories {
		// 查询仓库信息
		var warehouse entity.Warehouse
		if err := h.db.First(&warehouse, inventory.WarehouseID).Error; err != nil {
			logger.Error("Failed to get warehouse", err)
			continue
		}

		response = append(response, gin.H{
			"id":          inventory.ID,
			"productId":   inventory.ProductID,
			"warehouseId": inventory.WarehouseID,
			"warehouse": gin.H{
				"id":   warehouse.ID,
				"code": warehouse.Code,
				"name": warehouse.Name,
			},
			"quantity":  inventory.Quantity,
			"location":  inventory.Location,
			"createdAt": inventory.CreatedAt.Format(time.RFC3339),
			"updatedAt": inventory.UpdatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, response)
}
