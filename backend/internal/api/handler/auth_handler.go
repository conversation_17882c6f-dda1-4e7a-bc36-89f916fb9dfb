package handler

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/api/middleware"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/crypto"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	db *gorm.DB
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(db *gorm.DB) *AuthHandler {
	return &AuthHandler{db: db}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username          string `json:"username" binding:"required"`
	Password          string `json:"password" binding:"required"`
	PasswordEncrypted bool   `json:"passwordEncrypted"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string       `json:"token"`
	User      UserResponse `json:"user"`
	ExpiresIn int          `json:"expiresIn"` // 过期时间（小时）
}

// UserResponse 用户响应
type UserResponse struct {
	ID         uint   `json:"id"`
	Username   string `json:"username"`
	RealName   string `json:"realName"`
	Email      string `json:"email"`
	Department string `json:"department"`
	Status     int    `json:"status"`
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录并获取JWT令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录信息"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 查询用户
	var user entity.User
	if err := h.db.Where("username = ?", req.Username).First(&user).Error; err != nil {
		logger.Error("Failed to get user", err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Invalid username or password",
		})
		return
	}

	// 处理密码
	var passwordToVerify string
	var err error

	// 如果密码已加密，先解密
	if req.PasswordEncrypted {
		logger.Debug("Received encrypted password, attempting to decrypt")

		passwordToVerify, err = crypto.DecryptPassword(req.Password)
		if err != nil {
			logger.Error("Failed to decrypt password", err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "Invalid username or password",
			})
			return
		}

		logger.Debug("Password decrypted successfully: " + passwordToVerify)
	} else {
		// 未加密的密码直接使用
		logger.Debug("Received plaintext password")
		passwordToVerify = req.Password
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(passwordToVerify)); err != nil {
		logger.Error("Password verification failed", err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Invalid username or password",
		})
		return
	}

	// 检查用户状态
	if user.Status != 1 {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "User is disabled",
		})
		return
	}

	// 生成令牌
	token, err := middleware.GenerateToken(user.ID, user.Username)
	if err != nil {
		logger.Error("err ", err)
		logger.Error("Failed to generate token", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to generate token",
		})
		return
	}

	// 更新最后登录时间
	h.db.Model(&user).Update("last_login_at", time.Now())

	// 返回响应
	c.JSON(http.StatusOK, LoginResponse{
		Token: token,
		User: UserResponse{
			ID:         user.ID,
			Username:   user.Username,
			RealName:   user.RealName,
			Email:      user.Email,
			Department: user.Department,
			Status:     user.Status,
		},
		ExpiresIn: 24, // 24小时
	})
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出（目前仅返回成功，因为JWT是无状态的）
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} SuccessResponse "登出成功"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// JWT是无状态的，客户端只需要删除令牌即可
	// 这里可以实现黑名单机制，但需要额外的存储
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Logout successful",
	})
}

// GetUserInfo 获取用户信息
// @Summary 获取当前用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} UserResponse "用户信息"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /auth/user [get]
func (h *AuthHandler) GetUserInfo(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, _ := c.Get("userID")

	// 查询用户
	var user entity.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get user info",
		})
		return
	}

	// 返回用户信息
	c.JSON(http.StatusOK, UserResponse{
		ID:         user.ID,
		Username:   user.Username,
		RealName:   user.RealName,
		Email:      user.Email,
		Department: user.Department,
		Status:     user.Status,
	})
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword       string `json:"oldPassword" binding:"required"`
	NewPassword       string `json:"newPassword" binding:"required,min=6"`
	PasswordEncrypted bool   `json:"passwordEncrypted"`
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前用户的密码
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body ChangePasswordRequest true "密码信息"
// @Success 200 {object} SuccessResponse "修改成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /auth/change-password [post]

// GetUsers 获取所有用户
// @Summary 获取所有用户
// @Description 获取系统中所有用户的列表
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {array} UserResponse "用户列表"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /users [get]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 从上下文中获取用户ID
	userID, _ := c.Get("userID")

	// 查询用户
	var user entity.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get user info",
		})
		return
	}

	// 处理密码
	var oldPasswordToVerify, newPasswordToHash string
	var err error

	// 如果密码已加密，先解密
	if req.PasswordEncrypted {
		oldPasswordToVerify, err = crypto.DecryptPassword(req.OldPassword)
		if err != nil {
			logger.Error("Failed to decrypt old password", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Invalid old password format",
			})
			return
		}

		newPasswordToHash, err = crypto.DecryptPassword(req.NewPassword)
		if err != nil {
			logger.Error("Failed to decrypt new password", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Invalid new password format",
			})
			return
		}
	} else {
		// 未加密的密码直接使用
		oldPasswordToVerify = req.OldPassword
		newPasswordToHash = req.NewPassword
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPasswordToVerify)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Old password is incorrect",
		})
		return
	}

	// 生成新密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPasswordToHash), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("Failed to hash password", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to change password",
		})
		return
	}

	// 更新密码
	if err := h.db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		logger.Error("Failed to update password", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to change password",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Password changed successfully",
	})
}

// GetUsers 获取所有用户
func (h *AuthHandler) GetUsers(c *gin.Context) {
	var users []entity.User
	if err := h.db.Find(&users).Error; err != nil {
		logger.Error("Failed to get users", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get users",
		})
		return
	}

	var response []UserResponse
	for _, user := range users {
		response = append(response, UserResponse{
			ID:         user.ID,
			Username:   user.Username,
			RealName:   user.RealName,
			Email:      user.Email,
			Department: user.Department,
			Status:     user.Status,
		})
	}

	c.JSON(http.StatusOK, response)
}
