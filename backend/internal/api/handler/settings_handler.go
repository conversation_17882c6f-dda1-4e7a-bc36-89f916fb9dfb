package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// SettingsHandler 系统设置处理器
type SettingsHandler struct {
	db *gorm.DB
}

// NewSettingsHandler 创建系统设置处理器
func NewSettingsHandler(db *gorm.DB) *SettingsHandler {
	return &SettingsHandler{db: db}
}

// GetSystemSettings 获取系统设置
// @Summary 获取系统设置
// @Description 获取系统设置
// @Tags 系统设置
// @Accept json
// @Produce json
// @Success 200 {object} entity.SystemSettings "系统设置"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /settings [get]
func (h *SettingsHandler) GetSystemSettings(c *gin.Context) {
	var settings entity.SystemSettings
	
	// 查询系统设置，如果不存在则创建默认设置
	result := h.db.First(&settings)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 创建默认设置
			settings = entity.SystemSettings{
				SystemName:     "仓库管理系统",
				CompanyName:    "",
				ContactPhone:   "",
				ContactEmail:   "",
				Address:        "",
				OrderPrefix:    "ORD",
				ProductPrefix:  "PRD",
				CustomerPrefix: "CUS",
				PageSize:       10,
			}
			
			if err := h.db.Create(&settings).Error; err != nil {
				logger.Error("Failed to create default system settings", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to get system settings",
				})
				return
			}
		} else {
			logger.Error("Failed to get system settings", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to get system settings",
			})
			return
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": settings,
	})
}

// UpdateSystemSettings 更新系统设置
// @Summary 更新系统设置
// @Description 更新系统设置
// @Tags 系统设置
// @Accept json
// @Produce json
// @Param request body entity.SystemSettings true "系统设置"
// @Success 200 {object} entity.SystemSettings "更新成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /settings [put]
func (h *SettingsHandler) UpdateSystemSettings(c *gin.Context) {
	var req entity.SystemSettings
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}
	
	// 查询现有设置
	var settings entity.SystemSettings
	result := h.db.First(&settings)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 如果不存在，则创建新设置
			if err := h.db.Create(&req).Error; err != nil {
				logger.Error("Failed to create system settings", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update system settings",
				})
				return
			}
		} else {
			logger.Error("Failed to get system settings for update", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to update system settings",
			})
			return
		}
	} else {
		// 如果存在，则更新
		settings.SystemName = req.SystemName
		settings.CompanyName = req.CompanyName
		settings.ContactPhone = req.ContactPhone
		settings.ContactEmail = req.ContactEmail
		settings.Address = req.Address
		settings.OrderPrefix = req.OrderPrefix
		settings.ProductPrefix = req.ProductPrefix
		settings.CustomerPrefix = req.CustomerPrefix
		settings.PageSize = req.PageSize
		
		if err := h.db.Save(&settings).Error; err != nil {
			logger.Error("Failed to update system settings", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to update system settings",
			})
			return
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": req,
	})
}
