package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// DashboardHandler 仪表盘处理器
type DashboardHandler struct {
	db *gorm.DB
}

// NewDashboardHandler 创建仪表盘处理器
func NewDashboardHandler(db *gorm.DB) *DashboardHandler {
	return &DashboardHandler{db: db}
}

// OverviewResponse 仪表盘概览响应
type OverviewResponse struct {
	TotalProducts   int64   `json:"totalProducts"`
	MonthlyOrders   int64   `json:"monthlyOrders"`
	ActiveCustomers int64   `json:"activeCustomers"`
	MonthlySales    float64 `json:"monthlySales"`
}

// InventoryAlertResponse 库存预警响应
type InventoryAlertResponse struct {
	ID        uint    `json:"id"`
	ProductID uint    `json:"productId"`
	Name      string  `json:"name"`
	Code      string  `json:"code"`
	Current   float64 `json:"current"`
	Min       float64 `json:"min"`
	Max       float64 `json:"max"`
	Status    string  `json:"status"`
	Unit      string  `json:"unit"`
}

// RecentOrderResponse 最近订单响应
type RecentOrderResponse struct {
	ID           uint    `json:"id"`
	OrderNumber  string  `json:"orderNumber"`
	CustomerName string  `json:"customer"`
	Amount       float64 `json:"amount"`
	Status       string  `json:"status"`
	CreatedAt    string  `json:"createdAt"`
}

// GetDashboardOverview 获取仪表盘概览数据
// @Summary 获取仪表盘概览数据
// @Description 获取仪表盘概览数据，包括总产品数、本月订单数、活跃客户数和本月销售额
// @Tags 仪表盘
// @Accept json
// @Produce json
// @Success 200 {object} OverviewResponse "仪表盘概览数据"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /dashboard/overview [get]
func (h *DashboardHandler) GetDashboardOverview(c *gin.Context) {
	var response OverviewResponse

	// 获取总产品数
	if err := h.db.Model(&entity.Product{}).Where("is_active = ?", true).Count(&response.TotalProducts).Error; err != nil {
		logger.Error("Failed to count products", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get dashboard overview",
		})
		return
	}

	// 获取本月订单数，排除已取消的订单
	startOfMonth := time.Now().UTC().AddDate(0, 0, -time.Now().UTC().Day()+1).
		Truncate(24 * time.Hour)
	if err := h.db.Model(&entity.Order{}).Where("created_at >= ? AND status != ?", startOfMonth, entity.Cancelled).
		Count(&response.MonthlyOrders).Error; err != nil {
		logger.Error("Failed to count monthly orders", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get dashboard overview",
		})
		return
	}

	// 获取活跃客户数（过去30天有订单的客户），排除已取消的订单
	thirtyDaysAgo := time.Now().UTC().AddDate(0, 0, -30)
	if err := h.db.Model(&entity.Order{}).
		Select("COUNT(DISTINCT customer_id)").
		Where("created_at >= ? AND status != ?", thirtyDaysAgo, entity.Cancelled).
		Count(&response.ActiveCustomers).Error; err != nil {
		logger.Error("Failed to count active customers", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get dashboard overview",
		})
		return
	}

	// 获取本月销售额，排除已取消的订单
	if err := h.db.Model(&entity.Order{}).
		Select("COALESCE(SUM(total_amount), 0)").
		Where("created_at >= ? AND status != ?", startOfMonth, entity.Cancelled).
		Scan(&response.MonthlySales).Error; err != nil {
		logger.Error("Failed to sum monthly sales", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get dashboard overview",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetInventoryAlert 获取库存预警数据
// @Summary 获取库存预警数据
// @Description 获取库存预警数据，包括库存不足和库存过多的产品
// @Tags 仪表盘
// @Accept json
// @Produce json
// @Param limit query int false "限制返回数量，默认10"
// @Success 200 {array} InventoryAlertResponse "库存预警数据"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /dashboard/inventory-alert [get]
func (h *DashboardHandler) GetInventoryAlert(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// 查询所有产品的库存总量
	type ProductInventory struct {
		ProductID uint
		Total     float64
	}

	var productInventories []ProductInventory
	if err := h.db.Model(&entity.Inventory{}).
		Select("product_id, SUM(quantity) as total").
		Group("product_id").
		Find(&productInventories).Error; err != nil {
		logger.Error("Failed to get product inventories", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get inventory alert",
		})
		return
	}

	// 查询所有产品信息
	var products []entity.Product
	if err := h.db.Find(&products).Error; err != nil {
		logger.Error("Failed to get products", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get inventory alert",
		})
		return
	}

	// 构建产品ID到库存总量的映射
	inventoryMap := make(map[uint]float64)
	for _, pi := range productInventories {
		inventoryMap[pi.ProductID] = pi.Total
	}

	// 构建预警列表
	var alerts []InventoryAlertResponse
	for _, product := range products {
		currentStock := inventoryMap[product.ID]

		// 判断库存状态
		var status string
		if currentStock <= 0 {
			status = "紧急" // 无库存
		} else if currentStock < product.MinStock {
			status = "紧急" // 库存不足
		} else if currentStock > product.MaxStock {
			status = "警告" // 库存过多
		} else {
			continue // 库存正常，不需要预警
		}

		alerts = append(alerts, InventoryAlertResponse{
			ID:        product.ID,
			ProductID: product.ID,
			Name:      product.Name,
			Code:      product.Code,
			Current:   currentStock,
			Min:       product.MinStock,
			Max:       product.MaxStock,
			Status:    status,
			Unit:      product.Unit,
		})
	}

	// 限制返回数量
	if len(alerts) > limit {
		alerts = alerts[:limit]
	}

	c.JSON(http.StatusOK, alerts)
}

// GetRecentOrders 获取最近订单
// @Summary 获取最近订单
// @Description 获取最近的订单列表
// @Tags 仪表盘
// @Accept json
// @Produce json
// @Param limit query int false "限制返回数量，默认5"
// @Success 200 {array} RecentOrderResponse "最近订单列表"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /dashboard/recent-orders [get]
func (h *DashboardHandler) GetRecentOrders(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "5"))

	var orders []entity.Order
	if err := h.db.Preload("Customer").
		Where("status != ?", entity.Cancelled).
		Order("created_at DESC").
		Limit(limit).
		Find(&orders).Error; err != nil {
		logger.Error("Failed to get recent orders", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get recent orders",
		})
		return
	}

	var response []RecentOrderResponse
	for _, order := range orders {
		status := "未知"
		switch order.Status {
		case entity.Created:
			status = "已创建"
		case entity.Confirmed:
			status = "已确认"
		case entity.Processing:
			status = "处理中"
		case entity.Shipped:
			status = "已发货"
		case entity.Delivered:
			status = "已交付"
		case entity.Completed:
			status = "已完成"
		case entity.Cancelled:
			status = "已取消"
		}

		response = append(response, RecentOrderResponse{
			ID:           order.ID,
			OrderNumber:  order.OrderNumber,
			CustomerName: order.Customer.Name,
			Amount:       order.TotalAmount,
			Status:       status,
			CreatedAt:    order.CreatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, response)
}
