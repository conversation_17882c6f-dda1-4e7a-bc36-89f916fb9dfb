package handler

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// CustomerHandler 客户处理器
type CustomerHandler struct {
	db *gorm.DB
}

// NewCustomerHandler 创建客户处理器
func NewCustomerHandler(db *gorm.DB) *CustomerHandler {
	return &CustomerHandler{db: db}
}

// CustomerResponse 客户响应
type CustomerResponse struct {
	ID            uint   `json:"id"`
	Code          string `json:"code"`
	Name          string `json:"name"`
	ContactPerson string `json:"contactPerson"`
	ContactPhone  string `json:"contactPhone"`
	Email         string `json:"email"`
	Address       string `json:"address"`
	City          string `json:"city"`
	Province      string `json:"province"`
	PostalCode    string `json:"postalCode"`
	TaxID         string `json:"taxId"`
	BankAccount   string `json:"bankAccount"`
	BankName      string `json:"bankName"`
	Notes         string `json:"notes"`
	SalesPerson   string `json:"salesPerson"`
	SalesPersonID *uint  `json:"salesPersonId"`
	Type          int    `json:"type"`
	Level         int    `json:"level"`
	IsActive      bool   `json:"isActive"`
	CreatedAt     string `json:"createdAt"`
	UpdatedAt     string `json:"updatedAt"`
}

// CustomerListResponse 客户列表响应
type CustomerListResponse struct {
	Total int64              `json:"total"`
	Data  []CustomerResponse `json:"data"`
}

// CreateCustomerRequest 创建客户请求
type CreateCustomerRequest struct {
	Code          string `json:"code"`
	Name          string `json:"name" binding:"required"`
	ContactPerson string `json:"contactPerson"`
	ContactPhone  string `json:"contactPhone"`
	Email         string `json:"email"`
	Address       string `json:"address"`
	City          string `json:"city"`
	Province      string `json:"province"`
	PostalCode    string `json:"postalCode"`
	TaxID         string `json:"taxId"`
	BankAccount   string `json:"bankAccount"`
	BankName      string `json:"bankName"`
	Notes         string `json:"notes"`
	SalesPerson   string `json:"salesPerson"`
	SalesPersonID *uint  `json:"salesPersonId"`
	Type          int    `json:"type"`
	Level         int    `json:"level"`
	IsActive      bool   `json:"isActive"`
}

// UpdateCustomerRequest 更新客户请求
type UpdateCustomerRequest struct {
	Name          string `json:"name"`
	ContactPerson string `json:"contactPerson"`
	ContactPhone  string `json:"contactPhone"`
	Email         string `json:"email"`
	Address       string `json:"address"`
	City          string `json:"city"`
	Province      string `json:"province"`
	PostalCode    string `json:"postalCode"`
	TaxID         string `json:"taxId"`
	BankAccount   string `json:"bankAccount"`
	BankName      string `json:"bankName"`
	Notes         string `json:"notes"`
	SalesPerson   string `json:"salesPerson"`
	SalesPersonID *uint  `json:"salesPersonId"`
	Type          *int   `json:"type"`
	Level         *int   `json:"level"`
	IsActive      *bool  `json:"isActive"`
}

// GetCustomers 获取客户列表
// @Summary 获取客户列表
// @Description 获取客户列表，支持分页、搜索和过滤
// @Tags 客户
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键字"
// @Param isActive query string false "是否启用，true或false"
// @Param type query int false "客户类型"
// @Param level query int false "客户等级"
// @Success 200 {object} CustomerListResponse "客户列表"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /customers [get]
func (h *CustomerHandler) GetCustomers(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	query := c.Query("query")
	isActiveStr := c.Query("isActive")
	typeStr := c.Query("type")
	levelStr := c.Query("level")

	// 构建查询
	db := h.db.Model(&entity.Customer{})

	// 添加搜索条件
	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ? OR contact_person LIKE ? OR contact_phone LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 添加过滤条件
	if isActiveStr != "" {
		isActive := isActiveStr == "true"
		db = db.Where("is_active = ?", isActive)
	}

	if typeStr != "" {
		customerType, _ := strconv.Atoi(typeStr)
		db = db.Where("type = ?", customerType)
	}

	if levelStr != "" {
		customerLevel, _ := strconv.Atoi(levelStr)
		db = db.Where("level = ?", customerLevel)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count customers", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get customers",
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var customers []entity.Customer
	if err := db.Order("created_at DESC").
		Offset(offset).Limit(pageSize).Find(&customers).Error; err != nil {
		logger.Error("Failed to get customers", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get customers",
		})
		return
	}

	// 构建响应
	var response []CustomerResponse
	for _, customer := range customers {
		// 获取销售人员信息
		salesPerson := ""

		// 如果有销售人员ID，优先使用
		if customer.SalesPersonID != nil {
			var salesUser entity.User
			if err := h.db.First(&salesUser, *customer.SalesPersonID).Error; err == nil {
				salesPerson = salesUser.RealName
			}
		}

		// 如果没有销售人员ID或查询失败，尝试从Notes中提取（兼容旧版本）
		if salesPerson == "" && customer.Notes != "" {
			// 查找"销售:"或"销售："后面的内容
			salesIndex := -1
			if idx := strings.Index(customer.Notes, "销售:"); idx >= 0 {
				salesIndex = idx + 3
			} else if idx := strings.Index(customer.Notes, "销售："); idx >= 0 {
				salesIndex = idx + 3
			}

			if salesIndex >= 0 {
				// 提取销售人员信息，直到行尾或换行符
				endIndex := strings.Index(customer.Notes[salesIndex:], "\n")
				if endIndex < 0 {
					// 没有换行符，取到字符串结尾
					salesPerson = strings.TrimSpace(customer.Notes[salesIndex:])
				} else {
					// 有换行符，取到换行符
					salesPerson = strings.TrimSpace(customer.Notes[salesIndex : salesIndex+endIndex])
				}
			}
		}

		response = append(response, CustomerResponse{
			ID:            customer.ID,
			Code:          customer.Code,
			Name:          customer.Name,
			ContactPerson: customer.ContactPerson,
			ContactPhone:  customer.ContactPhone,
			Email:         customer.Email,
			Address:       customer.Address,
			City:          customer.City,
			Province:      customer.Province,
			PostalCode:    customer.PostalCode,
			TaxID:         customer.TaxID,
			BankAccount:   customer.BankAccount,
			BankName:      customer.BankName,
			Notes:         customer.Notes,
			SalesPerson:   salesPerson,
			SalesPersonID: customer.SalesPersonID,
			Type:          int(customer.Type),
			Level:         int(customer.Level),
			IsActive:      customer.IsActive,
			CreatedAt:     customer.CreatedAt.Format(time.RFC3339),
			UpdatedAt:     customer.UpdatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, CustomerListResponse{
		Total: total,
		Data:  response,
	})
}

// GetCustomer 获取客户详情
// @Summary 获取客户详情
// @Description 根据ID获取客户详情
// @Tags 客户
// @Accept json
// @Produce json
// @Param id path int true "客户ID"
// @Success 200 {object} CustomerResponse "客户详情"
// @Failure 404 {object} ErrorResponse "客户不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /customers/{id} [get]
func (h *CustomerHandler) GetCustomer(c *gin.Context) {
	// 获取客户ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid customer ID",
		})
		return
	}

	// 查询客户
	var customer entity.Customer
	if err := h.db.First(&customer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Customer not found",
			})
			return
		}
		// 使用简洁的DBError函数记录错误
		logger.DBError(err, "查询客户详情",
			"id", id)

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get customer",
		})
		return
	}

	// 获取销售人员信息
	salesPerson := ""

	// 如果有销售人员ID，优先使用
	if customer.SalesPersonID != nil {
		var salesUser entity.User
		if err := h.db.First(&salesUser, *customer.SalesPersonID).Error; err == nil {
			salesPerson = salesUser.RealName
		}
	}

	// 如果没有销售人员ID或查询失败，尝试从Notes中提取（兼容旧版本）
	if salesPerson == "" && customer.Notes != "" {
		// 查找"销售:"或"销售："后面的内容
		salesIndex := -1
		if idx := strings.Index(customer.Notes, "销售:"); idx >= 0 {
			salesIndex = idx + 3
		} else if idx := strings.Index(customer.Notes, "销售："); idx >= 0 {
			salesIndex = idx + 3
		}

		if salesIndex >= 0 {
			// 提取销售人员信息，直到行尾或换行符
			endIndex := strings.Index(customer.Notes[salesIndex:], "\n")
			if endIndex < 0 {
				// 没有换行符，取到字符串结尾
				salesPerson = strings.TrimSpace(customer.Notes[salesIndex:])
			} else {
				// 有换行符，取到换行符
				salesPerson = strings.TrimSpace(customer.Notes[salesIndex : salesIndex+endIndex])
			}
		}
	}

	// 构建响应
	response := CustomerResponse{
		ID:            customer.ID,
		Code:          customer.Code,
		Name:          customer.Name,
		ContactPerson: customer.ContactPerson,
		ContactPhone:  customer.ContactPhone,
		Email:         customer.Email,
		Address:       customer.Address,
		City:          customer.City,
		Province:      customer.Province,
		PostalCode:    customer.PostalCode,
		TaxID:         customer.TaxID,
		BankAccount:   customer.BankAccount,
		BankName:      customer.BankName,
		Notes:         customer.Notes,
		SalesPerson:   salesPerson,
		SalesPersonID: customer.SalesPersonID,
		Type:          int(customer.Type),
		Level:         int(customer.Level),
		IsActive:      customer.IsActive,
		CreatedAt:     customer.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     customer.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// CreateCustomer 创建客户
// @Summary 创建客户
// @Description 创建新客户
// @Tags 客户
// @Accept json
// @Produce json
// @Param request body CreateCustomerRequest true "客户信息"
// @Success 201 {object} CustomerResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /customers [post]
func (h *CustomerHandler) CreateCustomer(c *gin.Context) {
	// 获取请求体原始数据并记录
	requestBody, _ := c.GetRawData()
	logger.Info("创建客户请求体: " + string(requestBody))

	// 重新设置请求体，因为GetRawData会消耗请求体
	c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))

	var req CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("请求参数绑定失败", err)
		logger.Info("请求参数: " + string(requestBody))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 记录解析后的请求参数
	logger.Info(fmt.Sprintf("解析后的请求参数: Name=%s, Type=%d, IsActive=%v",
		req.Name, req.Type, req.IsActive))

	// 自动生成客户编码
	// 格式: C + 年月日 + 4位序号，例如: C2023060100001
	now := time.Now()
	datePrefix := fmt.Sprintf("C%s", now.Format("20060102"))

	// 查询当天最大的客户编码
	var maxCode string
	query := "code LIKE ?"
	if err := h.db.Model(&entity.Customer{}).
		Where(query, datePrefix+"%").
		Order("code DESC").
		Limit(1).
		Pluck("code", &maxCode).Error; err != nil && err != gorm.ErrRecordNotFound {
		// 使用增强的错误日志
		logger.ErrorTrace("查询最大客户编码失败", err)
		// 记录SQL查询信息
		logger.SQLError("SQL执行失败", err, query, datePrefix+"%")

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to generate customer code",
		})
		return
	}

	// 生成新的客户编码
	var newSeq int
	if maxCode == "" {
		// 如果当天没有客户，从1开始
		newSeq = 1
	} else {
		// 提取序号部分并加1
		seqStr := maxCode[len(datePrefix):]
		seq, err := strconv.Atoi(seqStr)
		if err != nil {
			logger.Error("解析客户编码序号失败", err)
			newSeq = 1
		} else {
			newSeq = seq + 1
		}
	}

	// 生成新的客户编码
	newCode := fmt.Sprintf("%s%05d", datePrefix, newSeq)
	logger.Info(fmt.Sprintf("自动生成客户编码: %s", newCode))

	// 创建客户
	customer := entity.Customer{
		Code:          newCode, // 使用自动生成的编码
		Name:          req.Name,
		ContactPerson: req.ContactPerson,
		ContactPhone:  req.ContactPhone,
		Email:         req.Email,
		Address:       req.Address,
		City:          req.City,
		Province:      req.Province,
		PostalCode:    req.PostalCode,
		TaxID:         req.TaxID,
		BankAccount:   req.BankAccount,
		BankName:      req.BankName,
		Notes:         req.Notes,
		Type:          entity.CustomerType(req.Type),
		Level:         entity.CustomerLevel(req.Level),
		IsActive:      req.IsActive,
		SalesPersonID: req.SalesPersonID,
	}

	// 如果提供了销售人员信息，保存到Notes字段（兼容旧版本）
	if req.SalesPerson != "" && req.SalesPersonID == nil {
		if customer.Notes != "" {
			customer.Notes += "\n"
		}
		customer.Notes += "销售: " + req.SalesPerson
	}

	// 记录创建的客户对象
	logger.Info(fmt.Sprintf("准备创建客户: %+v", customer))

	// 保存客户
	if err := h.db.Create(&customer).Error; err != nil {
		// 使用简洁的DBError函数记录错误，包含所有必要的上下文信息
		logger.DBError(err, "创建客户",
			"code", customer.Code,
			"name", customer.Name,
			"type", int(customer.Type),
			"is_active", customer.IsActive,
			"contact_person", customer.ContactPerson,
			"contact_phone", customer.ContactPhone)

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create customer: " + err.Error(),
		})
		return
	}

	// 获取销售人员信息
	salesPerson := req.SalesPerson

	// 如果有销售人员ID，优先使用
	if req.SalesPersonID != nil {
		var salesUser entity.User
		if err := h.db.First(&salesUser, *req.SalesPersonID).Error; err == nil {
			salesPerson = salesUser.RealName
		}
	}

	// 构建响应
	response := CustomerResponse{
		ID:            customer.ID,
		Code:          customer.Code,
		Name:          customer.Name,
		ContactPerson: customer.ContactPerson,
		ContactPhone:  customer.ContactPhone,
		Email:         customer.Email,
		Address:       customer.Address,
		City:          customer.City,
		Province:      customer.Province,
		PostalCode:    customer.PostalCode,
		TaxID:         customer.TaxID,
		BankAccount:   customer.BankAccount,
		BankName:      customer.BankName,
		Notes:         customer.Notes,
		SalesPerson:   salesPerson,
		SalesPersonID: customer.SalesPersonID,
		Type:          int(customer.Type),
		Level:         int(customer.Level),
		IsActive:      customer.IsActive,
		CreatedAt:     customer.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     customer.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateCustomer 更新客户
// @Summary 更新客户
// @Description 更新客户信息
// @Tags 客户
// @Accept json
// @Produce json
// @Param id path int true "客户ID"
// @Param request body UpdateCustomerRequest true "客户信息"
// @Success 200 {object} CustomerResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "客户不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /customers/{id} [put]
func (h *CustomerHandler) UpdateCustomer(c *gin.Context) {
	// 获取客户ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid customer ID",
		})
		return
	}

	var req UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 查询客户
	var customer entity.Customer
	if err := h.db.First(&customer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Customer not found",
			})
			return
		}
		logger.Error("Failed to get customer", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update customer",
		})
		return
	}

	// 更新客户信息
	updates := map[string]interface{}{}

	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.ContactPerson != "" {
		updates["contact_person"] = req.ContactPerson
	}
	if req.ContactPhone != "" {
		updates["contact_phone"] = req.ContactPhone
	}
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.Address != "" {
		updates["address"] = req.Address
	}
	if req.City != "" {
		updates["city"] = req.City
	}
	if req.Province != "" {
		updates["province"] = req.Province
	}
	if req.PostalCode != "" {
		updates["postal_code"] = req.PostalCode
	}
	if req.TaxID != "" {
		updates["tax_id"] = req.TaxID
	}
	if req.BankAccount != "" {
		updates["bank_account"] = req.BankAccount
	}
	if req.BankName != "" {
		updates["bank_name"] = req.BankName
	}
	if req.SalesPersonID != nil {
		updates["sales_person_id"] = req.SalesPersonID
	}
	// 处理Notes和SalesPerson
	if req.Notes != "" || req.SalesPerson != "" {
		// 如果提供了销售人员信息，需要更新Notes字段
		if req.SalesPerson != "" {
			// 从现有Notes中提取非销售信息
			notes := customer.Notes
			salesIndex := -1
			if idx := strings.Index(notes, "销售:"); idx >= 0 {
				salesIndex = idx
			} else if idx := strings.Index(notes, "销售："); idx >= 0 {
				salesIndex = idx
			}

			if salesIndex >= 0 {
				// 找到换行符位置
				endIndex := strings.Index(notes[salesIndex:], "\n")
				if endIndex < 0 {
					// 没有换行符，销售信息在最后
					notes = notes[:salesIndex]
				} else {
					// 有换行符，保留其他信息
					notes = notes[:salesIndex] + notes[salesIndex+endIndex+1:]
				}
			}

			// 添加新的销售信息
			if notes != "" && !strings.HasSuffix(notes, "\n") {
				notes += "\n"
			}
			notes += "销售: " + req.SalesPerson

			updates["notes"] = notes
		} else if req.Notes != "" {
			// 只更新Notes，保留销售信息
			salesPerson := ""
			notes := customer.Notes
			salesIndex := -1
			if idx := strings.Index(notes, "销售:"); idx >= 0 {
				salesIndex = idx
			} else if idx := strings.Index(notes, "销售："); idx >= 0 {
				salesIndex = idx
			}

			if salesIndex >= 0 {
				// 提取销售人员信息
				endIndex := strings.Index(notes[salesIndex:], "\n")
				if endIndex < 0 {
					salesPerson = notes[salesIndex:]
					notes = ""
				} else {
					salesPerson = notes[salesIndex : salesIndex+endIndex]
					notes = notes[:salesIndex] + notes[salesIndex+endIndex+1:]
				}
			}

			// 组合新的Notes
			newNotes := req.Notes
			if salesPerson != "" {
				if newNotes != "" && !strings.HasSuffix(newNotes, "\n") {
					newNotes += "\n"
				}
				newNotes += salesPerson
			}

			updates["notes"] = newNotes
		}
	}

	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Level != nil {
		updates["level"] = *req.Level
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	// 不再需要设置updated_by字段

	// 保存更新
	if err := h.db.Model(&customer).Updates(updates).Error; err != nil {
		// 使用简洁的DBError函数记录错误
		logger.DBError(err, "更新客户",
			"id", customer.ID,
			"code", customer.Code,
			"name", customer.Name,
			"updates", updates)

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update customer",
		})
		return
	}

	// 重新查询客户
	if err := h.db.First(&customer, id).Error; err != nil {
		logger.Error("Failed to get updated customer", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Customer updated but failed to retrieve details",
		})
		return
	}

	// 获取销售人员信息
	salesPerson := ""

	// 如果有销售人员ID，优先使用
	if customer.SalesPersonID != nil {
		var salesUser entity.User
		if err := h.db.First(&salesUser, *customer.SalesPersonID).Error; err == nil {
			salesPerson = salesUser.RealName
		}
	}

	// 如果没有销售人员ID或查询失败，尝试从Notes中提取（兼容旧版本）
	if salesPerson == "" && customer.Notes != "" {
		// 查找"销售:"或"销售："后面的内容
		salesIndex := -1
		if idx := strings.Index(customer.Notes, "销售:"); idx >= 0 {
			salesIndex = idx + 3
		} else if idx := strings.Index(customer.Notes, "销售："); idx >= 0 {
			salesIndex = idx + 3
		}

		if salesIndex >= 0 {
			// 提取销售人员信息，直到行尾或换行符
			endIndex := strings.Index(customer.Notes[salesIndex:], "\n")
			if endIndex < 0 {
				// 没有换行符，取到字符串结尾
				salesPerson = strings.TrimSpace(customer.Notes[salesIndex:])
			} else {
				// 有换行符，取到换行符
				salesPerson = strings.TrimSpace(customer.Notes[salesIndex : salesIndex+endIndex])
			}
		}
	}

	// 构建响应
	response := CustomerResponse{
		ID:            customer.ID,
		Code:          customer.Code,
		Name:          customer.Name,
		ContactPerson: customer.ContactPerson,
		ContactPhone:  customer.ContactPhone,
		Email:         customer.Email,
		Address:       customer.Address,
		City:          customer.City,
		Province:      customer.Province,
		PostalCode:    customer.PostalCode,
		TaxID:         customer.TaxID,
		BankAccount:   customer.BankAccount,
		BankName:      customer.BankName,
		Notes:         customer.Notes,
		SalesPerson:   salesPerson,
		SalesPersonID: customer.SalesPersonID,
		Type:          int(customer.Type),
		Level:         int(customer.Level),
		IsActive:      customer.IsActive,
		CreatedAt:     customer.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     customer.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// DeleteCustomer 删除客户
// @Summary 删除客户
// @Description 删除客户
// @Tags 客户
// @Accept json
// @Produce json
// @Param id path int true "客户ID"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "客户不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /customers/{id} [delete]
func (h *CustomerHandler) DeleteCustomer(c *gin.Context) {
	// 获取客户ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid customer ID",
		})
		return
	}

	// 查询客户
	var customer entity.Customer
	if err := h.db.First(&customer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Customer not found",
			})
			return
		}
		logger.Error("Failed to get customer", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete customer",
		})
		return
	}

	// 检查是否有关联的订单
	var orderCount int64
	if err := h.db.Model(&entity.Order{}).Where("customer_id = ?", id).Count(&orderCount).Error; err != nil {
		// 使用简洁的DBError函数记录错误
		logger.DBError(err, "检查客户关联订单",
			"customer_id", id)

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete customer",
		})
		return
	}

	if orderCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Cannot delete customer with related orders",
		})
		return
	}

	// 删除客户（软删除）
	if err := h.db.Delete(&customer).Error; err != nil {
		// 使用简洁的DBError函数记录错误
		logger.DBError(err, "删除客户",
			"id", customer.ID,
			"code", customer.Code,
			"name", customer.Name)

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete customer",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Customer deleted successfully",
	})
}
