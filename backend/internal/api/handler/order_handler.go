package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/pkg/logger"
	"gorm.io/gorm"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	db *gorm.DB
}

// NewOrderHandler 创建订单处理器
func NewOrderHandler(db *gorm.DB) *OrderHandler {
	return &OrderHandler{db: db}
}

// OrderItemRequest 订单项请求
type OrderItemRequest struct {
	ProductID   uint    `json:"productId" binding:"required"`
	Quantity    float64 `json:"quantity" binding:"required,gt=0"`
	UnitPrice   float64 `json:"unitPrice" binding:"required,gt=0"`
	BatchNumber string  `json:"batchNumber"`
	Remarks     string  `json:"remarks"`
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	CustomerID      uint               `json:"customerId" binding:"required"`
	OrderDate       string             `json:"orderDate" binding:"required"`
	Remarks         string             `json:"remarks"`
	DeliveryDate    string             `json:"deliveryDate"`
	DeliveryAddress string             `json:"deliveryAddress"`
	ContactPerson   string             `json:"contactPerson"`
	ContactPhone    string             `json:"contactPhone"`
	NeedInvoice     bool               `json:"needInvoice"`
	Company         string             `json:"company"`
	Items           []OrderItemRequest `json:"items" binding:"required,min=1"`
}

// UpdateOrderRequest 更新订单请求
type UpdateOrderRequest struct {
	Status              *entity.OrderStatus `json:"status"`
	PaidAmount          *float64            `json:"paidAmount"`
	TotalAmount         *float64            `json:"totalAmount"`
	Remarks             *string             `json:"remarks"`
	DeliveryDate        *string             `json:"deliveryDate"`
	DeliveryAddress     *string             `json:"deliveryAddress"`
	ContactPerson       *string             `json:"contactPerson"`
	ContactPhone        *string             `json:"contactPhone"`
	NeedInvoice         *bool               `json:"needInvoice"`
	Company             *string             `json:"company"`
	IsShipped           *bool               `json:"isShipped"`
	ShippedDate         *string             `json:"shippedDate"`
	IsDelivered         *bool               `json:"isDelivered"`
	DeliveredDate       *string             `json:"deliveredDate"`
	IsOutboundConfirmed *bool               `json:"isOutboundConfirmed"`
	OutboundDate        *string             `json:"outboundDate"`
	PaymentMethod       *string             `json:"paymentMethod"`
	Items               []OrderItemRequest  `json:"items"`
}

// CreatePaymentRequest 创建支付记录请求
type CreatePaymentRequest struct {
	OrderID       uint                 `json:"orderId" binding:"required"`
	Amount        float64              `json:"amount" binding:"required,gt=0"`
	PaymentMethod entity.PaymentMethod `json:"paymentMethod" binding:"required"`
	PaymentDate   string               `json:"paymentDate" binding:"required"`
	Remarks       string               `json:"remarks"`
}

// OrderItemResponse 订单项响应
type OrderItemResponse struct {
	ID          uint        `json:"id"`
	OrderID     uint        `json:"orderId"`
	ProductID   uint        `json:"productId"`
	Product     ProductInfo `json:"product"`
	Quantity    float64     `json:"quantity"`
	UnitPrice   float64     `json:"unitPrice"`
	Amount      float64     `json:"amount"`
	BatchNumber string      `json:"batchNumber"`
	Remarks     string      `json:"remarks"`
	CreatedAt   string      `json:"createdAt"`
	UpdatedAt   string      `json:"updatedAt"`
}

// CustomerInfo 客户信息
type CustomerInfo struct {
	ID            uint   `json:"id"`
	Code          string `json:"code"`
	Name          string `json:"name"`
	ContactPerson string `json:"contactPerson"`
	ContactPhone  string `json:"contactPhone"`
}

// PaymentResponse 支付记录响应
type PaymentResponse struct {
	ID                uint                 `json:"id"`
	OrderID           uint                 `json:"orderId"`
	Amount            float64              `json:"amount"`
	PaymentMethod     entity.PaymentMethod `json:"paymentMethod"`
	PaymentMethodText string               `json:"paymentMethodText"`
	PaymentDate       string               `json:"paymentDate"`
	Remarks           string               `json:"remarks"`
	CreatedAt         string               `json:"createdAt"`
	UpdatedAt         string               `json:"updatedAt"`
}

// OrderResponse 订单响应
type OrderResponse struct {
	ID                  uint                `json:"id"`
	OrderNumber         string              `json:"orderNumber"`
	CustomerID          uint                `json:"customerId"`
	Customer            CustomerInfo        `json:"customer"`
	OrderDate           string              `json:"orderDate"`
	TotalAmount         float64             `json:"totalAmount"`
	PaidAmount          float64             `json:"paidAmount"`
	UnpaidAmount        float64             `json:"unpaidAmount"`
	Status              entity.OrderStatus  `json:"status"`
	StatusText          string              `json:"statusText"`
	Remarks             string              `json:"remarks"`
	DeliveryDate        string              `json:"deliveryDate,omitempty"`
	DeliveryAddress     string              `json:"deliveryAddress"`
	ContactPerson       string              `json:"contactPerson"`
	ContactPhone        string              `json:"contactPhone"`
	NeedInvoice         bool                `json:"needInvoice"`
	Company             string              `json:"company"`
	IsShipped           bool                `json:"isShipped"`
	ShippedDate         string              `json:"shippedDate,omitempty"`
	IsDelivered         bool                `json:"isDelivered"`
	DeliveredDate       string              `json:"deliveredDate,omitempty"`
	IsOutboundConfirmed bool                `json:"isOutboundConfirmed"`
	OutboundDate        string              `json:"outboundDate,omitempty"`
	PaymentMethod       string              `json:"paymentMethod"`
	Items               []OrderItemResponse `json:"items"`
	Payments            []PaymentResponse   `json:"payments"`
	CreatedAt           string              `json:"createdAt"`
	UpdatedAt           string              `json:"updatedAt"`
}

// OrderListResponse 订单列表响应
type OrderListResponse struct {
	Total int64           `json:"total"`
	Data  []OrderResponse `json:"data"`
}

// GetOrderStatusText 获取订单状态文本
func GetOrderStatusText(status entity.OrderStatus) string {
	switch status {
	case entity.Created:
		return "已创建"
	case entity.Confirmed:
		return "已确认"
	case entity.Processing:
		return "处理中"
	case entity.Shipped:
		return "已发货"
	case entity.Delivered:
		return "已交付"
	case entity.Completed:
		return "已完成"
	case entity.Cancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// getPaymentMethodText 获取支付方式文本
func getPaymentMethodText(method entity.PaymentMethod) string {
	switch method {
	case entity.PaymentMethodCorporate:
		return "对公"
	case entity.PaymentMethodPersonal:
		return "对私"
	case entity.PaymentMethodWechat:
		return "微信"
	case entity.PaymentMethodAlipay:
		return "支付宝"
	default:
		return string(method)
	}
}

// GetOrders 获取订单列表
// @Summary 获取订单列表
// @Description 获取订单列表，支持分页、搜索和过滤
// @Tags 订单
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param customerId query int false "客户ID"
// @Param status query int false "订单状态"
// @Param query query string false "搜索关键字"
// @Param startDate query string false "开始日期，格式：YYYY-MM-DD"
// @Param endDate query string false "结束日期，格式：YYYY-MM-DD"
// @Param excludeCancelled query bool false "是否排除已取消订单，默认false"
// @Success 200 {object} OrderListResponse "订单列表"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /orders [get]
func (h *OrderHandler) GetOrders(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	customerIDStr := c.Query("customerId")
	statusStr := c.Query("status")
	query := c.Query("query")
	startDate := c.Query("startDate")
	endDate := c.Query("endDate")
	excludeCancelled := c.Query("excludeCancelled") == "true"

	// 构建查询
	db := h.db.Model(&entity.Order{})

	// 添加过滤条件
	if customerIDStr != "" {
		customerID, _ := strconv.Atoi(customerIDStr)
		db = db.Where("customer_id = ?", customerID)
	}

	if statusStr != "" {
		status, _ := strconv.Atoi(statusStr)
		db = db.Where("status = ?", status)
	}

	// 排除已取消的订单
	if excludeCancelled {
		db = db.Where("status != ?", entity.Cancelled)
	}

	// 添加日期范围过滤
	if startDate != "" {
		db = db.Where("order_date >= ?", startDate+" 00:00:00")
	}

	if endDate != "" {
		db = db.Where("order_date <= ?", endDate+" 23:59:59")
	}

	// 添加搜索条件
	if query != "" {
		db = db.Joins("JOIN customers ON orders.customer_id = customers.id").
			Where("orders.order_number LIKE ? OR customers.name LIKE ? OR customers.code LIKE ?",
				"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logger.Error("Failed to count orders", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get orders",
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var orders []entity.Order
	if err := db.Preload("Customer").Preload("Items").Preload("Items.Product").Preload("Payments").
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).Find(&orders).Error; err != nil {
		logger.Error("Failed to get orders", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get orders",
		})
		return
	}

	// 构建响应
	var response []OrderResponse
	for _, order := range orders {
		// 构建订单项响应
		var itemsResponse []OrderItemResponse
		for _, item := range order.Items {
			itemsResponse = append(itemsResponse, OrderItemResponse{
				ID:        item.ID,
				OrderID:   item.OrderID,
				ProductID: item.ProductID,
				Product: ProductInfo{
					ID:    item.Product.ID,
					Code:  item.Product.Code,
					Name:  item.Product.Name,
					Unit:  item.Product.Unit,
					Price: item.Product.Price,
				},
				Quantity:    item.Quantity,
				UnitPrice:   item.UnitPrice,
				Amount:      item.Amount,
				BatchNumber: item.BatchNumber,
				Remarks:     item.Remarks,
				CreatedAt:   item.CreatedAt.Format(time.RFC3339),
				UpdatedAt:   item.UpdatedAt.Format(time.RFC3339),
			})
		}

		// 处理可能为空的日期
		deliveryDate := ""
		if order.DeliveryDate != nil && !order.DeliveryDate.IsZero() {
			deliveryDate = order.DeliveryDate.Format(time.RFC3339)
		}

		// 构建支付记录响应
		var paymentsResponse []PaymentResponse
		for _, payment := range order.Payments {
			paymentsResponse = append(paymentsResponse, PaymentResponse{
				ID:                payment.ID,
				OrderID:           payment.OrderID,
				Amount:            payment.Amount,
				PaymentMethod:     payment.PaymentMethod,
				PaymentMethodText: getPaymentMethodText(payment.PaymentMethod),
				PaymentDate:       payment.PaymentDate.Format(time.RFC3339),
				Remarks:           payment.Remarks,
				CreatedAt:         payment.CreatedAt.Format(time.RFC3339),
				UpdatedAt:         payment.UpdatedAt.Format(time.RFC3339),
			})
		}

		// 计算欠款金额
		unpaidAmount := order.TotalAmount - order.PaidAmount
		if unpaidAmount < 0 {
			unpaidAmount = 0
		}

		// 处理可能为空的日期
		shippedDate := ""
		if order.ShippedDate != nil && !order.ShippedDate.IsZero() {
			shippedDate = order.ShippedDate.Format(time.RFC3339)
		}

		deliveredDate := ""
		if order.DeliveredDate != nil && !order.DeliveredDate.IsZero() {
			deliveredDate = order.DeliveredDate.Format(time.RFC3339)
		}

		outboundDate := ""
		if order.OutboundDate != nil && !order.OutboundDate.IsZero() {
			outboundDate = order.OutboundDate.Format(time.RFC3339)
		}

		// 构建订单响应
		orderResponse := OrderResponse{
			ID:          order.ID,
			OrderNumber: order.OrderNumber,
			CustomerID:  order.CustomerID,
			Customer: CustomerInfo{
				ID:            order.Customer.ID,
				Code:          order.Customer.Code,
				Name:          order.Customer.Name,
				ContactPerson: order.Customer.ContactPerson,
				ContactPhone:  order.Customer.ContactPhone,
			},
			OrderDate:           order.OrderDate.Format(time.RFC3339),
			TotalAmount:         order.TotalAmount,
			PaidAmount:          order.PaidAmount,
			UnpaidAmount:        unpaidAmount,
			Status:              order.Status,
			StatusText:          GetOrderStatusText(order.Status),
			Remarks:             order.Remarks,
			DeliveryDate:        deliveryDate,
			DeliveryAddress:     order.DeliveryAddress,
			ContactPerson:       order.ContactPerson,
			ContactPhone:        order.ContactPhone,
			NeedInvoice:         order.NeedInvoice,
			Company:             order.Company,
			IsShipped:           order.IsShipped,
			ShippedDate:         shippedDate,
			IsDelivered:         order.IsDelivered,
			DeliveredDate:       deliveredDate,
			IsOutboundConfirmed: order.IsOutboundConfirmed,
			OutboundDate:        outboundDate,
			PaymentMethod:       order.PaymentMethod,
			Items:               itemsResponse,
			Payments:            paymentsResponse,
			CreatedAt:           order.CreatedAt.Format(time.RFC3339),
			UpdatedAt:           order.UpdatedAt.Format(time.RFC3339),
		}

		response = append(response, orderResponse)
	}

	c.JSON(http.StatusOK, OrderListResponse{
		Total: total,
		Data:  response,
	})
}

// GetOrder 获取订单详情
// @Summary 获取订单详情
// @Description 根据ID获取订单详情
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} OrderResponse "订单详情"
// @Failure 404 {object} ErrorResponse "订单不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /orders/{id} [get]
func (h *OrderHandler) GetOrder(c *gin.Context) {
	// 获取订单ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid order ID",
		})
		return
	}

	// 查询订单
	var order entity.Order
	if err := h.db.Preload("Customer").Preload("Items").Preload("Items.Product").Preload("Payments").
		First(&order, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Order not found",
			})
			return
		}
		logger.Error("Failed to get order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get order",
		})
		return
	}

	// 构建订单项响应
	var itemsResponse []OrderItemResponse
	for _, item := range order.Items {
		itemsResponse = append(itemsResponse, OrderItemResponse{
			ID:        item.ID,
			OrderID:   item.OrderID,
			ProductID: item.ProductID,
			Product: ProductInfo{
				ID:    item.Product.ID,
				Code:  item.Product.Code,
				Name:  item.Product.Name,
				Unit:  item.Product.Unit,
				Price: item.Product.Price,
			},
			Quantity:    item.Quantity,
			UnitPrice:   item.UnitPrice,
			Amount:      item.Amount,
			BatchNumber: item.BatchNumber,
			Remarks:     item.Remarks,
			CreatedAt:   item.CreatedAt.Format(time.RFC3339),
			UpdatedAt:   item.UpdatedAt.Format(time.RFC3339),
		})
	}

	// 处理可能为空的日期
	deliveryDate := ""
	if order.DeliveryDate != nil && !order.DeliveryDate.IsZero() {
		deliveryDate = order.DeliveryDate.Format(time.RFC3339)
	}

	// 构建支付记录响应
	var paymentsResponse []PaymentResponse
	for _, payment := range order.Payments {
		paymentsResponse = append(paymentsResponse, PaymentResponse{
			ID:                payment.ID,
			OrderID:           payment.OrderID,
			Amount:            payment.Amount,
			PaymentMethod:     payment.PaymentMethod,
			PaymentMethodText: getPaymentMethodText(payment.PaymentMethod),
			PaymentDate:       payment.PaymentDate.Format(time.RFC3339),
			Remarks:           payment.Remarks,
			CreatedAt:         payment.CreatedAt.Format(time.RFC3339),
			UpdatedAt:         payment.UpdatedAt.Format(time.RFC3339),
		})
	}

	// 计算欠款金额
	unpaidAmount := order.TotalAmount - order.PaidAmount
	if unpaidAmount < 0 {
		unpaidAmount = 0
	}

	// 处理可能为空的日期
	shippedDate := ""
	if order.ShippedDate != nil && !order.ShippedDate.IsZero() {
		shippedDate = order.ShippedDate.Format(time.RFC3339)
	}

	deliveredDate := ""
	if order.DeliveredDate != nil && !order.DeliveredDate.IsZero() {
		deliveredDate = order.DeliveredDate.Format(time.RFC3339)
	}

	outboundDate := ""
	if order.OutboundDate != nil && !order.OutboundDate.IsZero() {
		outboundDate = order.OutboundDate.Format(time.RFC3339)
	}

	// 构建订单响应
	response := OrderResponse{
		ID:          order.ID,
		OrderNumber: order.OrderNumber,
		CustomerID:  order.CustomerID,
		Customer: CustomerInfo{
			ID:            order.Customer.ID,
			Code:          order.Customer.Code,
			Name:          order.Customer.Name,
			ContactPerson: order.Customer.ContactPerson,
			ContactPhone:  order.Customer.ContactPhone,
		},
		OrderDate:           order.OrderDate.Format(time.RFC3339),
		TotalAmount:         order.TotalAmount,
		PaidAmount:          order.PaidAmount,
		UnpaidAmount:        unpaidAmount,
		Status:              order.Status,
		StatusText:          GetOrderStatusText(order.Status),
		Remarks:             order.Remarks,
		DeliveryDate:        deliveryDate,
		DeliveryAddress:     order.DeliveryAddress,
		ContactPerson:       order.ContactPerson,
		ContactPhone:        order.ContactPhone,
		NeedInvoice:         order.NeedInvoice,
		Company:             order.Company,
		IsShipped:           order.IsShipped,
		ShippedDate:         shippedDate,
		IsDelivered:         order.IsDelivered,
		DeliveredDate:       deliveredDate,
		IsOutboundConfirmed: order.IsOutboundConfirmed,
		OutboundDate:        outboundDate,
		PaymentMethod:       order.PaymentMethod,
		Items:               itemsResponse,
		Payments:            paymentsResponse,
		CreatedAt:           order.CreatedAt.Format(time.RFC3339),
		UpdatedAt:           order.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// CreateOrder 创建订单
// @Summary 创建订单
// @Description 创建新订单
// @Tags 订单
// @Accept json
// @Produce json
// @Param request body CreateOrderRequest true "订单信息"
// @Success 201 {object} OrderResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	var req CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("err :", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 解析订单日期
	orderDate, err := time.Parse("2006-01-02", req.OrderDate)
	if err != nil {
		logger.Error("err :", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid order date format. Use YYYY-MM-DD",
		})
		return
	}

	// 解析交付日期（如果有）
	var deliveryDate *time.Time
	if req.DeliveryDate != "" {
		parsedDate, err := time.Parse("2006-01-02", req.DeliveryDate)
		if err != nil {
			logger.Error("err :", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Invalid delivery date format. Use YYYY-MM-DD",
			})
			return
		}
		deliveryDate = &parsedDate
	}

	// 验证客户是否存在
	var customer entity.Customer
	if err := h.db.First(&customer, req.CustomerID).Error; err != nil {
		logger.Error("err :", err)
		if err == gorm.ErrRecordNotFound {
			logger.Error("err :", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Customer not found",
			})
			return
		}
		logger.Error("Failed to get customer", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create order",
		})
		return
	}

	// 生成订单号
	orderNumber := fmt.Sprintf("ORD%s%d", time.Now().Format("20060102"), time.Now().UnixNano()%1000000)

	// 开始事务
	tx := h.db.Begin()
	if tx.Error != nil {
		logger.Error("Failed to begin transaction", tx.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create order",
		})
		return
	}

	// 创建订单
	order := entity.Order{
		OrderNumber:     orderNumber,
		CustomerID:      req.CustomerID,
		OrderDate:       orderDate,
		Status:          entity.Created,
		Remarks:         req.Remarks,
		DeliveryDate:    deliveryDate,
		DeliveryAddress: req.DeliveryAddress,
		ContactPerson:   req.ContactPerson,
		ContactPhone:    req.ContactPhone,
		NeedInvoice:     req.NeedInvoice,
		Company:         req.Company,
	}

	// 不再需要设置CreatedBy字段

	// 保存订单
	if err := tx.Create(&order).Error; err != nil {
		logger.Error("err :", err)
		tx.Rollback()
		logger.Error("Failed to create order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create order",
		})
		return
	}

	// 计算总金额
	var totalAmount float64 = 0

	// 创建订单项
	for _, itemReq := range req.Items {
		// 验证产品是否存在
		var product entity.Product
		if err := tx.First(&product, itemReq.ProductID).Error; err != nil {
			tx.Rollback()
			if err == gorm.ErrRecordNotFound {
				logger.Error("err :", err)
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": fmt.Sprintf("Product with ID %d not found", itemReq.ProductID),
				})
				return
			}
			logger.Error("Failed to get product", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to create order",
			})
			return
		}

		// 计算金额
		amount := itemReq.Quantity * itemReq.UnitPrice

		// 创建订单项
		orderItem := entity.OrderItem{
			OrderID:     order.ID,
			ProductID:   itemReq.ProductID,
			Quantity:    itemReq.Quantity,
			UnitPrice:   itemReq.UnitPrice,
			Amount:      amount,
			BatchNumber: itemReq.BatchNumber,
			Remarks:     itemReq.Remarks,
			// 不再需要设置BaseEntity
		}

		if err := tx.Create(&orderItem).Error; err != nil {
			logger.Error("err :", err)
			tx.Rollback()
			logger.Error("Failed to create order item", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to create order",
			})
			return
		}

		totalAmount += amount
	}

	// 更新订单总金额
	if err := tx.Model(&order).Update("total_amount", totalAmount).Error; err != nil {
		logger.Error("err :", err)
		tx.Rollback()
		logger.Error("Failed to update order total amount", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create order",
		})
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create order",
		})
		return
	}

	// 重新查询完整的订单信息
	var createdOrder entity.Order
	if err := h.db.Preload("Customer").Preload("Items").Preload("Items.Product").Preload("Payments").
		First(&createdOrder, order.ID).Error; err != nil {
		logger.Error("Failed to get created order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Order created but failed to retrieve details",
		})
		return
	}

	// 构建响应
	var itemsResponse []OrderItemResponse
	for _, item := range createdOrder.Items {
		itemsResponse = append(itemsResponse, OrderItemResponse{
			ID:        item.ID,
			OrderID:   item.OrderID,
			ProductID: item.ProductID,
			Product: ProductInfo{
				ID:    item.Product.ID,
				Code:  item.Product.Code,
				Name:  item.Product.Name,
				Unit:  item.Product.Unit,
				Price: item.Product.Price,
			},
			Quantity:    item.Quantity,
			UnitPrice:   item.UnitPrice,
			Amount:      item.Amount,
			BatchNumber: item.BatchNumber,
			Remarks:     item.Remarks,
			CreatedAt:   item.CreatedAt.Format(time.RFC3339),
			UpdatedAt:   item.UpdatedAt.Format(time.RFC3339),
		})
	}

	// 构建支付记录响应
	var paymentsResponse []PaymentResponse
	for _, payment := range createdOrder.Payments {
		paymentsResponse = append(paymentsResponse, PaymentResponse{
			ID:                payment.ID,
			OrderID:           payment.OrderID,
			Amount:            payment.Amount,
			PaymentMethod:     payment.PaymentMethod,
			PaymentMethodText: getPaymentMethodText(payment.PaymentMethod),
			PaymentDate:       payment.PaymentDate.Format(time.RFC3339),
			Remarks:           payment.Remarks,
			CreatedAt:         payment.CreatedAt.Format(time.RFC3339),
			UpdatedAt:         payment.UpdatedAt.Format(time.RFC3339),
		})
	}

	// 计算欠款金额
	unpaidAmount := createdOrder.TotalAmount - createdOrder.PaidAmount
	if unpaidAmount < 0 {
		unpaidAmount = 0
	}

	// 处理可能为空的日期
	deliveryDateStr := ""
	if createdOrder.DeliveryDate != nil && !createdOrder.DeliveryDate.IsZero() {
		deliveryDateStr = createdOrder.DeliveryDate.Format(time.RFC3339)
	}

	shippedDate := ""
	if createdOrder.ShippedDate != nil && !createdOrder.ShippedDate.IsZero() {
		shippedDate = createdOrder.ShippedDate.Format(time.RFC3339)
	}

	deliveredDate := ""
	if createdOrder.DeliveredDate != nil && !createdOrder.DeliveredDate.IsZero() {
		deliveredDate = createdOrder.DeliveredDate.Format(time.RFC3339)
	}

	outboundDate := ""
	if createdOrder.OutboundDate != nil && !createdOrder.OutboundDate.IsZero() {
		outboundDate = createdOrder.OutboundDate.Format(time.RFC3339)
	}

	response := OrderResponse{
		ID:          createdOrder.ID,
		OrderNumber: createdOrder.OrderNumber,
		CustomerID:  createdOrder.CustomerID,
		Customer: CustomerInfo{
			ID:            createdOrder.Customer.ID,
			Code:          createdOrder.Customer.Code,
			Name:          createdOrder.Customer.Name,
			ContactPerson: createdOrder.Customer.ContactPerson,
			ContactPhone:  createdOrder.Customer.ContactPhone,
		},
		OrderDate:           createdOrder.OrderDate.Format(time.RFC3339),
		TotalAmount:         createdOrder.TotalAmount,
		PaidAmount:          createdOrder.PaidAmount,
		UnpaidAmount:        unpaidAmount,
		Status:              createdOrder.Status,
		StatusText:          GetOrderStatusText(createdOrder.Status),
		Remarks:             createdOrder.Remarks,
		DeliveryDate:        deliveryDateStr,
		DeliveryAddress:     createdOrder.DeliveryAddress,
		ContactPerson:       createdOrder.ContactPerson,
		ContactPhone:        createdOrder.ContactPhone,
		NeedInvoice:         createdOrder.NeedInvoice,
		Company:             createdOrder.Company,
		IsShipped:           createdOrder.IsShipped,
		ShippedDate:         shippedDate,
		IsDelivered:         createdOrder.IsDelivered,
		DeliveredDate:       deliveredDate,
		IsOutboundConfirmed: createdOrder.IsOutboundConfirmed,
		OutboundDate:        outboundDate,
		PaymentMethod:       createdOrder.PaymentMethod,
		Items:               itemsResponse,
		Payments:            paymentsResponse,
		CreatedAt:           createdOrder.CreatedAt.Format(time.RFC3339),
		UpdatedAt:           createdOrder.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateOrder 更新订单
// @Summary 更新订单
// @Description 更新订单信息，包括状态、支付金额等
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param request body UpdateOrderRequest true "订单更新信息"
// @Success 200 {object} OrderResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "订单不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /orders/{id} [put]
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	// 获取订单ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid order ID",
		})
		return
	}

	var req UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind request", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 查询订单
	var order entity.Order
	if err := h.db.Preload("Items").Preload("Payments").First(&order, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Order not found",
			})
			return
		}
		logger.Error("Failed to get order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update order",
		})
		return
	}

	// 开始事务
	tx := h.db.Begin()
	if tx.Error != nil {
		logger.Error("Failed to begin transaction", tx.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update order",
		})
		return
	}

	// 更新订单信息
	updates := map[string]any{}

	// 处理状态更新
	if req.Status != nil {
		oldStatus := order.Status
		newStatus := *req.Status

		// 记录状态变更
		logger.Info(fmt.Sprintf("订单状态变更: ID=%d, 旧状态=%d, 新状态=%d",
			order.ID, oldStatus, newStatus))

		// 如果状态从"已创建"变为"已确认"，需要检查库存
		if oldStatus == entity.Created && newStatus == entity.Confirmed {
			// 检查每个订单项的库存是否足够
			for _, item := range order.Items {
				var inventory entity.Inventory
				// 假设默认从ID为1的仓库出库
				warehouseID := uint(1)

				// 查询库存
				result := tx.Where("product_id = ? AND warehouse_id = ?", item.ProductID, warehouseID).First(&inventory)
				if result.Error != nil {
					tx.Rollback()
					if result.Error == gorm.ErrRecordNotFound {
						logger.Error(fmt.Sprintf("产品ID=%d在仓库ID=%d中没有库存", item.ProductID, warehouseID), result.Error)
						c.JSON(http.StatusBadRequest, gin.H{
							"code":    400,
							"message": fmt.Sprintf("Product with ID %d not found in warehouse", item.ProductID),
						})
						return
					}
					logger.Error("Failed to get inventory", result.Error)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update order",
					})
					return
				}

				// 检查库存是否足够
				if inventory.Quantity < item.Quantity {
					tx.Rollback()
					logger.Error(fmt.Sprintf("产品ID=%d库存不足: 需要%.2f, 实际%.2f",
						item.ProductID, item.Quantity, inventory.Quantity), fmt.Errorf("insufficient inventory"))
					c.JSON(http.StatusBadRequest, gin.H{
						"code":    400,
						"message": fmt.Sprintf("Insufficient inventory for product ID %d", item.ProductID),
					})
					return
				}
			}
		}

		// 如果状态从"已确认"变为"已完成"，需要减少库存
		if oldStatus == entity.Confirmed && newStatus == entity.Completed {
			// 处理每个订单项的库存
			for _, item := range order.Items {
				var inventory entity.Inventory
				// 假设默认从ID为1的仓库出库
				warehouseID := uint(1)

				// 查询库存
				result := tx.Where("product_id = ? AND warehouse_id = ?", item.ProductID, warehouseID).First(&inventory)
				if result.Error != nil {
					tx.Rollback()
					if result.Error == gorm.ErrRecordNotFound {
						logger.Error(fmt.Sprintf("产品ID=%d在仓库ID=%d中没有库存", item.ProductID, warehouseID), result.Error)
						c.JSON(http.StatusBadRequest, gin.H{
							"code":    400,
							"message": fmt.Sprintf("Product with ID %d not found in warehouse", item.ProductID),
						})
						return
					}
					logger.Error("Failed to get inventory", result.Error)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update order",
					})
					return
				}

				// 检查库存是否足够
				if inventory.Quantity < item.Quantity {
					tx.Rollback()
					logger.Error(fmt.Sprintf("产品ID=%d库存不足: 需要%.2f, 实际%.2f",
						item.ProductID, item.Quantity, inventory.Quantity), fmt.Errorf("insufficient inventory"))
					c.JSON(http.StatusBadRequest, gin.H{
						"code":    400,
						"message": fmt.Sprintf("Insufficient inventory for product ID %d", item.ProductID),
					})
					return
				}

				// 减少库存
				newQuantity := inventory.Quantity - item.Quantity
				if err := tx.Model(&inventory).Update("quantity", newQuantity).Error; err != nil {
					tx.Rollback()
					logger.Error("Failed to update inventory", err)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update inventory",
					})
					return
				}

				// 创建出库记录
				stockMovement := entity.StockMovement{
					ProductID:         item.ProductID,
					SourceWarehouseID: warehouseID,
					Quantity:          item.Quantity,
					UnitPrice:         item.UnitPrice,
					MovementType:      entity.StockOut,
					Reason:            "订单完成",
					Reference:         order.OrderNumber,
					BatchNumber:       item.BatchNumber,
					MovementDate:      time.Now(),
				}

				if err := tx.Create(&stockMovement).Error; err != nil {
					tx.Rollback()
					logger.Error("Failed to create stock movement", err)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update order",
					})
					return
				}
			}
		}

		updates["status"] = newStatus
	}

	// 处理其他字段更新
	if req.PaidAmount != nil {
		updates["paid_amount"] = *req.PaidAmount
	}

	if req.Remarks != nil {
		updates["remarks"] = *req.Remarks
	}

	if req.DeliveryDate != nil {
		if *req.DeliveryDate == "" {
			updates["delivery_date"] = nil
		} else {
			deliveryDate, err := time.Parse("2006-01-02", *req.DeliveryDate)
			if err != nil {
				tx.Rollback()
				logger.Error("Invalid delivery date format", err)
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "Invalid delivery date format. Use YYYY-MM-DD",
				})
				return
			}
			updates["delivery_date"] = deliveryDate
		}
	}

	if req.DeliveryAddress != nil {
		updates["delivery_address"] = *req.DeliveryAddress
	}

	if req.ContactPerson != nil {
		updates["contact_person"] = *req.ContactPerson
	}

	if req.ContactPhone != nil {
		updates["contact_phone"] = *req.ContactPhone
	}

	if req.NeedInvoice != nil {
		updates["need_invoice"] = *req.NeedInvoice
	}

	if req.Company != nil {
		updates["company"] = *req.Company
	}

	if req.IsShipped != nil {
		updates["is_shipped"] = *req.IsShipped
		if *req.IsShipped && (order.ShippedDate == nil || order.ShippedDate.IsZero()) {
			updates["shipped_date"] = time.Now()
		}
	}

	if req.ShippedDate != nil {
		if *req.ShippedDate == "" {
			updates["shipped_date"] = nil
		} else {
			shippedDate, err := time.Parse("2006-01-02", *req.ShippedDate)
			if err != nil {
				tx.Rollback()
				logger.Error("Invalid shipped date format", err)
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "Invalid shipped date format. Use YYYY-MM-DD",
				})
				return
			}
			updates["shipped_date"] = shippedDate
			updates["is_shipped"] = true
		}
	}

	if req.IsDelivered != nil {
		updates["is_delivered"] = *req.IsDelivered
		if *req.IsDelivered && (order.DeliveredDate == nil || order.DeliveredDate.IsZero()) {
			updates["delivered_date"] = time.Now()
		}
	}

	if req.DeliveredDate != nil {
		if *req.DeliveredDate == "" {
			updates["delivered_date"] = nil
		} else {
			deliveredDate, err := time.Parse("2006-01-02", *req.DeliveredDate)
			if err != nil {
				tx.Rollback()
				logger.Error("Invalid delivered date format", err)
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "Invalid delivered date format. Use YYYY-MM-DD",
				})
				return
			}
			updates["delivered_date"] = deliveredDate
			updates["is_delivered"] = true
		}
	}

	if req.IsOutboundConfirmed != nil {
		// 如果要将订单标记为已出库，需要检查库存
		if *req.IsOutboundConfirmed && !order.IsOutboundConfirmed {
			// 检查每个订单项的库存是否足够
			for _, item := range order.Items {
				var inventory entity.Inventory
				// 假设默认从ID为1的仓库出库
				warehouseID := uint(1)

				// 查询库存
				result := tx.Where("product_id = ? AND warehouse_id = ?", item.ProductID, warehouseID).First(&inventory)
				if result.Error != nil {
					tx.Rollback()
					if result.Error == gorm.ErrRecordNotFound {
						logger.Error(fmt.Sprintf("产品ID=%d在仓库ID=%d中没有库存", item.ProductID, warehouseID), result.Error)
						c.JSON(http.StatusBadRequest, gin.H{
							"code":    400,
							"message": fmt.Sprintf("Product with ID %d not found in warehouse", item.ProductID),
						})
						return
					}
					logger.Error("Failed to get inventory", result.Error)
					c.JSON(http.StatusInternalServerError, gin.H{
						"code":    500,
						"message": "Failed to update order",
					})
					return
				}

				// 检查库存是否足够
				if inventory.Quantity < item.Quantity {
					tx.Rollback()
					logger.Error(fmt.Sprintf("产品ID=%d库存不足: 需要%.2f, 实际%.2f",
						item.ProductID, item.Quantity, inventory.Quantity), fmt.Errorf("insufficient inventory"))
					c.JSON(http.StatusBadRequest, gin.H{
						"code":    400,
						"message": fmt.Sprintf("Insufficient inventory for product ID %d", item.ProductID),
					})
					return
				}
			}
		}

		updates["is_outbound_confirmed"] = *req.IsOutboundConfirmed
		if *req.IsOutboundConfirmed && (order.OutboundDate == nil || order.OutboundDate.IsZero()) {
			updates["outbound_date"] = time.Now()
		}
	}

	if req.OutboundDate != nil {
		if *req.OutboundDate == "" {
			updates["outbound_date"] = nil
		} else {
			outboundDate, err := time.Parse("2006-01-02", *req.OutboundDate)
			if err != nil {
				tx.Rollback()
				logger.Error("Invalid outbound date format", err)
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "Invalid outbound date format. Use YYYY-MM-DD",
				})
				return
			}
			updates["outbound_date"] = outboundDate
			updates["is_outbound_confirmed"] = true
		}
	}

	if req.PaymentMethod != nil {
		updates["payment_method"] = *req.PaymentMethod
	}

	// 处理总金额更新
	if req.TotalAmount != nil {
		updates["total_amount"] = *req.TotalAmount
	}

	// 更新订单
	if err := tx.Model(&order).Updates(updates).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to update order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update order",
		})
		return
	}

	// 处理订单项更新
	if req.Items != nil && len(req.Items) > 0 {
		// 先删除原有的订单项
		if err := tx.Where("order_id = ?", order.ID).Delete(&entity.OrderItem{}).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to delete order items", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to update order items",
			})
			return
		}

		// 创建新的订单项
		for _, itemReq := range req.Items {
			// 验证产品是否存在
			var product entity.Product
			if err := tx.First(&product, itemReq.ProductID).Error; err != nil {
				tx.Rollback()
				if err == gorm.ErrRecordNotFound {
					logger.Error(fmt.Sprintf("产品ID=%d不存在", itemReq.ProductID), err)
					c.JSON(http.StatusBadRequest, gin.H{
						"code":    400,
						"message": fmt.Sprintf("Product with ID %d not found", itemReq.ProductID),
					})
					return
				}
				logger.Error("Failed to get product", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update order",
				})
				return
			}

			// 计算金额
			amount := itemReq.Quantity * itemReq.UnitPrice

			// 创建订单项
			orderItem := entity.OrderItem{
				OrderID:     order.ID,
				ProductID:   itemReq.ProductID,
				Quantity:    itemReq.Quantity,
				UnitPrice:   itemReq.UnitPrice,
				Amount:      amount,
				BatchNumber: itemReq.BatchNumber,
				Remarks:     itemReq.Remarks,
			}

			if err := tx.Create(&orderItem).Error; err != nil {
				tx.Rollback()
				logger.Error("Failed to create order item", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Failed to update order",
				})
				return
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update order",
		})
		return
	}

	// 重新查询完整的订单信息
	var updatedOrder entity.Order
	if err := h.db.Preload("Customer").Preload("Items").Preload("Items.Product").Preload("Payments").
		First(&updatedOrder, id).Error; err != nil {
		logger.Error("Failed to get updated order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Order updated but failed to retrieve details",
		})
		return
	}

	// 构建订单项响应
	var itemsResponse []OrderItemResponse
	for _, item := range updatedOrder.Items {
		itemsResponse = append(itemsResponse, OrderItemResponse{
			ID:        item.ID,
			OrderID:   item.OrderID,
			ProductID: item.ProductID,
			Product: ProductInfo{
				ID:    item.Product.ID,
				Code:  item.Product.Code,
				Name:  item.Product.Name,
				Unit:  item.Product.Unit,
				Price: item.Product.Price,
			},
			Quantity:    item.Quantity,
			UnitPrice:   item.UnitPrice,
			Amount:      item.Amount,
			BatchNumber: item.BatchNumber,
			Remarks:     item.Remarks,
			CreatedAt:   item.CreatedAt.Format(time.RFC3339),
			UpdatedAt:   item.UpdatedAt.Format(time.RFC3339),
		})
	}

	// 构建支付记录响应
	var paymentsResponse []PaymentResponse
	for _, payment := range updatedOrder.Payments {
		paymentsResponse = append(paymentsResponse, PaymentResponse{
			ID:                payment.ID,
			OrderID:           payment.OrderID,
			Amount:            payment.Amount,
			PaymentMethod:     payment.PaymentMethod,
			PaymentMethodText: getPaymentMethodText(payment.PaymentMethod),
			PaymentDate:       payment.PaymentDate.Format(time.RFC3339),
			Remarks:           payment.Remarks,
			CreatedAt:         payment.CreatedAt.Format(time.RFC3339),
			UpdatedAt:         payment.UpdatedAt.Format(time.RFC3339),
		})
	}

	// 计算欠款金额
	unpaidAmount := updatedOrder.TotalAmount - updatedOrder.PaidAmount
	if unpaidAmount < 0 {
		unpaidAmount = 0
	}

	// 处理可能为空的日期
	deliveryDate := ""
	if updatedOrder.DeliveryDate != nil && !updatedOrder.DeliveryDate.IsZero() {
		deliveryDate = updatedOrder.DeliveryDate.Format(time.RFC3339)
	}

	shippedDate := ""
	if updatedOrder.ShippedDate != nil && !updatedOrder.ShippedDate.IsZero() {
		shippedDate = updatedOrder.ShippedDate.Format(time.RFC3339)
	}

	deliveredDate := ""
	if updatedOrder.DeliveredDate != nil && !updatedOrder.DeliveredDate.IsZero() {
		deliveredDate = updatedOrder.DeliveredDate.Format(time.RFC3339)
	}

	outboundDate := ""
	if updatedOrder.OutboundDate != nil && !updatedOrder.OutboundDate.IsZero() {
		outboundDate = updatedOrder.OutboundDate.Format(time.RFC3339)
	}

	// 构建响应
	response := OrderResponse{
		ID:          updatedOrder.ID,
		OrderNumber: updatedOrder.OrderNumber,
		CustomerID:  updatedOrder.CustomerID,
		Customer: CustomerInfo{
			ID:            updatedOrder.Customer.ID,
			Code:          updatedOrder.Customer.Code,
			Name:          updatedOrder.Customer.Name,
			ContactPerson: updatedOrder.Customer.ContactPerson,
			ContactPhone:  updatedOrder.Customer.ContactPhone,
		},
		OrderDate:           updatedOrder.OrderDate.Format(time.RFC3339),
		TotalAmount:         updatedOrder.TotalAmount,
		PaidAmount:          updatedOrder.PaidAmount,
		UnpaidAmount:        unpaidAmount,
		Status:              updatedOrder.Status,
		StatusText:          GetOrderStatusText(updatedOrder.Status),
		Remarks:             updatedOrder.Remarks,
		DeliveryDate:        deliveryDate,
		DeliveryAddress:     updatedOrder.DeliveryAddress,
		ContactPerson:       updatedOrder.ContactPerson,
		ContactPhone:        updatedOrder.ContactPhone,
		NeedInvoice:         updatedOrder.NeedInvoice,
		Company:             updatedOrder.Company,
		IsShipped:           updatedOrder.IsShipped,
		ShippedDate:         shippedDate,
		IsDelivered:         updatedOrder.IsDelivered,
		DeliveredDate:       deliveredDate,
		IsOutboundConfirmed: updatedOrder.IsOutboundConfirmed,
		OutboundDate:        outboundDate,
		PaymentMethod:       updatedOrder.PaymentMethod,
		Items:               itemsResponse,
		Payments:            paymentsResponse,
		CreatedAt:           updatedOrder.CreatedAt.Format(time.RFC3339),
		UpdatedAt:           updatedOrder.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// CreatePayment 创建支付记录
// @Summary 创建支付记录
// @Description 为订单创建支付记录
// @Tags 订单
// @Accept json
// @Produce json
// @Param request body CreatePaymentRequest true "支付记录信息"
// @Success 201 {object} PaymentResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Failure 404 {object} ErrorResponse "订单不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /payments [post]
func (h *OrderHandler) CreatePayment(c *gin.Context) {
	var req CreatePaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind request", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// 解析支付日期
	paymentDate, err := time.Parse("2006-01-02", req.PaymentDate)
	if err != nil {
		logger.Error("Invalid payment date format", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid payment date format. Use YYYY-MM-DD",
		})
		return
	}

	// 查询订单
	var order entity.Order
	if err := h.db.First(&order, req.OrderID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Order not found",
			})
			return
		}
		logger.Error("Failed to get order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create payment",
		})
		return
	}

	// 开始事务
	tx := h.db.Begin()
	if tx.Error != nil {
		logger.Error("Failed to begin transaction", tx.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create payment",
		})
		return
	}

	// 创建支付记录
	payment := entity.Payment{
		OrderID:       req.OrderID,
		Amount:        req.Amount,
		PaymentMethod: req.PaymentMethod,
		PaymentDate:   paymentDate,
		Remarks:       req.Remarks,
	}

	if err := tx.Create(&payment).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to create payment", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create payment",
		})
		return
	}

	// 更新订单已付金额
	newPaidAmount := order.PaidAmount + req.Amount
	updates := map[string]any{
		"paid_amount": newPaidAmount,
	}

	// 检查是否已全部付清，如果是，则自动将订单状态更新为"已完成"
	if newPaidAmount >= order.TotalAmount && order.Status != entity.Completed {
		updates["status"] = entity.Completed
		logger.Info(fmt.Sprintf("订单ID=%d已全部付清，自动更新状态为已完成", order.ID))
	}

	if err := tx.Model(&order).Updates(updates).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to update order paid amount", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create payment",
		})
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create payment",
		})
		return
	}

	// 构建响应
	response := PaymentResponse{
		ID:                payment.ID,
		OrderID:           payment.OrderID,
		Amount:            payment.Amount,
		PaymentMethod:     payment.PaymentMethod,
		PaymentMethodText: getPaymentMethodText(payment.PaymentMethod),
		PaymentDate:       payment.PaymentDate.Format(time.RFC3339),
		Remarks:           payment.Remarks,
		CreatedAt:         payment.CreatedAt.Format(time.RFC3339),
		UpdatedAt:         payment.UpdatedAt.Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

// GetOrderPayments 获取订单支付记录
// @Summary 获取订单支付记录
// @Description 获取指定订单的所有支付记录
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {array} PaymentResponse "支付记录列表"
// @Failure 404 {object} ErrorResponse "订单不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /orders/{id}/payments [get]
func (h *OrderHandler) GetOrderPayments(c *gin.Context) {
	// 获取订单ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid order ID",
		})
		return
	}

	// 查询订单
	var order entity.Order
	if err := h.db.First(&order, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Order not found",
			})
			return
		}
		logger.Error("Failed to get order", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get order payments",
		})
		return
	}

	// 查询支付记录
	var payments []entity.Payment
	if err := h.db.Where("order_id = ?", id).Order("payment_date DESC").Find(&payments).Error; err != nil {
		logger.Error("Failed to get payments", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get order payments",
		})
		return
	}

	// 构建响应
	var response []PaymentResponse
	for _, payment := range payments {
		response = append(response, PaymentResponse{
			ID:                payment.ID,
			OrderID:           payment.OrderID,
			Amount:            payment.Amount,
			PaymentMethod:     payment.PaymentMethod,
			PaymentMethodText: getPaymentMethodText(payment.PaymentMethod),
			PaymentDate:       payment.PaymentDate.Format(time.RFC3339),
			Remarks:           payment.Remarks,
			CreatedAt:         payment.CreatedAt.Format(time.RFC3339),
			UpdatedAt:         payment.UpdatedAt.Format(time.RFC3339),
		})
	}

	c.JSON(http.StatusOK, response)
}
