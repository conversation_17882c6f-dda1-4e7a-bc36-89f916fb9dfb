package handler

import (
	"fmt"
	"testing"
	"time"

	"github.com/yourusername/storehouse/internal/domain/entity"
	"github.com/yourusername/storehouse/internal/infrastructure/persistence"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var db *gorm.DB

// 初始化测试数据库连接
func init() {
	var err error
	// 使用与配置文件相同的数据库连接信息，但使用测试数据库
	dsn := "root:root@tcp(localhost:3306)/storehouse_test?charset=utf8mb4&parseTime=True&loc=Local"
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		panic("Failed to connect to test database: " + err.Error())
	}

	// 自动迁移表结构
	err = persistence.AutoMigrate(db)
	if err != nil {
		panic("Failed to migrate test database: " + err.Error())
	}

	// 确保 payments 表存在
	db.Exec("CREATE TABLE IF NOT EXISTS payments (" +
		"id INT AUTO_INCREMENT PRIMARY KEY," +
		"order_id INT NOT NULL," +
		"amount DECIMAL(18,2) NOT NULL," +
		"payment_method VARCHAR(50) NOT NULL," +
		"payment_date DATETIME NOT NULL," +
		"remarks VARCHAR(500)," +
		"created_at DATETIME NOT NULL," +
		"updated_at DATETIME," +
		"deleted_at DATETIME," +
		"INDEX idx_order_id (order_id)" +
		")")

	// 准备测试数据
	prepareTestData()
}

// 准备测试数据
func prepareTestData() {
	// 清空测试数据
	db.Exec("SET FOREIGN_KEY_CHECKS = 0")
	db.Exec("TRUNCATE TABLE orders")
	db.Exec("TRUNCATE TABLE order_items")
	db.Exec("TRUNCATE TABLE payments")
	db.Exec("TRUNCATE TABLE inventories")
	db.Exec("TRUNCATE TABLE stock_movements")
	db.Exec("TRUNCATE TABLE products")
	db.Exec("TRUNCATE TABLE warehouses")
	db.Exec("TRUNCATE TABLE customers")
	db.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// 创建测试仓库
	warehouse1 := entity.Warehouse{
		Code:          "W001",
		Name:          "测试仓库1",
		Address:       "测试地址1",
		ContactPerson: "测试联系人1",
		ContactPhone:  "13800000001",
		IsActive:      true,
	}
	db.Create(&warehouse1)

	warehouse2 := entity.Warehouse{
		Code:          "W002",
		Name:          "测试仓库2",
		Address:       "测试地址2",
		ContactPerson: "测试联系人2",
		ContactPhone:  "13800000002",
		IsActive:      true,
	}
	db.Create(&warehouse2)

	// 创建测试客户
	customer1 := entity.Customer{
		Code:          "C001",
		Name:          "测试客户A",
		ContactPerson: "测试联系人A",
		ContactPhone:  "13800000001",
		Address:       "测试地址A",
		IsActive:      true,
	}
	db.Create(&customer1)

	customer2 := entity.Customer{
		Code:          "C002",
		Name:          "测试客户B",
		ContactPerson: "测试联系人B",
		ContactPhone:  "13800000002",
		Address:       "测试地址B",
		IsActive:      true,
	}
	db.Create(&customer2)

	// 创建测试商品
	products := []entity.Product{
		{Code: "A001", Name: "商品A", Unit: "个", Price: 100},
		{Code: "B001", Name: "商品B", Unit: "个", Price: 200},
		{Code: "C001", Name: "商品C", Unit: "个", Price: 300},
	}

	for _, product := range products {
		db.Create(&product)
	}

	// 创建测试库存
	inventories := []entity.Inventory{
		{ProductID: 1, WarehouseID: 1, Quantity: 100},
		{ProductID: 2, WarehouseID: 1, Quantity: 50},
		{ProductID: 3, WarehouseID: 1, Quantity: 20},
	}

	for _, inventory := range inventories {
		db.Create(&inventory)
	}
}

// 测试用例1.1：创建基本订单
func TestCreateBasicOrder(t *testing.T) {
	// 准备测试数据
	orderDate := time.Now()
	orderNumber := fmt.Sprintf("TEST%s%d", time.Now().Format("20060102"), time.Now().UnixNano()%1000000)

	// 创建订单
	order := entity.Order{
		OrderNumber: orderNumber,
		CustomerID:  1,
		OrderDate:   orderDate,
		Status:      entity.Created,
		Remarks:     "测试基本订单",
	}

	// 添加订单项
	orderItem := entity.OrderItem{
		ProductID: 1,
		Quantity:  10,
		UnitPrice: 100,
		Amount:    1000,
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	// 保存订单
	if err := tx.Create(&order).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create order: %v", err)
	}

	// 设置订单项的订单ID
	orderItem.OrderID = order.ID

	// 保存订单项
	if err := tx.Create(&orderItem).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create order item: %v", err)
	}

	// 更新订单总金额
	if err := tx.Model(&order).Update("total_amount", orderItem.Amount).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to update order total amount: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
	}

	// 验证订单创建结果
	var createdOrder entity.Order
	if err := db.Preload("Items").First(&createdOrder, order.ID).Error; err != nil {
		t.Fatalf("Failed to get created order: %v", err)
	}

	// 验证订单数据
	if createdOrder.OrderNumber != orderNumber {
		t.Errorf("Expected order number %s, got %s", orderNumber, createdOrder.OrderNumber)
	}

	if createdOrder.CustomerID != 1 {
		t.Errorf("Expected customer ID 1, got %d", createdOrder.CustomerID)
	}

	if createdOrder.Status != entity.Created {
		t.Errorf("Expected order status %d, got %d", entity.Created, createdOrder.Status)
	}

	if createdOrder.TotalAmount != 1000 {
		t.Errorf("Expected total amount 1000, got %f", createdOrder.TotalAmount)
	}

	if len(createdOrder.Items) != 1 {
		t.Errorf("Expected 1 order item, got %d", len(createdOrder.Items))
	}

	// 获取当前库存
	var inventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", 1, 1).First(&inventory).Error; err != nil {
		t.Fatalf("Failed to get inventory: %v", err)
	}

	// 验证库存未变（不检查具体数值，因为其他测试可能已经修改了库存）
	t.Logf("Current inventory quantity: %f", inventory.Quantity)
}

// 测试用例3.4：确认订单出库
func TestConfirmOrderOutbound(t *testing.T) {
	// 准备测试数据
	orderDate := time.Now()
	orderNumber := fmt.Sprintf("TEST%s%d", time.Now().Format("20060102"), time.Now().UnixNano()%1000000)

	// 创建已确认状态的订单
	order := entity.Order{
		OrderNumber: orderNumber,
		CustomerID:  1,
		OrderDate:   orderDate,
		Status:      entity.Confirmed,
		TotalAmount: 1000,
		Remarks:     "测试确认出库",
	}

	// 添加订单项
	orderItem := entity.OrderItem{
		ProductID: 1,
		Quantity:  10,
		UnitPrice: 100,
		Amount:    1000,
	}

	// 保存订单和订单项
	tx := db.Begin()
	if err := tx.Create(&order).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create order: %v", err)
	}

	orderItem.OrderID = order.ID
	if err := tx.Create(&orderItem).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create order item: %v", err)
	}
	tx.Commit()

	// 获取商品初始库存
	var initialInventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", 1, 1).First(&initialInventory).Error; err != nil {
		t.Fatalf("Failed to get initial inventory: %v", err)
	}

	initialQuantity := initialInventory.Quantity

	// 确认订单出库
	outboundDate := time.Now()
	tx = db.Begin()

	// 更新订单出库状态
	if err := tx.Model(&order).Updates(map[string]interface{}{
		"is_outbound_confirmed": true,
		"outbound_date":         outboundDate,
	}).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to update order outbound status: %v", err)
	}

	// 创建出库记录
	stockMovement := entity.StockMovement{
		ProductID:         1,
		SourceWarehouseID: 1,
		Quantity:          10,
		MovementType:      entity.StockOut,
		Reason:            "订单出库",
		Reference:         orderNumber,
		MovementDate:      outboundDate,
		CustomerID:        &order.CustomerID,
	}

	if err := tx.Create(&stockMovement).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create stock movement: %v", err)
	}

	// 更新库存
	if err := tx.Model(&initialInventory).Update("quantity", initialQuantity-10).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to update inventory: %v", err)
	}

	tx.Commit()

	// 验证订单出库状态
	var updatedOrder entity.Order
	if err := db.First(&updatedOrder, order.ID).Error; err != nil {
		t.Fatalf("Failed to get updated order: %v", err)
	}

	if !updatedOrder.IsOutboundConfirmed {
		t.Error("Expected order outbound confirmed to be true")
	}

	// 验证库存减少
	var updatedInventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", 1, 1).First(&updatedInventory).Error; err != nil {
		t.Fatalf("Failed to get updated inventory: %v", err)
	}

	expectedQuantity := initialQuantity - 10
	if updatedInventory.Quantity != expectedQuantity {
		t.Errorf("Expected inventory quantity %f, got %f", expectedQuantity, updatedInventory.Quantity)
	}

	// 验证出库记录
	var movement entity.StockMovement
	if err := db.Where("reference = ?", orderNumber).First(&movement).Error; err != nil {
		t.Fatalf("Failed to get stock movement: %v", err)
	}

	if movement.MovementType != entity.StockOut {
		t.Errorf("Expected movement type %d, got %d", entity.StockOut, movement.MovementType)
	}

	if movement.Quantity != 10 {
		t.Errorf("Expected movement quantity 10, got %f", movement.Quantity)
	}
}
