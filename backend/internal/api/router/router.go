package router

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/yourusername/storehouse/internal/api/handler"
	"github.com/yourusername/storehouse/internal/api/middleware"
	"github.com/yourusername/storehouse/internal/pkg/config"
	"gorm.io/gorm"
)

// InitRouter 初始化路由
func InitRouter(db *gorm.DB) *gin.Engine {
	// 设置运行模式
	if config.GetConfig().App.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// CORS配置
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// 初始化处理器
	productHandler := handler.NewProductHandler(db)
	inventoryHandler := handler.NewInventoryHandler(db)
	orderHandler := handler.NewOrderHandler(db)
	customerHandler := handler.NewCustomerHandler(db)
	stockMovementHandler := handler.NewStockMovementHandler(db)
	warehouseHandler := handler.NewWarehouseHandler(db)
	dashboardHandler := handler.NewDashboardHandler(db)
	reportHandler := handler.NewReportHandler(db)
	authHandler := handler.NewAuthHandler(db)
	settingsHandler := handler.NewSettingsHandler(db)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
		})
	})

	// Swagger文档
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)

			// 需要认证的路由
			authRequired := auth.Group("/")
			authRequired.Use(middleware.JWTAuth())
			{
				authRequired.GET("/user", authHandler.GetUserInfo)
				authRequired.POST("/change-password", authHandler.ChangePassword)
				authRequired.POST("/logout", authHandler.Logout)
			}
		}

		// 需要认证的API路由组
		apiAuth := api.Group("/")
		apiAuth.Use(middleware.JWTAuth())
		{
			// 产品相关路由
			products := apiAuth.Group("/products")
			{
				products.GET("", productHandler.GetProducts)
				products.POST("", productHandler.CreateProduct)
				products.GET("/:id", productHandler.GetProduct)
				products.PUT("/:id", productHandler.UpdateProduct)
				products.DELETE("/:id", productHandler.DeleteProduct)
				products.GET("/:id/inventory", productHandler.GetProductInventory)
			}

			// 库存相关路由
			inventories := apiAuth.Group("/inventories")
			{
				inventories.GET("", inventoryHandler.GetInventories)
				inventories.GET("/:id", inventoryHandler.GetInventory)
			}

			// 订单相关路由
			orders := apiAuth.Group("/orders")
			{
				orders.GET("", orderHandler.GetOrders)
				orders.POST("", orderHandler.CreateOrder)
				orders.GET("/:id", orderHandler.GetOrder)
				orders.PUT("/:id", orderHandler.UpdateOrder)
				orders.GET("/:id/payments", orderHandler.GetOrderPayments)
			}

			// 支付相关路由
			payments := apiAuth.Group("/payments")
			{
				payments.POST("", orderHandler.CreatePayment)
			}

			// 客户相关路由
			customers := apiAuth.Group("/customers")
			{
				customers.GET("", customerHandler.GetCustomers)
				customers.POST("", customerHandler.CreateCustomer)
				customers.GET("/:id", customerHandler.GetCustomer)
				customers.PUT("/:id", customerHandler.UpdateCustomer)
				customers.DELETE("/:id", customerHandler.DeleteCustomer)
			}

			// 出入库记录相关路由
			stockMovements := apiAuth.Group("/stock-movements")
			{
				stockMovements.GET("", stockMovementHandler.GetStockMovements)
				stockMovements.POST("", stockMovementHandler.CreateStockMovement)
				stockMovements.GET("/:id", stockMovementHandler.GetStockMovement)
				stockMovements.GET("/export", stockMovementHandler.ExportStockMovements)
			}

			// 仓库相关路由
			warehouses := apiAuth.Group("/warehouses")
			{
				warehouses.GET("", warehouseHandler.GetWarehouses)
				warehouses.POST("", warehouseHandler.CreateWarehouse)
				warehouses.GET("/:id", warehouseHandler.GetWarehouse)
				warehouses.PUT("/:id", warehouseHandler.UpdateWarehouse)
				warehouses.DELETE("/:id", warehouseHandler.DeleteWarehouse)
				warehouses.GET("/:id/inventory", warehouseHandler.GetWarehouseInventory)
			}

			// 仪表盘相关路由
			dashboard := apiAuth.Group("/dashboard")
			{
				dashboard.GET("/overview", dashboardHandler.GetDashboardOverview)
				dashboard.GET("/inventory-alert", dashboardHandler.GetInventoryAlert)
				dashboard.GET("/recent-orders", dashboardHandler.GetRecentOrders)
			}

			// 用户相关路由
			users := apiAuth.Group("/users")
			{
				users.GET("", authHandler.GetUsers)
			}

			// 报表相关路由
			reports := apiAuth.Group("/reports")
			{
				reports.GET("/sales", reportHandler.GetSalesReport)
				reports.GET("/sales/export", reportHandler.ExportSalesReport)
				reports.GET("/inventory", reportHandler.GetInventoryReport)
				reports.GET("/inventory/export", reportHandler.ExportInventoryReport)
				reports.GET("/customers", reportHandler.GetCustomerReport)
				reports.GET("/customers/export", reportHandler.ExportCustomerReport)
				reports.GET("/salespersons", reportHandler.GetSalesPersonReport)
				reports.GET("/salespersons/export", reportHandler.ExportSalesPersonReport)
			}

			// 系统设置相关路由
			apiAuth.GET("/settings", settingsHandler.GetSystemSettings)
			apiAuth.PUT("/settings", settingsHandler.UpdateSystemSettings)
		}
	}

	return r
}
