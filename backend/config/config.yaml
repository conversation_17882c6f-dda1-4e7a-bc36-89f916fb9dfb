app:
  name: storehouse-api
  version: 0.1.0
  mode: test # development, production
  port: 8888
  timeout: 30 # seconds
  jwt:
    secret: your-secret-key-change-in-production
    expire: 24 # hours
    issuer: storehouse-api

database:
  driver: mysql
  host: localhost
  port: 3306
  username: root
  password: root
  dbname: storehouse
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600 # seconds

log:
  level: debug # debug, info, warn, error
  format: json # json, console
  output: stdout # stdout, file
  file_path: ./logs/app.log
