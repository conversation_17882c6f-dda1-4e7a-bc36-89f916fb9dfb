# 仓库管理系统后端

基于Golang + Gin + GORM + MySQL的仓库管理系统后端项目。

## 技术栈

- Golang 1.20+
- Gin Web框架
- GORM ORM框架
- MySQL 数据库
- JWT 认证
- Swagger API文档

## 项目结构

```
backend/
├── cmd/
│   └── api/                # 应用入口
├── config/                 # 配置文件
├── internal/
│   ├── api/                # API层
│   │   ├── handler/        # 请求处理器
│   │   ├── middleware/     # 中间件
│   │   └── router/         # 路由定义
│   ├── domain/             # 领域层
│   │   ├── entity/         # 实体定义
│   │   ├── repository/     # 仓储接口
│   │   └── service/        # 领域服务
│   ├── infrastructure/     # 基础设施层
│   │   ├── persistence/    # 持久化实现
│   │   └── auth/           # 认证相关
│   └── pkg/                # 内部公共包
│       ├── logger/         # 日志工具
│       ├── util/           # 工具函数
│       └── errors/         # 错误处理
├── pkg/                    # 公共包
├── scripts/                # 脚本文件
├── go.mod                  # Go模块定义
└── go.sum                  # Go依赖校验
```

## 开发指南

### 环境要求

- Go 1.20+
- MySQL 8.0+

### 配置数据库

1. 创建MySQL数据库
```sql
CREATE DATABASE storehouse CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改配置文件 `config/config.yaml`

### 运行项目

```bash
# 安装依赖
go mod tidy

# 运行项目
go run cmd/api/main.go

# 或者构建后运行
go build -o storehouse-api cmd/api/main.go
./storehouse-api
```

### API文档

启动服务后，访问 http://localhost:8080/swagger/index.html 查看API文档。
