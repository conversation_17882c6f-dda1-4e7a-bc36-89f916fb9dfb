# Nginx 基本配置详解

本章将详细介绍 Nginx 的配置文件结构、基本指令和常用配置模式。

## Nginx 配置文件结构

Nginx 的主配置文件通常位于 `/etc/nginx/nginx.conf`。配置文件采用层次化结构，由多个上下文（contexts）组成。

```mermaid
graph TD
    A[主配置文件 nginx.conf] --> B[全局配置]
    A --> C[events 上下文]
    A --> D[http 上下文]
    D --> E[server 上下文]
    E --> F[location 上下文]
    A --> G[mail 上下文]
    A --> H[stream 上下文]
```

### 配置文件层次结构

```
# 全局配置
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# events 上下文
events {
    worker_connections 1024;
}

# http 上下文
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 包含其他配置文件
    include /etc/nginx/conf.d/*.conf;
    
    # server 上下文
    server {
        listen 80;
        server_name example.com;
        
        # location 上下文
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
        }
    }
}
```

## 配置文件可视化

下面是 Nginx 配置文件的层次结构可视化：

```mermaid
graph TB
    subgraph "全局配置"
    A1[user]
    A2[worker_processes]
    A3[error_log]
    A4[pid]
    end
    
    subgraph "events 上下文"
    B1[worker_connections]
    end
    
    subgraph "http 上下文"
    C1[include mime.types]
    C2[default_type]
    C3[log_format]
    C4[access_log]
    C5[include conf.d/*.conf]
    
    subgraph "server 上下文"
    D1[listen]
    D2[server_name]
    
    subgraph "location 上下文"
    E1[root]
    E2[index]
    end
    end
    end
    
    A1 --> B1
    A2 --> B1
    B1 --> C1
    C1 --> D1
    D1 --> E1
```

## 全局配置指令

全局配置指令影响 Nginx 的整体行为：

| 指令 | 描述 | 示例 |
|------|------|------|
| `user` | 指定 Nginx 工作进程运行的用户和组 | `user nginx;` |
| `worker_processes` | 工作进程数量 | `worker_processes auto;` |
| `error_log` | 错误日志路径和级别 | `error_log /var/log/nginx/error.log warn;` |
| `pid` | 存储主进程 PID 的文件 | `pid /run/nginx.pid;` |
| `include` | 包含其他配置文件 | `include /etc/nginx/modules-enabled/*.conf;` |

## events 上下文

events 上下文用于配置连接处理：

| 指令 | 描述 | 示例 |
|------|------|------|
| `worker_connections` | 每个工作进程的最大连接数 | `worker_connections 1024;` |
| `multi_accept` | 一次接受所有新连接 | `multi_accept on;` |
| `use` | 指定事件模型 | `use epoll;` |

## http 上下文

http 上下文包含处理 HTTP 和 HTTPS 流量的配置：

| 指令 | 描述 | 示例 |
|------|------|------|
| `include` | 包含 MIME 类型定义 | `include /etc/nginx/mime.types;` |
| `default_type` | 默认 MIME 类型 | `default_type application/octet-stream;` |
| `log_format` | 定义日志格式 | `log_format main '...';` |
| `access_log` | 访问日志路径和格式 | `access_log /var/log/nginx/access.log main;` |
| `sendfile` | 启用 sendfile 系统调用 | `sendfile on;` |
| `tcp_nopush` | 优化数据包发送 | `tcp_nopush on;` |
| `keepalive_timeout` | 保持连接超时时间 | `keepalive_timeout 65;` |
| `gzip` | 启用 gzip 压缩 | `gzip on;` |

## server 上下文

server 上下文定义虚拟服务器：

| 指令 | 描述 | 示例 |
|------|------|------|
| `listen` | 监听的 IP 地址和端口 | `listen 80;` |
| `server_name` | 服务器名称（域名） | `server_name example.com www.example.com;` |
| `root` | 网站根目录 | `root /usr/share/nginx/html;` |
| `index` | 默认索引文件 | `index index.html index.htm;` |
| `error_page` | 自定义错误页面 | `error_page 404 /404.html;` |
| `return` | 返回特定状态码或重定向 | `return 301 https://$host$request_uri;` |

## location 上下文

location 上下文定义如何处理特定的 URI：

| 指令 | 描述 | 示例 |
|------|------|------|
| `root` | 请求的根目录 | `root /usr/share/nginx/html;` |
| `alias` | 替换 URI 的路径 | `alias /path/to/files;` |
| `index` | 默认索引文件 | `index index.html;` |
| `try_files` | 尝试多个文件路径 | `try_files $uri $uri/ /index.html;` |
| `proxy_pass` | 代理请求到另一个服务器 | `proxy_pass http://backend;` |
| `fastcgi_pass` | 将请求传递给 FastCGI 服务器 | `fastcgi_pass 127.0.0.1:9000;` |

## location 匹配规则

location 指令使用不同的修饰符来匹配 URI：

```mermaid
graph TD
    A[location 匹配顺序] --> B[精确匹配 =]
    A --> C[前缀匹配 ^~]
    A --> D[正则匹配 ~或~*]
    A --> E[普通前缀匹配 无修饰符]
```

| 修饰符 | 描述 | 示例 |
|------|------|------|
| `=` | 精确匹配 | `location = /favicon.ico { ... }` |
| `^~` | 优先前缀匹配 | `location ^~ /assets/ { ... }` |
| `~` | 区分大小写的正则匹配 | `location ~ \.php$ { ... }` |
| `~*` | 不区分大小写的正则匹配 | `location ~* \.(jpg|jpeg|png)$ { ... }` |
| 无修饰符 | 普通前缀匹配 | `location /api/ { ... }` |

## 变量

Nginx 提供了许多内置变量，可以在配置中使用：

| 变量 | 描述 |
|------|------|
| `$uri` | 当前请求的 URI（已规范化） |
| `$request_uri` | 原始请求 URI |
| `$host` | 请求头中的 Host 字段 |
| `$remote_addr` | 客户端 IP 地址 |
| `$args` | 请求参数 |
| `$http_user_agent` | User-Agent 请求头 |
| `$scheme` | 请求协议（http 或 https） |
| `$server_name` | 匹配的 server_name |
| `$request_method` | 请求方法（GET、POST 等） |

## 包含文件

Nginx 配置通常分散在多个文件中，使用 `include` 指令包含：

```
# 在 nginx.conf 中
include /etc/nginx/conf.d/*.conf;
include /etc/nginx/sites-enabled/*;
```

这种模块化方法使配置更易于管理。

## 配置示例

### 基本静态网站

```nginx
server {
    listen 80;
    server_name example.com www.example.com;
    
    root /var/www/example;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
}
```

### 简单的反向代理

```nginx
server {
    listen 80;
    server_name api.example.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 配置测试和重载

修改配置后，应该先测试配置文件的语法，然后再重新加载：

```bash
# 测试配置
sudo nginx -t

# 重新加载配置
sudo nginx -s reload
```

## 总结

本章介绍了 Nginx 配置文件的结构和基本指令。理解这些基础知识对于配置和优化 Nginx 至关重要。在下一章中，我们将深入探讨 HTTP 服务器配置。
