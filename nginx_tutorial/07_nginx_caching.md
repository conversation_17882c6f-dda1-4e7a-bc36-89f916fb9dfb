# Nginx 缓存机制与配置

本章将详细介绍 Nginx 的缓存功能，包括代理缓存、微缓存、浏览器缓存控制、缓存清除和缓存优化策略。

## 缓存的重要性

缓存是提高 Web 应用性能的关键技术，它可以：
- 减少后端服务器负载
- 降低响应时间
- 减少网络带宽使用
- 提高用户体验
- 增强系统可扩展性

Nginx 提供了多种缓存机制，可以在不同层面实现缓存。

## Nginx 缓存架构

```mermaid
graph TD
    A[客户端请求] --> B[Nginx]
    B --> C{缓存中存在?}
    C -->|是| D[从缓存提供响应]
    C -->|否| E[转发到后端服务器]
    E --> F[后端处理请求]
    F --> G[返回响应]
    G --> H[存储到缓存]
    H --> I[返回响应给客户端]
    D --> I
```

## 代理缓存配置

Nginx 的代理缓存功能允许缓存后端服务器的响应，减少对后端的请求。

### 基本缓存配置

```nginx
# 定义缓存区域
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g 
                 inactive=60m use_temp_path=off;

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_cache my_cache;
        proxy_pass http://backend;
        
        # 缓存设置
        proxy_cache_valid 200 302 10m;    # 对 200 和 302 响应缓存 10 分钟
        proxy_cache_valid 404 1m;         # 对 404 响应缓存 1 分钟
        proxy_cache_key $scheme$host$request_uri;  # 缓存键
        
        # 添加缓存状态头
        add_header X-Cache-Status $upstream_cache_status;
    }
}
```

### proxy_cache_path 参数详解

- `levels=1:2`：缓存目录的层级结构，这里是两级目录
- `keys_zone=my_cache:10m`：共享内存区域名称和大小，用于存储缓存键和元数据
- `max_size=10g`：缓存的最大大小
- `inactive=60m`：如果在此时间内未被访问，缓存项将被删除
- `use_temp_path=off`：禁用临时文件路径，直接写入缓存目录，提高性能

### 缓存键配置

缓存键决定了如何识别和存储缓存项：

```nginx
# 基本缓存键
proxy_cache_key $scheme$host$request_uri;

# 包含查询参数的缓存键
proxy_cache_key $scheme$host$request_uri$is_args$args;

# 包含请求方法的缓存键
proxy_cache_key $scheme$host$request_method$request_uri;

# 包含 Cookie 的缓存键
proxy_cache_key $scheme$host$request_uri$cookie_user;
```

### 缓存有效期

可以为不同的响应状态码设置不同的缓存时间：

```nginx
# 基于状态码的缓存时间
proxy_cache_valid 200 302 10m;
proxy_cache_valid 404 1m;
proxy_cache_valid any 5m;  # 其他状态码

# 使用后端响应头中的 Cache-Control 和 Expires
proxy_cache_valid any 0;
```

## 缓存控制

### 条件缓存

可以根据请求或响应的特定条件决定是否缓存：

```nginx
# 不缓存带有特定 Cookie 的请求
proxy_no_cache $cookie_nocache $arg_nocache $arg_comment;

# 不缓存带有特定请求头的请求
proxy_no_cache $http_pragma $http_authorization;

# 绕过缓存（即使缓存中有响应也不使用）
proxy_cache_bypass $cookie_nocache $arg_nocache $arg_comment;
```

### 缓存锁定

防止多个相同请求同时发送到后端服务器（缓存雪崩）：

```nginx
# 启用缓存锁
proxy_cache_lock on;
proxy_cache_lock_timeout 5s;  # 锁定超时时间
```

### 缓存后台更新

允许在缓存过期后继续提供旧缓存，同时在后台更新缓存：

```nginx
# 启用后台更新
proxy_cache_use_stale updating;

# 在各种错误情况下使用过期缓存
proxy_cache_use_stale error timeout invalid_header updating
                       http_500 http_502 http_503 http_504;
```

### 缓存重新验证

使用条件请求重新验证缓存：

```nginx
# 启用条件请求
proxy_cache_revalidate on;
```

## 微缓存

微缓存是一种短期缓存策略，适用于频繁变化但可以接受短暂延迟的动态内容：

```nginx
location / {
    proxy_cache my_cache;
    proxy_cache_valid 200 302 1s;  # 只缓存 1 秒
    proxy_cache_key $scheme$host$request_uri$is_args$args;
    proxy_pass http://backend;
}
```

微缓存流程：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Nginx as Nginx
    participant Backend as 后端服务器
    
    Client->>Nginx: 请求 1
    Nginx->>Backend: 转发请求
    Backend->>Nginx: 返回响应
    Note over Nginx: 缓存响应 (1秒)
    Nginx->>Client: 返回响应
    
    Client->>Nginx: 请求 2 (1秒内)
    Note over Nginx: 使用缓存
    Nginx->>Client: 返回缓存响应
    
    Client->>Nginx: 请求 3 (1秒后)
    Nginx->>Backend: 转发请求
    Backend->>Nginx: 返回响应
    Note over Nginx: 更新缓存
    Nginx->>Client: 返回响应
```

## 浏览器缓存控制

Nginx 可以通过设置响应头来控制浏览器缓存：

```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    # 设置过期时间
    expires 30d;
    
    # 或使用更详细的 Cache-Control 头
    add_header Cache-Control "public, max-age=2592000, immutable";
    
    # 添加 ETag 和 Last-Modified
    etag on;
    if_modified_since exact;
}

location ~* \.(html|xml)$ {
    expires 1h;
    add_header Cache-Control "public, must-revalidate";
}

location /api/ {
    # 禁止缓存
    expires -1;
    add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate";
    add_header Pragma "no-cache";
}
```

### Cache-Control 指令

常用的 Cache-Control 指令：

| 指令 | 描述 |
|------|------|
| `public` | 响应可以被任何缓存存储 |
| `private` | 响应只能被浏览器缓存，不能被中间缓存存储 |
| `no-cache` | 每次使用缓存前必须重新验证 |
| `no-store` | 不缓存响应 |
| `max-age=<seconds>` | 缓存的最大有效时间 |
| `s-maxage=<seconds>` | 覆盖共享缓存的 max-age |
| `must-revalidate` | 过期后必须重新验证 |
| `immutable` | 表示响应内容不会改变 |

## 缓存清除和刷新

### 手动清除缓存

可以通过删除缓存文件来清除缓存：

```bash
rm -rf /var/cache/nginx/*
```

### 使用 proxy_cache_purge 模块

使用第三方模块 `ngx_cache_purge` 可以通过 HTTP 请求清除特定缓存：

```nginx
# 安装模块后配置
location ~ /purge(/.*) {
    proxy_cache_purge my_cache $scheme$host$1;
    allow 127.0.0.1;
    deny all;
}
```

然后可以通过请求清除缓存：

```bash
curl -X PURGE http://example.com/purge/path/to/resource
```

### 使用 Nginx Plus 的 API

Nginx Plus 提供了 API 来管理缓存：

```nginx
location /api {
    api;
    allow 127.0.0.1;
    deny all;
}
```

然后可以使用 API 清除缓存：

```bash
# 清除特定缓存区域的所有缓存
curl -X DELETE http://localhost/api/6/http/caches/my_cache

# 清除特定缓存键
curl -X DELETE http://localhost/api/6/http/caches/my_cache/keys?key=example.com/path
```

## 缓存分片

对于大文件，可以使用缓存分片提高性能：

```nginx
# 启用分片缓存
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g 
                 inactive=60m use_temp_path=off;

location / {
    proxy_cache my_cache;
    proxy_cache_valid 200 302 60m;
    
    # 启用分片
    slice 1m;
    proxy_cache_key $scheme$host$uri$is_args$args$slice_range;
    proxy_set_header Range $slice_range;
    proxy_http_version 1.1;
    proxy_cache_valid 206 60m;
}
```

分片缓存流程：

```mermaid
graph TD
    A[客户端请求大文件] --> B[Nginx 分割请求]
    B --> C[分片 1 请求]
    B --> D[分片 2 请求]
    B --> E[分片 n 请求]
    C --> F[缓存分片 1]
    D --> G[缓存分片 2]
    E --> H[缓存分片 n]
    F --> I[组合响应]
    G --> I
    H --> I
    I --> J[返回完整文件]
```

## 缓存监控和调试

### 缓存状态头

添加 `X-Cache-Status` 头可以帮助调试缓存：

```nginx
add_header X-Cache-Status $upstream_cache_status;
```

可能的状态值：
- `MISS`：缓存中没有找到响应
- `HIT`：从缓存中提供响应
- `EXPIRED`：缓存中的响应已过期
- `STALE`：提供了过期的响应（与 proxy_cache_use_stale 配合使用）
- `UPDATING`：内容正在更新，提供了旧内容
- `REVALIDATED`：缓存内容已被重新验证为新鲜
- `BYPASS`：缓存被绕过

### 缓存统计

使用 Nginx Plus 可以获取缓存统计信息：

```nginx
location /api {
    api;
    allow 127.0.0.1;
    deny all;
}
```

然后可以查询缓存统计：

```bash
curl http://localhost/api/6/http/caches/my_cache
```

## 缓存优化策略

### 1. 合理设置缓存大小

```nginx
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:20m max_size=20g;
```

根据服务器内存和磁盘空间调整 `keys_zone` 和 `max_size`。

### 2. 使用多级缓存

```nginx
# 小文件使用内存缓存
location ~* \.(css|js|jpg|jpeg|png|gif|ico)$ {
    proxy_cache small_cache;
    proxy_cache_valid 200 302 30d;
}

# 大文件使用磁盘缓存
location ~* \.(mp4|zip|pdf)$ {
    proxy_cache large_cache;
    proxy_cache_valid 200 302 1d;
}
```

### 3. 根据内容类型设置不同的缓存策略

```nginx
# 静态内容长时间缓存
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    proxy_cache static_cache;
    proxy_cache_valid 200 302 30d;
}

# API 响应短时间缓存
location /api/ {
    proxy_cache api_cache;
    proxy_cache_valid 200 302 1m;
}

# 动态内容微缓存
location / {
    proxy_cache dynamic_cache;
    proxy_cache_valid 200 302 1s;
}
```

### 4. 使用 stale 缓存提高可用性

```nginx
proxy_cache_use_stale error timeout invalid_header updating
                       http_500 http_502 http_503 http_504;
```

### 5. 避免缓存私有内容

```nginx
# 不缓存带有授权信息的请求
proxy_no_cache $http_authorization;

# 不缓存登录页面
location /login {
    proxy_no_cache 1;
    proxy_cache_bypass 1;
    proxy_pass http://backend;
}
```

## 实际应用示例

### 1. 静态网站缓存

```nginx
proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=10g 
                 inactive=60m use_temp_path=off;

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_cache static_cache;
        proxy_cache_valid 200 302 60m;
        proxy_cache_valid 404 1m;
        proxy_pass http://backend;
        add_header X-Cache-Status $upstream_cache_status;
    }
    
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        proxy_cache static_cache;
        proxy_cache_valid 200 302 30d;
        proxy_cache_valid 404 1m;
        proxy_pass http://backend;
        add_header X-Cache-Status $upstream_cache_status;
        
        # 启用浏览器缓存
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }
}
```

### 2. API 缓存

```nginx
proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=1g 
                 inactive=10m use_temp_path=off;

server {
    listen 80;
    server_name api.example.com;
    
    # 只缓存 GET 请求
    proxy_cache_methods GET;
    
    location /api/v1/ {
        proxy_cache api_cache;
        proxy_cache_valid 200 302 1m;
        proxy_cache_key $scheme$host$request_uri;
        proxy_pass http://backend;
        add_header X-Cache-Status $upstream_cache_status;
        
        # 不缓存带有特定参数的请求
        proxy_no_cache $arg_nocache $arg_refresh;
        proxy_cache_bypass $arg_nocache $arg_refresh;
    }
}
```

## 总结

本章介绍了 Nginx 的缓存机制和配置，包括代理缓存、微缓存、浏览器缓存控制、缓存清除和优化策略。合理配置缓存可以显著提高 Web 应用的性能和可扩展性。在下一章中，我们将探讨 Nginx 的 SSL/HTTPS 配置。
