# 负载均衡配置与策略

本章将详细介绍 Nginx 的负载均衡功能，包括不同的负载均衡算法、配置方法、会话保持和健康检查等。

## 什么是负载均衡？

负载均衡是将工作负载分布到多个服务器的过程，以提高应用程序的可用性、可靠性和性能。Nginx 可以作为高效的负载均衡器，将客户端请求分发到多个后端服务器。

```mermaid
graph TD
    A[客户端] --> B[Nginx 负载均衡器]
    B -->|分发请求| C[服务器 1]
    B -->|分发请求| D[服务器 2]
    B -->|分发请求| E[服务器 3]
    C --> B
    D --> B
    E --> B
    B --> A
```

## 负载均衡的优势

1. **高可用性**：单个服务器故障不会导致整个系统不可用
2. **可扩展性**：可以轻松添加或移除服务器以应对负载变化
3. **性能优化**：将请求分散到多个服务器，提高响应速度
4. **维护便利**：可以在不中断服务的情况下对单个服务器进行维护
5. **地理分布**：可以将请求路由到地理位置最近的服务器

## 基本负载均衡配置

Nginx 使用 `upstream` 模块定义后端服务器组，然后在 `proxy_pass` 指令中引用：

```nginx
# 定义上游服务器组
upstream backend {
    server backend1.example.com;
    server backend2.example.com;
    server backend3.example.com;
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 负载均衡算法

Nginx 提供多种负载均衡算法，可以根据不同的需求选择：

```mermaid
graph TD
    A[负载均衡算法] --> B[轮询]
    A --> C[加权轮询]
    A --> D[IP 哈希]
    A --> E[最少连接]
    A --> F[加权最少连接]
    A --> G[随机]
```

### 1. 轮询（默认）

按顺序将请求分配给后端服务器：

```nginx
upstream backend {
    server backend1.example.com;
    server backend2.example.com;
    server backend3.example.com;
}
```

### 2. 加权轮询

根据权重分配请求，权重越高的服务器接收的请求越多：

```nginx
upstream backend {
    server backend1.example.com weight=5;  # 50% 的请求
    server backend2.example.com weight=3;  # 30% 的请求
    server backend3.example.com weight=2;  # 20% 的请求
}
```

加权轮询适用于服务器性能不均衡的情况。

### 3. IP 哈希

根据客户端 IP 地址的哈希值将请求分配给固定的后端服务器：

```nginx
upstream backend {
    ip_hash;
    server backend1.example.com;
    server backend2.example.com;
    server backend3.example.com;
}
```

IP 哈希适用于需要会话保持的应用。

### 4. 最少连接

将请求发送到当前活动连接数最少的服务器：

```nginx
upstream backend {
    least_conn;
    server backend1.example.com;
    server backend2.example.com;
    server backend3.example.com;
}
```

最少连接适用于请求处理时间差异较大的场景。

### 5. 加权最少连接

结合权重和最少连接数的算法：

```nginx
upstream backend {
    least_conn;
    server backend1.example.com weight=5;
    server backend2.example.com weight=3;
    server backend3.example.com weight=2;
}
```

### 6. 随机

随机选择后端服务器：

```nginx
upstream backend {
    random;
    server backend1.example.com;
    server backend2.example.com;
    server backend3.example.com;
}
```

可以指定两次随机选择，选择连接数较少的服务器：

```nginx
upstream backend {
    random two;
    server backend1.example.com;
    server backend2.example.com;
    server backend3.example.com;
}
```

## 会话保持

在某些应用中，需要确保来自同一客户端的请求始终发送到同一服务器。Nginx 提供多种会话保持方法：

### 1. IP 哈希

如前所述，使用 `ip_hash` 指令：

```nginx
upstream backend {
    ip_hash;
    server backend1.example.com;
    server backend2.example.com;
}
```

### 2. 粘性 Cookie

使用 `sticky cookie` 指令（需要 Nginx Plus）：

```nginx
upstream backend {
    sticky cookie srv_id expires=1h domain=.example.com path=/;
    server backend1.example.com;
    server backend2.example.com;
}
```

### 3. 一致性哈希

使用 `hash` 指令基于自定义键进行哈希：

```nginx
upstream backend {
    hash $cookie_jsessionid consistent;
    server backend1.example.com;
    server backend2.example.com;
}
```

这里使用 `consistent` 参数启用一致性哈希，减少服务器添加或移除时的重新分配。

## 服务器权重和状态

### 服务器权重

如前所述，可以使用 `weight` 参数设置服务器权重：

```nginx
upstream backend {
    server backend1.example.com weight=5;
    server backend2.example.com weight=3;
}
```

### 服务器状态标记

可以使用以下参数标记服务器状态：

```nginx
upstream backend {
    server backend1.example.com;
    server backend2.example.com down;         # 标记为不可用
    server backend3.example.com backup;       # 备用服务器，仅在主服务器不可用时使用
    server backend4.example.com max_fails=3 fail_timeout=30s;  # 失败检测
}
```

- `down`：标记服务器为永久不可用
- `backup`：标记为备用服务器
- `max_fails`：允许的最大失败次数
- `fail_timeout`：在指定时间内达到最大失败次数后，服务器被标记为不可用的时间

## 健康检查

### 被动健康检查

Nginx 开源版提供被动健康检查，通过监控请求失败来检测服务器健康状态：

```nginx
upstream backend {
    server backend1.example.com max_fails=3 fail_timeout=30s;
    server backend2.example.com max_fails=3 fail_timeout=30s;
}
```

当服务器连续失败 `max_fails` 次后，将被标记为不可用 `fail_timeout` 秒。

### 主动健康检查

Nginx Plus 提供主动健康检查，定期发送请求检测服务器状态：

```nginx
upstream backend {
    zone backend 64k;
    
    server backend1.example.com;
    server backend2.example.com;
    
    # 健康检查配置
    health_check interval=5s fails=3 passes=2;
}
```

## 动态服务器组

Nginx Plus 支持通过 API 动态添加或移除上游服务器：

```nginx
upstream backend {
    zone backend 64k;  # 定义共享内存区域
    server backend1.example.com;
    server backend2.example.com;
}

server {
    listen 8080;
    
    location /api {
        api;
        allow 127.0.0.1;
        deny all;
    }
}
```

然后可以使用 API 添加或移除服务器：

```bash
# 添加服务器
curl -X POST -d '{"server":"backend3.example.com"}' \
     http://localhost:8080/api/6/http/upstreams/backend/servers

# 移除服务器
curl -X DELETE \
     http://localhost:8080/api/6/http/upstreams/backend/servers/1
```

## 慢启动

Nginx Plus 支持慢启动功能，使新添加或恢复的服务器逐渐增加负载：

```nginx
upstream backend {
    zone backend 64k;
    
    server backend1.example.com slow_start=30s;
    server backend2.example.com slow_start=30s;
}
```

服务器在 30 秒内逐渐增加权重，避免突然接收大量请求。

## 负载均衡多层架构

可以配置多层负载均衡，例如前端和后端分别使用不同的负载均衡策略：

```mermaid
graph TD
    A[客户端] --> B[Nginx 前端负载均衡]
    B -->|静态内容| C[静态服务器组]
    B -->|动态内容| D[Nginx 应用负载均衡]
    D --> E[应用服务器 1]
    D --> F[应用服务器 2]
    E --> G[数据库负载均衡]
    F --> G
    G --> H[数据库 1]
    G --> I[数据库 2]
```

配置示例：

```nginx
# 前端服务器组
upstream static_servers {
    server static1.example.com;
    server static2.example.com;
}

# 应用服务器组
upstream app_servers {
    ip_hash;  # 会话保持
    server app1.example.com;
    server app2.example.com;
}

server {
    listen 80;
    server_name example.com;
    
    # 静态内容
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        proxy_pass http://static_servers;
    }
    
    # 动态内容
    location / {
        proxy_pass http://app_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 负载均衡与缓存结合

结合负载均衡和缓存可以进一步提高性能：

```nginx
# 缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=cache:10m max_size=10g inactive=60m;

# 上游服务器组
upstream backend {
    server backend1.example.com;
    server backend2.example.com;
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        # 缓存配置
        proxy_cache cache;
        proxy_cache_valid 200 302 10m;
        proxy_cache_valid 404 1m;
        
        # 负载均衡
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 监控负载均衡

### 基本状态监控

使用 `stub_status` 模块监控 Nginx 状态：

```nginx
server {
    listen 8080;
    
    location /status {
        stub_status;
        allow 127.0.0.1;
        deny all;
    }
}
```

### 高级监控（Nginx Plus）

Nginx Plus 提供更详细的监控信息：

```nginx
server {
    listen 8080;
    
    location /api {
        api;
        allow 127.0.0.1;
        deny all;
    }
    
    location = /dashboard.html {
        root /usr/share/nginx/html;
    }
}
```

## 故障排除

常见问题及解决方法：

1. **负载不均衡**：
   - 检查服务器权重设置
   - 考虑使用不同的负载均衡算法
   - 检查会话保持配置

2. **服务器频繁被标记为不可用**：
   - 增加 `max_fails` 值
   - 增加 `fail_timeout` 值
   - 检查后端服务器性能问题

3. **会话丢失**：
   - 确保正确配置了会话保持
   - 检查应用程序的会话管理

## 总结

本章介绍了 Nginx 的负载均衡功能，包括不同的负载均衡算法、会话保持、健康检查等。负载均衡是构建高可用、高性能应用的关键组件，Nginx 提供了强大而灵活的负载均衡功能。在下一章中，我们将探讨 Nginx 的缓存机制与配置。
