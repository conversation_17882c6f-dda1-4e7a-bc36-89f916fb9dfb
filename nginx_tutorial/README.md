# Nginx 完全指南（含图解）

欢迎来到 Nginx 完全指南！本教程旨在深入讲解 Nginx 的基本概念、配置、使用场景和工作原理，并通过 Mermaid 图表直观展示各种概念，帮助您从入门到精通 Nginx。

## 目录

1. [Nginx 简介与架构](01_nginx_introduction.md)
2. [Nginx 安装指南](02_nginx_installation.md)
3. [Nginx 基本配置详解](03_nginx_basic_configuration.md)
4. [HTTP 服务器配置](04_nginx_http_server.md)
5. [反向代理配置与原理](05_nginx_reverse_proxy.md)
6. [负载均衡配置与策略](06_nginx_load_balancing.md)
7. [Nginx 缓存机制与配置](07_nginx_caching.md)
8. [SSL/HTTPS 配置](08_nginx_ssl_configuration.md)
9. [Nginx 安全最佳实践](09_nginx_security.md)
10. [Nginx 性能优化](10_nginx_performance_tuning.md)
11. [Nginx 常见使用场景](11_nginx_use_cases.md)
12. [Nginx 与其他 Web 服务器的比较](12_nginx_vs_others.md)
13. [Nginx 配置示例详解](13_nginx_config_examples_explained.md)
14. [仓库管理软件的 Nginx 配置](14_storehouse_management_nginx_config.md)

## 如何使用本教程

本教程按照由浅入深的顺序组织，建议按照目录顺序阅读。每个章节都包含理论知识和实践示例，您可以在自己的环境中尝试这些示例来加深理解。

对于初学者，建议从第 1 章开始，逐步学习；对于有一定经验的用户，可以直接跳转到感兴趣的章节。

## 关于 Mermaid 图表

本教程使用 Mermaid 图表来可视化 Nginx 的各种概念和流程。Mermaid 是一种基于文本的图表生成工具，可以在 Markdown 文件中直接编写并渲染为图表。

如果您在本地查看这些文件，可能需要使用支持 Mermaid 的 Markdown 查看器（如 VS Code + Markdown Preview Enhanced 插件）或将文件上传到支持 Mermaid 的平台（如 GitHub）。

## 环境要求

- Linux 操作系统（Ubuntu、CentOS 等）
- 基本的命令行操作知识
- 基本的网络知识

## 贡献与反馈

如果您发现任何错误或有改进建议，请随时提出。

祝您学习愉快！
