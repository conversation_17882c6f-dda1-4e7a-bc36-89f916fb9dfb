# HTTP 服务器配置

本章将详细介绍如何配置 Nginx 作为 HTTP 服务器，包括虚拟主机、静态文件服务、MIME 类型、压缩、缓存等。

## HTTP 请求处理流程

当 Nginx 接收到 HTTP 请求时，会经过以下处理流程：

```mermaid
flowchart TD
    A[接收客户端请求] --> B[解析 HTTP 请求]
    B --> C[查找匹配的 server 块]
    C --> D[查找匹配的 location 块]
    D --> E{是静态文件?}
    E -->|是| F[提供静态文件]
    E -->|否| G[代理到后端服务器]
    F --> H[应用过滤器]
    G --> H
    H --> I[发送响应给客户端]
```

## 虚拟主机配置

虚拟主机允许一个 Nginx 服务器处理多个域名的请求。每个虚拟主机由一个 `server` 块定义。

### 基于域名的虚拟主机

```nginx
server {
    listen 80;
    server_name example.com www.example.com;
    
    root /var/www/example.com;
    index index.html;
    
    # 其他配置...
}

server {
    listen 80;
    server_name another.com www.another.com;
    
    root /var/www/another.com;
    index index.html;
    
    # 其他配置...
}
```

### 基于 IP 的虚拟主机

```nginx
server {
    listen ************:80;
    server_name example.com;
    
    # 配置...
}

server {
    listen ************:80;
    server_name example.com;
    
    # 配置...
}
```

### 基于端口的虚拟主机

```nginx
server {
    listen 80;
    server_name example.com;
    
    # 配置...
}

server {
    listen 8080;
    server_name example.com;
    
    # 配置...
}
```

### 默认服务器

当请求的 Host 头不匹配任何 server_name 时，Nginx 会使用默认服务器：

```nginx
server {
    listen 80 default_server;
    server_name _;
    
    return 444; # 关闭连接，不发送响应头
}
```

## 静态文件服务

Nginx 非常适合提供静态文件，如 HTML、CSS、JavaScript、图片等。

### 基本配置

```nginx
server {
    listen 80;
    server_name example.com;
    
    root /var/www/example.com;
    index index.html index.htm;
    
    # 提供静态文件
    location / {
        try_files $uri $uri/ =404;
    }
}
```

### try_files 指令

`try_files` 指令按顺序检查文件是否存在，并使用第一个找到的文件进行响应：

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

这个配置对于单页应用（SPA）特别有用，它会：
1. 尝试提供请求的 URI 作为文件
2. 如果不存在，尝试作为目录（添加 /）
3. 如果都不存在，回退到 /index.html

### 静态文件类型处理

```mermaid
graph TD
    A[客户端请求文件] --> B[Nginx 接收请求]
    B --> C[检查文件扩展名]
    C --> D[查找 MIME 类型映射]
    D --> E[设置 Content-Type 头]
    E --> F[发送文件给客户端]
```

Nginx 使用 `mime.types` 文件将文件扩展名映射到 MIME 类型：

```nginx
include mime.types;
default_type application/octet-stream;

# 自定义 MIME 类型
types {
    application/wasm wasm;
}
```

## 文件缓存控制

为了提高性能，可以配置浏览器缓存静态文件：

```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 30d;
    add_header Cache-Control "public, no-transform";
}

location ~* \.(html|xml)$ {
    expires 1h;
    add_header Cache-Control "public";
}
```

## 启用 Gzip 压缩

Gzip 压缩可以显著减少传输数据的大小：

```nginx
http {
    # 启用 gzip
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    
    # 压缩的 MIME 类型
    gzip_types
        application/javascript
        application/json
        application/xml
        text/css
        text/plain
        text/xml;
}
```

压缩流程：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Nginx as Nginx 服务器
    
    Client->>Nginx: 请求资源 (Accept-Encoding: gzip)
    Note over Nginx: 检查是否可压缩
    Note over Nginx: 压缩内容
    Nginx->>Client: 返回压缩内容 (Content-Encoding: gzip)
    Note over Client: 解压内容并使用
```

## 访问控制

### IP 地址限制

```nginx
location /admin/ {
    # 允许特定 IP
    allow ***********/24;
    allow 10.0.0.0/8;
    # 拒绝其他所有 IP
    deny all;
}
```

### 基本认证

```nginx
location /private/ {
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/.htpasswd;
}
```

创建密码文件：

```bash
sudo apt install apache2-utils
sudo htpasswd -c /etc/nginx/.htpasswd username
```

## 错误页面自定义

```nginx
server {
    # 自定义错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 错误页面位置
    location = /404.html {
        root /var/www/error_pages;
        internal;
    }
    
    location = /50x.html {
        root /var/www/error_pages;
        internal;
    }
}
```

## 日志配置

Nginx 提供访问日志和错误日志：

```nginx
http {
    # 定义日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    # 访问日志
    access_log /var/log/nginx/access.log main;
    
    # 错误日志
    error_log /var/log/nginx/error.log warn;
    
    server {
        # 针对特定虚拟主机的日志
        access_log /var/log/nginx/example.com.access.log main;
        error_log /var/log/nginx/example.com.error.log;
        
        # 禁用特定位置的访问日志
        location /status {
            access_log off;
        }
    }
}
```

## 限制和保护

### 请求速率限制

使用 `limit_req_zone` 和 `limit_req` 指令限制请求速率：

```nginx
# 定义限制区域
limit_req_zone $binary_remote_addr zone=mylimit:10m rate=10r/s;

server {
    # 应用限制
    location / {
        limit_req zone=mylimit burst=20 nodelay;
    }
}
```

这将限制每个 IP 地址每秒最多 10 个请求，允许最多 20 个请求的突发。

### 连接数限制

```nginx
# 定义限制区域
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

server {
    # 限制每个 IP 最多 10 个并发连接
    limit_conn conn_limit 10;
}
```

## 文件上传配置

处理大文件上传：

```nginx
server {
    # 客户端请求体大小限制
    client_max_body_size 100m;
    
    # 临时文件目录
    client_body_temp_path /var/nginx/client_temp;
    
    # 缓冲区大小
    client_body_buffer_size 128k;
}
```

## 跨域资源共享 (CORS)

为 API 或静态资源启用 CORS：

```nginx
location /api/ {
    # 允许所有来源
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    
    # 预检请求缓存 1 小时
    add_header 'Access-Control-Max-Age' 3600;
    
    # 处理 OPTIONS 请求
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header 'Access-Control-Max-Age' 3600;
        add_header 'Content-Type' 'text/plain; charset=utf-8';
        add_header 'Content-Length' 0;
        return 204;
    }
    
    # 代理到后端
    proxy_pass http://backend;
}
```

## 总结

本章介绍了 Nginx 作为 HTTP 服务器的各种配置选项，包括虚拟主机、静态文件服务、缓存控制、压缩、访问控制等。这些功能使 Nginx 成为一个强大而灵活的 Web 服务器。在下一章中，我们将探讨 Nginx 作为反向代理的配置和原理。
