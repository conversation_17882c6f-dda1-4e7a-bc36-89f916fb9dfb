# Nginx 性能优化

本章将详细介绍 Nginx 的性能优化技术，包括配置调优、系统优化、监控和基准测试。

## 性能优化的重要性

优化 Nginx 性能可以带来多方面的好处：
- 提高网站响应速度
- 增加服务器吞吐量
- 减少资源使用
- 提升用户体验
- 降低运营成本
- 提高搜索引擎排名

## 性能优化流程

```mermaid
graph TD
    A[性能基准测试] --> B[识别瓶颈]
    B --> C[实施优化]
    C --> D[测试优化效果]
    D --> E{达到目标?}
    E -->|否| B
    E -->|是| F[监控性能]
    F --> G[定期重复流程]
```

## 基本配置优化

### 工作进程和连接

```nginx
# 设置工作进程数量
worker_processes auto;  # 自动设置为 CPU 核心数

# 设置每个工作进程的最大连接数
events {
    worker_connections 1024;  # 根据服务器内存调整
    multi_accept on;          # 一次接受所有新连接
    use epoll;                # Linux 上使用 epoll 事件模型
}
```

### 保持连接设置

```nginx
http {
    # 启用 keepalive
    keepalive_timeout 65;
    keepalive_requests 100;
    
    # 对上游服务器的连接
    upstream backend {
        server backend1.example.com;
        server backend2.example.com;
        keepalive 32;  # 每个工作进程保持的空闲连接数
    }
    
    server {
        location / {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";  # 清除 Connection 头以启用 keepalive
        }
    }
}
```

### 缓冲区优化

```nginx
http {
    # 客户端缓冲区
    client_body_buffer_size 16k;
    client_header_buffer_size 1k;
    client_max_body_size 10m;
    large_client_header_buffers 4 8k;
    
    # 代理缓冲区
    proxy_buffer_size 4k;
    proxy_buffers 8 16k;
    proxy_busy_buffers_size 32k;
    proxy_temp_file_write_size 64k;
}
```

### 超时设置

```nginx
http {
    # 客户端超时
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # 代理超时
    proxy_connect_timeout 5s;
    proxy_send_timeout 10s;
    proxy_read_timeout 10s;
}
```

## 静态内容优化

### 启用 sendfile 和 tcp_nopush

```nginx
http {
    sendfile on;           # 使用 sendfile 系统调用
    tcp_nopush on;         # 在 sendfile 开启时启用，优化数据包发送
    tcp_nodelay on;        # 禁用 Nagle 算法，减少延迟
    sendfile_max_chunk 1m; # 限制单个 sendfile() 调用传输的数据量
}
```

### 静态文件缓存

```nginx
http {
    # 打开文件描述符缓存
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
}
```

### 启用压缩

```nginx
http {
    # 启用 gzip 压缩
    gzip on;
    gzip_comp_level 5;     # 压缩级别 (1-9)
    gzip_min_length 256;   # 最小压缩文件大小
    gzip_proxied any;      # 对代理请求也进行压缩
    gzip_vary on;          # 添加 Vary: Accept-Encoding 头
    
    # 压缩的 MIME 类型
    gzip_types
        application/javascript
        application/json
        application/xml
        text/css
        text/plain
        text/xml
        image/svg+xml;
    
    # 禁止压缩的 User-Agent
    gzip_disable "MSIE [1-6]\.";
}
```

### 启用 Brotli 压缩

Brotli 是比 gzip 更高效的压缩算法：

```nginx
# 需要安装 Brotli 模块
load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;

http {
    # 启用 Brotli 压缩
    brotli on;
    brotli_comp_level 6;   # 压缩级别 (0-11)
    brotli_static on;      # 提供预压缩的 .br 文件
    brotli_types
        application/javascript
        application/json
        application/xml
        text/css
        text/plain
        text/xml
        image/svg+xml;
}
```

## 缓存优化

### 浏览器缓存

```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 30d;
    add_header Cache-Control "public, no-transform";
}

location ~* \.(html|xml)$ {
    expires 1h;
    add_header Cache-Control "public";
}
```

### 代理缓存

```nginx
# 定义缓存区域
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g 
                 inactive=60m use_temp_path=off;

server {
    # 启用缓存
    proxy_cache my_cache;
    proxy_cache_valid 200 302 10m;
    proxy_cache_valid 404 1m;
    proxy_cache_lock on;
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
    proxy_cache_revalidate on;
    add_header X-Cache-Status $upstream_cache_status;
}
```

## HTTP/2 和 HTTP/3 优化

### 启用 HTTP/2

```nginx
server {
    listen 443 ssl http2;
    server_name example.com;
    
    # SSL 配置...
}
```

### 启用 HTTP/3 (QUIC)

```nginx
# 需要编译支持 QUIC 的 Nginx
server {
    listen 443 ssl http2;
    listen 443 quic reuseport;  # HTTP/3
    server_name example.com;
    
    # 告诉客户端支持 HTTP/3
    add_header Alt-Svc 'h3=":443"; ma=86400';
    
    # SSL 配置...
}
```

## 负载均衡优化

### 加权负载均衡

```nginx
upstream backend {
    server backend1.example.com weight=5;  # 50% 的请求
    server backend2.example.com weight=3;  # 30% 的请求
    server backend3.example.com weight=2;  # 20% 的请求
    
    keepalive 32;  # 保持连接
}
```

### 最少连接负载均衡

```nginx
upstream backend {
    least_conn;  # 将请求发送到活动连接数最少的服务器
    server backend1.example.com;
    server backend2.example.com;
    server backend3.example.com;
}
```

## 系统级优化

### 文件描述符限制

```bash
# 在 /etc/security/limits.conf 中添加
nginx soft nofile 65535
nginx hard nofile 65535

# 在 nginx.conf 中添加
worker_rlimit_nofile 65535;
```

### 网络优化

```bash
# 在 /etc/sysctl.conf 中添加
# 增加 TCP 连接队列
net.core.somaxconn = 4096
net.ipv4.tcp_max_syn_backlog = 4096

# TIME_WAIT 优化
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30

# 应用更改
sudo sysctl -p
```

### 使用 tmpfs 加速缓存

```bash
# 创建 tmpfs 挂载点
sudo mkdir -p /var/cache/nginx/tmpfs
sudo mount -t tmpfs -o size=1G tmpfs /var/cache/nginx/tmpfs

# 在 nginx.conf 中使用
proxy_cache_path /var/cache/nginx/tmpfs levels=1:2 keys_zone=my_cache:10m max_size=1g;
```

## 监控和分析

### 启用 stub_status 模块

```nginx
server {
    location /nginx_status {
        stub_status;
        allow 127.0.0.1;
        deny all;
    }
}
```

### 使用 Nginx Plus 的高级监控

```nginx
server {
    location /api {
        api;
        allow 127.0.0.1;
        deny all;
    }
    
    location = /dashboard.html {
        root /usr/share/nginx/html;
    }
}
```

### 日志优化

```nginx
http {
    # 禁用不必要的日志
    access_log off;
    
    # 或者使用缓冲日志
    access_log /var/log/nginx/access.log combined buffer=16k flush=5s;
    
    # 对静态文件禁用日志
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        access_log off;
        log_not_found off;
    }
}
```

## 性能测试工具

### 使用 ab (Apache Benchmark)

```bash
# 发送 10000 个请求，并发 100
ab -n 10000 -c 100 https://example.com/
```

### 使用 wrk

```bash
# 运行 30 秒，使用 12 个线程，保持 400 个 HTTP 连接
wrk -t12 -c400 -d30s https://example.com/
```

### 使用 siege

```bash
# 模拟 100 个并发用户，持续 1 分钟
siege -c100 -t1M https://example.com/
```

## 性能优化案例

### 高流量静态网站

```nginx
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    multi_accept on;
    use epoll;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    
    # 文件缓存
    open_file_cache max=200000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    
    # 压缩
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_types text/plain text/css application/javascript application/json;
    
    # 缓冲区
    client_body_buffer_size 16k;
    client_header_buffer_size 1k;
    client_max_body_size 1m;
    large_client_header_buffers 4 8k;
    
    # 超时
    client_body_timeout 12;
    client_header_timeout 12;
    keepalive_timeout 15;
    send_timeout 10;
    
    server {
        listen 80;
        server_name example.com;
        
        root /var/www/html;
        
        # 静态文件缓存
        location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
            expires 30d;
            add_header Cache-Control "public, no-transform";
            access_log off;
        }
    }
}
```

### API 服务器

```nginx
worker_processes auto;

events {
    worker_connections 2048;
    multi_accept on;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    
    # 上游服务器
    upstream api_backend {
        least_conn;
        server backend1.example.com;
        server backend2.example.com;
        keepalive 32;
    }
    
    # 缓存配置
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=1g 
                     inactive=10m use_temp_path=off;
    
    server {
        listen 80;
        server_name api.example.com;
        
        # 全局代理设置
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # API 缓存
        location /api/ {
            proxy_cache api_cache;
            proxy_cache_valid 200 1m;
            proxy_cache_lock on;
            proxy_cache_use_stale updating;
            add_header X-Cache-Status $upstream_cache_status;
            
            # 限制请求速率
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://api_backend;
        }
    }
}
```

## 性能优化检查清单

- [ ] 设置适当的工作进程数
- [ ] 优化工作连接数
- [ ] 启用 sendfile 和 tcp_nopush
- [ ] 配置文件缓存
- [ ] 启用压缩
- [ ] 优化缓冲区大小
- [ ] 配置超时设置
- [ ] 启用 HTTP/2
- [ ] 配置浏览器缓存
- [ ] 配置代理缓存
- [ ] 优化负载均衡
- [ ] 增加系统文件描述符限制
- [ ] 优化网络设置
- [ ] 配置日志
- [ ] 进行性能测试

## 常见性能问题及解决方案

### 1. 高 CPU 使用率

可能原因：
- 工作进程数配置不当
- 复杂的正则表达式
- SSL 处理负担重

解决方案：
- 调整 `worker_processes` 为 CPU 核心数
- 优化正则表达式
- 启用 SSL 会话缓存
- 考虑使用 CDN 分担 SSL 处理

### 2. 高内存使用

可能原因：
- 缓冲区设置过大
- 缓存区域过大
- 连接数过多

解决方案：
- 调整缓冲区大小
- 优化缓存配置
- 限制连接数

### 3. 高磁盘 I/O

可能原因：
- 频繁的日志写入
- 缓存写入频繁

解决方案：
- 使用缓冲日志
- 对静态内容禁用日志
- 使用 tmpfs 存储缓存
- 优化缓存配置

### 4. 网络瓶颈

可能原因：
- 未启用压缩
- 未优化静态内容
- 未使用 HTTP/2

解决方案：
- 启用 gzip 或 Brotli 压缩
- 配置浏览器缓存
- 启用 HTTP/2
- 使用 CDN

## 总结

本章介绍了 Nginx 的性能优化技术，包括配置调优、系统优化、监控和基准测试。通过实施这些优化措施，可以显著提高 Nginx 的性能和可扩展性。在下一章中，我们将探讨 Nginx 的常见使用场景。
