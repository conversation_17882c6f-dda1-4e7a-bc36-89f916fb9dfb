# 反向代理配置与原理

本章将详细介绍 Nginx 作为反向代理的工作原理、配置方法和最佳实践。

## 什么是反向代理？

反向代理是一种服务器，它接收客户端的请求，然后将请求转发给后端服务器，再将后端服务器的响应返回给客户端。客户端只与反向代理服务器通信，不直接接触后端服务器。

```mermaid
graph LR
    A[客户端] -->|请求| B[Nginx 反向代理]
    B -->|转发请求| C[后端服务器 1]
    B -->|转发请求| D[后端服务器 2]
    B -->|转发请求| E[后端服务器 3]
    C -->|响应| B
    D -->|响应| B
    E -->|响应| B
    B -->|返回响应| A
```

## 反向代理的优势

1. **安全性增强**：隐藏后端服务器的真实信息，减少直接攻击的风险
2. **负载均衡**：将请求分发到多个后端服务器
3. **缓存静态内容**：减轻后端服务器负担
4. **SSL 终结**：在代理层处理 HTTPS 加密/解密
5. **压缩**：减少传输数据大小
6. **统一入口**：为不同的后端服务提供统一的访问点
7. **灰度发布**：支持 A/B 测试和金丝雀发布

## 基本反向代理配置

最简单的反向代理配置如下：

```nginx
server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend_server;
    }
}
```

这个配置将所有请求转发到 `http://backend_server`。

## 反向代理处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Nginx as Nginx 代理
    participant Backend as 后端服务器
    
    Client->>Nginx: 发送 HTTP 请求
    Note over Nginx: 解析请求
    Note over Nginx: 应用请求头修改
    Nginx->>Backend: 转发修改后的请求
    Backend->>Nginx: 返回响应
    Note over Nginx: 应用响应头修改
    Nginx->>Client: 返回修改后的响应
```

## 代理请求头设置

为了让后端服务器获取客户端的真实信息，需要设置一些代理请求头：

```nginx
location / {
    proxy_pass http://backend_server;
    
    # 传递客户端信息
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

这些头的作用：
- `Host`: 原始请求的主机名
- `X-Real-IP`: 客户端的真实 IP
- `X-Forwarded-For`: 客户端 IP 列表，包括所有经过的代理
- `X-Forwarded-Proto`: 原始请求的协议（http 或 https）

## 超时设置

配置各种超时参数以优化代理性能：

```nginx
location / {
    proxy_pass http://backend_server;
    
    # 超时设置
    proxy_connect_timeout 5s;     # 与后端建立连接的超时时间
    proxy_send_timeout 10s;       # 向后端发送请求的超时时间
    proxy_read_timeout 30s;       # 从后端读取响应的超时时间
    
    # 缓冲设置
    proxy_buffering on;           # 启用响应缓冲
    proxy_buffer_size 16k;        # 响应头缓冲区大小
    proxy_buffers 4 32k;          # 响应体缓冲区大小和数量
    proxy_busy_buffers_size 64k;  # 繁忙状态下的缓冲区大小
    proxy_temp_file_write_size 64k; # 临时文件写入大小
}
```

## 缓存配置

配置代理缓存可以显著提高性能：

```nginx
# 定义缓存区域
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g inactive=60m;

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend_server;
        
        # 启用缓存
        proxy_cache my_cache;
        proxy_cache_valid 200 302 10m;  # 对 200 和 302 响应缓存 10 分钟
        proxy_cache_valid 404 1m;       # 对 404 响应缓存 1 分钟
        proxy_cache_key $scheme$host$request_uri; # 缓存键
        
        # 添加缓存状态头
        add_header X-Cache-Status $upstream_cache_status;
        
        # 条件缓存
        proxy_cache_bypass $http_cache_control; # 如果请求头包含特定值，绕过缓存
        proxy_no_cache $http_pragma;           # 不缓存特定请求
    }
}
```

缓存流程：

```mermaid
flowchart TD
    A[客户端请求] --> B{缓存中存在?}
    B -->|是| C[从缓存提供响应]
    B -->|否| D[转发到后端]
    D --> E[后端处理请求]
    E --> F[返回响应]
    F --> G[存储到缓存]
    G --> H[返回响应给客户端]
    C --> H
```

## 处理 WebSocket 连接

WebSocket 需要特殊的代理配置：

```nginx
location /ws/ {
    proxy_pass http://websocket_backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    
    # WebSocket 连接可能长时间空闲
    proxy_read_timeout 3600s;
    proxy_send_timeout 3600s;
}
```

## 代理到 UNIX 套接字

对于本地后端服务，可以使用 UNIX 套接字提高性能：

```nginx
location / {
    proxy_pass http://unix:/var/run/app.sock:/;
    # 其他代理设置...
}
```

## 代理到 HTTPS 后端

当后端使用 HTTPS 时，需要配置 SSL：

```nginx
location / {
    proxy_pass https://secure_backend;
    proxy_ssl_verify on;
    proxy_ssl_trusted_certificate /etc/nginx/certs/trusted_ca.crt;
    proxy_ssl_verify_depth 2;
    proxy_ssl_session_reuse on;
}
```

## URL 重写和修改

### 修改代理 URL 路径

```nginx
# 不保留路径
location /api/ {
    proxy_pass http://backend:8080/;  # 注意末尾的斜杠
}

# 保留路径
location /api/ {
    proxy_pass http://backend:8080;  # 没有末尾的斜杠
}
```

区别：
- 对于请求 `/api/users`
- 第一种配置会代理到 `http://backend:8080/users`
- 第二种配置会代理到 `http://backend:8080/api/users`

### 使用 rewrite 修改 URL

```nginx
location /old/ {
    rewrite ^/old/(.*)$ /new/$1 break;
    proxy_pass http://backend;
}
```

## 错误处理

配置代理错误处理：

```nginx
location / {
    proxy_pass http://backend;
    
    # 自定义错误页面
    proxy_intercept_errors on;
    error_page 500 502 503 504 /50x.html;
    
    # 后端超时或不可用时的备用响应
    proxy_next_upstream error timeout http_500;
}
```

## 健康检查

使用被动健康检查：

```nginx
upstream backend {
    server backend1.example.com max_fails=3 fail_timeout=30s;
    server backend2.example.com max_fails=3 fail_timeout=30s;
}
```

这会在后端服务器失败 3 次后，将其标记为不可用 30 秒。

## 高级代理配置示例

### 多后端应用整合

```nginx
server {
    listen 80;
    server_name example.com;
    
    # 前端应用
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API 服务
    location /api/ {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 管理后台
    location /admin/ {
        proxy_pass http://admin_backend;
        # 限制访问
        allow ***********/24;
        deny all;
    }
    
    # WebSocket
    location /ws/ {
        proxy_pass http://ws_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 基于子域名的代理

```nginx
# API 子域名
server {
    listen 80;
    server_name api.example.com;
    
    location / {
        proxy_pass http://api_backend;
        # 代理设置...
    }
}

# 管理后台子域名
server {
    listen 80;
    server_name admin.example.com;
    
    location / {
        proxy_pass http://admin_backend;
        # 代理设置...
    }
}
```

## 安全最佳实践

1. **隐藏后端信息**：
   ```nginx
   proxy_hide_header X-Powered-By;
   proxy_hide_header Server;
   ```

2. **设置安全头**：
   ```nginx
   add_header X-XSS-Protection "1; mode=block";
   add_header X-Content-Type-Options "nosniff";
   add_header X-Frame-Options "SAMEORIGIN";
   ```

3. **限制请求方法**：
   ```nginx
   if ($request_method !~ ^(GET|POST|HEAD)$) {
       return 405;
   }
   ```

4. **设置超时**：防止慢速攻击
   ```nginx
   client_body_timeout 10s;
   client_header_timeout 10s;
   ```

## 故障排除

常见问题及解决方法：

1. **502 Bad Gateway**：后端服务器不可用或响应超时
   - 检查后端服务是否运行
   - 增加超时设置
   - 检查防火墙规则

2. **504 Gateway Timeout**：后端响应超时
   - 增加 `proxy_read_timeout` 值
   - 检查后端服务性能问题

3. **无法传递客户端 IP**：
   - 确保设置了 `X-Real-IP` 和 `X-Forwarded-For` 头

## 总结

本章介绍了 Nginx 作为反向代理的配置和原理。反向代理是 Nginx 最强大的功能之一，可以用于负载均衡、安全防护、缓存加速等多种场景。在下一章中，我们将深入探讨 Nginx 的负载均衡配置和策略。
