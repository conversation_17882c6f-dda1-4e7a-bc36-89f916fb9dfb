# 仓库管理软件的 Nginx 配置

本文档将详细介绍如何配置 Nginx 来管理仓库管理软件的前后端部署。这个配置适用于将前端 Vue 应用和后端 Go 服务部署在同一服务器上的场景。

## 系统架构

仓库管理软件采用前后端分离的架构：

```mermaid
graph TD
    A[用户] --> B[Nginx]
    B --> C[前端应用 - Vue.js]
    B --> D[后端API - Go]
    D --> E[数据库 - MySQL]
```

- **前端**：Vue.js 构建的单页应用 (SPA)
- **后端**：Go 语言开发的 API 服务
- **数据库**：MySQL 数据库
- **Nginx**：作为反向代理和静态资源服务器

## 基本配置思路

1. Nginx 监听 80 端口（HTTP）和 443 端口（HTTPS）
2. 将前端静态资源部署在 Nginx 服务器上
3. 将 API 请求代理到后端 Go 服务
4. 配置 SSL 证书实现 HTTPS 访问
5. 添加安全相关配置

## 目录结构

假设服务器上的目录结构如下：

```
/opt/storehouse_web/
├── frontend/         # 前端构建文件
│   ├── index.html
│   ├── css/
│   ├── js/
│   └── assets/
├── backend/          # 后端Go应用
│   └── storehouse    # Go编译后的可执行文件
└── nginx/            # Nginx配置文件
    └── storehouse-manage.top.conf
```

## HTTP 配置（基础版）

以下是基础的 HTTP 配置，不包含 SSL：

```nginx
server {
    listen 80;
    server_name storehouse-manage.top www.storehouse-manage.top;
    
    # 访问日志
    access_log /var/log/nginx/storehouse.access.log;
    error_log /var/log/nginx/storehouse.error.log;
    
    # 前端静态文件
    location / {
        root /opt/storehouse_web/frontend;
        index index.html;
        try_files $uri $uri/ /index.html;  # 支持Vue路由的HTML5 History模式
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8888;  # Go后端服务运行在8888端口
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        root /opt/storehouse_web/frontend;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
}
```

## HTTPS 配置（推荐版）

以下是包含 SSL 的完整配置：

```nginx
# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name storehouse-manage.top www.storehouse-manage.top;
    
    # 将所有 HTTP 请求重定向到 HTTPS
    return 301 https://$host$request_uri;
}

# HTTPS 服务器
server {
    listen 443 ssl http2;
    server_name storehouse-manage.top www.storehouse-manage.top;
    
    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/storehouse-manage.top.crt;
    ssl_certificate_key /etc/nginx/ssl/storehouse-manage.top.key;
    
    # SSL 参数优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 其他安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 访问日志
    access_log /var/log/nginx/storehouse.access.log;
    error_log /var/log/nginx/storehouse.error.log;
    
    # 前端静态文件
    location / {
        root /opt/storehouse_web/frontend;
        index index.html;
        try_files $uri $uri/ /index.html;  # 支持Vue路由的HTML5 History模式
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8888;  # Go后端服务运行在8888端口
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API请求超时设置
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 30s;
    }
    
    # 静态资源缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        root /opt/storehouse_web/frontend;
        expires 30d;
        add_header Cache-Control "public, no-transform";
        access_log off;  # 静态资源不记录访问日志
    }
}
```

## 使用 Docker 的配置

如果您使用 Docker 部署前后端应用，Nginx 配置会略有不同：

```nginx
server {
    listen 80;
    server_name storehouse-manage.top www.storehouse-manage.top;
    
    # 将所有 HTTP 请求重定向到 HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name storehouse-manage.top www.storehouse-manage.top;
    
    # SSL 配置...（同上）
    
    # 前端静态文件
    location / {
        # 假设前端容器暴露在 8080 端口
        proxy_pass http://frontend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 后端API代理
    location /api/ {
        # 假设后端容器暴露在 8888 端口
        proxy_pass http://backend:8888;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 配置说明

### 前端配置

```nginx
location / {
    root /opt/storehouse_web/frontend;
    index index.html;
    try_files $uri $uri/ /index.html;
}
```

**作用和效果**：
- `root` 指定前端静态文件的根目录
- `index` 指定默认索引文件
- `try_files` 实现 Vue 路由的 HTML5 History 模式支持：
  1. 首先尝试提供请求的 URI 作为文件
  2. 如果不存在，尝试作为目录（添加 /）
  3. 如果都不存在，回退到 /index.html，让 Vue 路由处理

这样配置可以确保无论用户访问什么 URL，都能正确加载 Vue 应用，并由前端路由处理导航。

### 后端 API 代理

```nginx
location /api/ {
    proxy_pass http://localhost:8888;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

**作用和效果**：
- 所有以 `/api/` 开头的请求都会被代理到后端 Go 服务
- `proxy_set_header` 指令确保后端服务能获取到原始请求的信息：
  - `Host`：原始请求的主机名
  - `X-Real-IP`：客户端的真实 IP
  - `X-Forwarded-For`：客户端 IP 链
  - `X-Forwarded-Proto`：原始请求协议（http/https）

### 静态资源缓存

```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    root /opt/storehouse_web/frontend;
    expires 30d;
    add_header Cache-Control "public, no-transform";
}
```

**作用和效果**：
- 对静态资源（图片、CSS、JS 等）设置 30 天的缓存时间
- 减少重复请求，提高页面加载速度
- `public` 允许 CDN 等中间缓存存储内容
- `no-transform` 防止内容被代理或缓存修改

## 部署步骤

1. **准备前端文件**：
   ```bash
   # 构建前端项目
   cd frontend
   npm run build
   
   # 将构建文件复制到服务器
   scp -r dist/* root@*************:/opt/storehouse_web/frontend/
   ```

2. **准备后端服务**：
   ```bash
   # 编译Go后端
   cd backend
   go build -o storehouse
   
   # 将可执行文件复制到服务器
   scp storehouse root@*************:/opt/storehouse_web/backend/
   ```

3. **配置 Nginx**：
   ```bash
   # 创建Nginx配置文件
   scp storehouse-manage.top.conf root@*************:/etc/nginx/conf.d/
   
   # 测试配置
   ssh root@************* "nginx -t"
   
   # 重新加载Nginx
   ssh root@************* "nginx -s reload"
   ```

4. **启动后端服务**：
   ```bash
   ssh root@************* "cd /opt/storehouse_web/backend && nohup ./storehouse > storehouse.log 2>&1 &"
   ```

## 常见问题排查

1. **前端路由问题**：
   - 症状：刷新页面时出现 404 错误
   - 解决：确保 `try_files $uri $uri/ /index.html;` 配置正确

2. **API 请求失败**：
   - 症状：前端无法获取后端数据
   - 排查：
     - 检查后端服务是否正常运行：`ps aux | grep storehouse`
     - 检查端口是否正确：`netstat -tulpn | grep 8888`
     - 检查 Nginx 错误日志：`tail -f /var/log/nginx/storehouse.error.log`

3. **SSL 证书问题**：
   - 症状：浏览器显示证书错误
   - 排查：
     - 检查证书路径是否正确
     - 检查证书是否过期：`openssl x509 -in /etc/nginx/ssl/storehouse-manage.top.crt -text -noout | grep "Not After"`

4. **静态资源加载失败**：
   - 症状：页面样式或图片无法加载
   - 排查：
     - 检查文件权限：`ls -la /opt/storehouse_web/frontend`
     - 确保 Nginx 用户有读取权限：`chmod -R 755 /opt/storehouse_web/frontend`

## 总结

这个 Nginx 配置为仓库管理软件提供了一个完整的部署方案，包括：

1. 前端静态文件服务
2. 后端 API 代理
3. HTTPS 安全访问
4. 静态资源缓存优化

通过这种配置，用户可以通过单一域名访问整个应用，同时前后端可以独立开发和部署，提高了系统的可维护性和扩展性。
