# Nginx 安装指南

本章将介绍在不同操作系统上安装 Nginx 的方法，以及安装后的基本验证。

## 安装流程概览

无论您使用哪种操作系统，Nginx 的安装和初始配置流程大致如下：

```mermaid
flowchart TD
    A[选择安装方式] --> B[安装 Nginx]
    B --> C[验证安装]
    C --> D[基本配置]
    D --> E[启动 Nginx]
    E --> F[验证运行状态]
```

## 在 Ubuntu/Debian 上安装 Nginx

### 使用包管理器安装

```bash
# 更新包索引
sudo apt update

# 安装 Nginx
sudo apt install nginx

# 启动 Nginx
sudo systemctl start nginx

# 设置开机自启
sudo systemctl enable nginx
```

### 验证安装

```bash
# 检查 Nginx 版本
nginx -v

# 检查 Nginx 状态
sudo systemctl status nginx
```

## 在 CentOS/RHEL 上安装 Nginx

### 使用 EPEL 仓库安装

```bash
# 安装 EPEL 仓库
sudo yum install epel-release

# 安装 Nginx
sudo yum install nginx

# 启动 Nginx
sudo systemctl start nginx

# 设置开机自启
sudo systemctl enable nginx
```

### 验证安装

```bash
# 检查 Nginx 版本
nginx -v

# 检查 Nginx 状态
sudo systemctl status nginx
```

## 从源代码编译安装

从源代码编译安装可以获得最新版本的 Nginx，并可以自定义编译选项。

```mermaid
flowchart TD
    A[下载源代码] --> B[安装依赖]
    B --> C[配置编译选项]
    C --> D[编译]
    D --> E[安装]
    E --> F[配置系统服务]
```

### 步骤详解

```bash
# 安装编译依赖
sudo apt install build-essential libpcre3 libpcre3-dev zlib1g zlib1g-dev libssl-dev

# 下载 Nginx 源代码
wget https://nginx.org/download/nginx-1.24.0.tar.gz
tar -zxvf nginx-1.24.0.tar.gz
cd nginx-1.24.0

# 配置
./configure --prefix=/usr/local/nginx \
            --with-http_ssl_module \
            --with-http_v2_module \
            --with-http_realip_module \
            --with-http_stub_status_module

# 编译和安装
make
sudo make install

# 创建软链接
sudo ln -s /usr/local/nginx/sbin/nginx /usr/bin/nginx
```

## 使用 Docker 安装 Nginx

Docker 提供了一种简单、隔离的方式来运行 Nginx。

```mermaid
flowchart TD
    A[安装 Docker] --> B[拉取 Nginx 镜像]
    B --> C[运行 Nginx 容器]
    C --> D[映射配置文件]
    D --> E[验证运行状态]
```

### 基本命令

```bash
# 拉取官方 Nginx 镜像
docker pull nginx

# 运行 Nginx 容器
docker run --name my-nginx -p 80:80 -d nginx

# 使用自定义配置文件
docker run --name my-nginx -p 80:80 -v /path/to/nginx.conf:/etc/nginx/nginx.conf:ro -d nginx
```

## 安装后的验证

无论使用哪种方法安装，都可以通过以下方式验证 Nginx 是否正常工作：

1. **检查进程**：
   ```bash
   ps aux | grep nginx
   ```

2. **检查端口**：
   ```bash
   sudo netstat -tulpn | grep nginx
   ```

3. **访问默认页面**：
   在浏览器中访问 `http://localhost` 或 `http://服务器IP`，应该能看到 Nginx 的欢迎页面。

```mermaid
graph TD
    A[安装完成] --> B{检查进程}
    B -->|成功| C{检查端口}
    C -->|成功| D{访问网页}
    D -->|成功| E[安装验证完成]
    B -->|失败| F[检查错误日志]
    C -->|失败| F
    D -->|失败| F
    F --> G[解决问题]
    G --> A
```

## Nginx 目录结构

安装完成后，Nginx 的主要目录和文件如下：

| 路径 | 描述 |
|------|------|
| `/etc/nginx/` | 配置文件目录 |
| `/etc/nginx/nginx.conf` | 主配置文件 |
| `/etc/nginx/sites-available/` | 可用站点配置 |
| `/etc/nginx/sites-enabled/` | 已启用站点配置 |
| `/var/log/nginx/` | 日志文件目录 |
| `/var/www/html/` | 默认网站根目录 |
| `/usr/share/nginx/html/` | 默认网站根目录（某些发行版） |

## Nginx 基本命令

以下是一些常用的 Nginx 命令：

```bash
# 启动 Nginx
sudo systemctl start nginx

# 停止 Nginx
sudo systemctl stop nginx

# 重启 Nginx
sudo systemctl restart nginx

# 重新加载配置（不中断服务）
sudo systemctl reload nginx
# 或
sudo nginx -s reload

# 检查配置文件语法
sudo nginx -t

# 显示编译选项和版本
nginx -V
```

## 常见安装问题及解决方案

### 端口冲突

如果 80 端口已被占用，Nginx 将无法启动。解决方法：

1. 找出占用端口的进程：
   ```bash
   sudo netstat -tulpn | grep :80
   ```

2. 停止该进程或修改 Nginx 配置使用其他端口。

### 权限问题

确保 Nginx 用户（通常是 www-data 或 nginx）对网站目录有读取权限。

```bash
# 修改目录所有者
sudo chown -R www-data:www-data /var/www/html

# 设置适当的权限
sudo chmod -R 755 /var/www/html
```

## 总结

本章介绍了在不同操作系统上安装 Nginx 的方法，以及安装后的基本验证和常用命令。在下一章中，我们将深入探讨 Nginx 的基本配置。
