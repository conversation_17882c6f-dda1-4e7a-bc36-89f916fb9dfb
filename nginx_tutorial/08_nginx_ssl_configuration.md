# SSL/HTTPS 配置

本章将详细介绍如何在 Nginx 中配置 SSL/HTTPS，包括证书获取、安全配置、性能优化和最佳实践。

## HTTPS 的重要性

HTTPS（HTTP Secure）通过 SSL/TLS 协议为 HTTP 通信提供加密、数据完整性和身份验证。使用 HTTPS 的好处：

1. **数据加密**：防止数据在传输过程中被窃取
2. **数据完整性**：确保数据在传输过程中不被篡改
3. **身份验证**：验证网站的真实身份
4. **搜索引擎优化**：HTTPS 是搜索引擎排名的因素之一
5. **现代功能支持**：许多现代 Web 功能（如 HTTP/2、Service Workers）要求使用 HTTPS
6. **用户信任**：浏览器会标记非 HTTPS 网站为"不安全"

## SSL/TLS 工作原理

SSL/TLS 握手过程：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    
    Client->>Server: 客户端 Hello (支持的加密套件、随机数)
    Server->>Client: 服务器 Hello (选择的加密套件、随机数)
    Server->>Client: 发送证书
    Server->>Client: 服务器 Hello 完成
    Client->>Client: 验证证书
    Client->>Server: 客户端密钥交换 (预主密钥)
    Client->>Server: 更改加密规范
    Client->>Server: 客户端 Hello 完成
    Server->>Server: 生成会话密钥
    Server->>Client: 更改加密规范
    Server->>Client: 服务器 Hello 完成
    Client->>Client: 生成会话密钥
    
    Note over Client,Server: 使用对称加密的安全通信开始
```

## 获取 SSL 证书

### 1. 使用 Let's Encrypt 免费证书

[Let's Encrypt](https://letsencrypt.org/) 提供免费的 SSL 证书，可以使用 Certbot 工具自动获取和更新证书。

安装 Certbot：

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install certbot python3-certbot-nginx

# CentOS/RHEL
sudo yum install epel-release
sudo yum install certbot python3-certbot-nginx
```

获取证书：

```bash
sudo certbot --nginx -d example.com -d www.example.com
```

自动更新证书：

```bash
sudo certbot renew --dry-run  # 测试更新
```

添加定时任务自动更新：

```bash
echo "0 3 * * * root certbot renew --quiet" | sudo tee -a /etc/crontab
```

### 2. 使用商业 SSL 证书

从商业 CA（如 DigiCert、Comodo、GlobalSign）购买证书的步骤：

1. 生成 CSR（证书签名请求）：
   ```bash
   openssl req -new -newkey rsa:2048 -nodes -keyout example.com.key -out example.com.csr
   ```

2. 将 CSR 提交给 CA 并完成验证流程

3. 接收并安装证书：
   ```bash
   # 将证书和私钥放在安全位置
   sudo mkdir -p /etc/nginx/ssl
   sudo cp example.com.crt /etc/nginx/ssl/
   sudo cp example.com.key /etc/nginx/ssl/
   sudo chmod 600 /etc/nginx/ssl/example.com.key
   ```

### 3. 自签名证书（仅用于测试）

```bash
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout /etc/nginx/ssl/self-signed.key \
     -out /etc/nginx/ssl/self-signed.crt
```

## 基本 SSL 配置

```nginx
server {
    listen 80;
    server_name example.com www.example.com;
    
    # 将 HTTP 重定向到 HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name example.com www.example.com;
    
    # SSL 证书
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    
    # 其他配置...
    root /var/www/html;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
}
```

## 安全增强配置

### 1. 优化 SSL 协议和加密套件

```nginx
server {
    listen 443 ssl;
    server_name example.com;
    
    # SSL 证书
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    
    # 协议
    ssl_protocols TLSv1.2 TLSv1.3;
    
    # 加密套件
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;
    
    # 其他配置...
}
```

### 2. 启用 HSTS

HTTP Strict Transport Security (HSTS) 告诉浏览器只使用 HTTPS 连接：

```nginx
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
```

### 3. 配置 SSL 会话缓存

```nginx
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;
```

### 4. 使用 OCSP Stapling

OCSP Stapling 允许服务器代表客户端检查证书状态：

```nginx
ssl_stapling on;
ssl_stapling_verify on;
ssl_trusted_certificate /etc/nginx/ssl/ca-certs.pem;
resolver ******* ******* valid=300s;
resolver_timeout 5s;
```

### 5. 配置 Diffie-Hellman 参数

生成 DH 参数：

```bash
openssl dhparam -out /etc/nginx/ssl/dhparam.pem 2048
```

在 Nginx 配置中使用：

```nginx
ssl_dhparam /etc/nginx/ssl/dhparam.pem;
```

## 完整的安全 SSL 配置

```nginx
server {
    listen 80;
    server_name example.com www.example.com;
    
    # 将 HTTP 重定向到 HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name example.com www.example.com;
    
    # SSL 证书
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    
    # 协议和加密套件
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;
    
    # DH 参数
    ssl_dhparam /etc/nginx/ssl/dhparam.pem;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/nginx/ssl/ca-certs.pem;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # 会话缓存
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # 其他安全头
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 网站配置
    root /var/www/html;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
}
```

## SSL 证书链配置

对于需要证书链的证书，将所有证书合并到一个文件中：

```bash
cat example.com.crt intermediate.crt root.crt > example.com.chained.crt
```

然后在 Nginx 中使用：

```nginx
ssl_certificate /etc/nginx/ssl/example.com.chained.crt;
ssl_certificate_key /etc/nginx/ssl/example.com.key;
```

## 多域名 SSL 配置

### 1. 使用多个证书

```nginx
server {
    listen 443 ssl;
    server_name example.com;
    
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    
    # 其他配置...
}

server {
    listen 443 ssl;
    server_name another.com;
    
    ssl_certificate /etc/nginx/ssl/another.com.crt;
    ssl_certificate_key /etc/nginx/ssl/another.com.key;
    
    # 其他配置...
}
```

### 2. 使用 SNI（服务器名称指示）

SNI 允许在同一 IP 地址上托管多个 SSL 网站：

```nginx
# Nginx 自动支持 SNI，只需配置多个 server 块
server {
    listen 443 ssl;
    server_name example.com;
    
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    
    # 其他配置...
}

server {
    listen 443 ssl;
    server_name another.com;
    
    ssl_certificate /etc/nginx/ssl/another.com.crt;
    ssl_certificate_key /etc/nginx/ssl/another.com.key;
    
    # 其他配置...
}
```

### 3. 使用通配符证书

```nginx
server {
    listen 443 ssl;
    server_name *.example.com;
    
    ssl_certificate /etc/nginx/ssl/wildcard.example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/wildcard.example.com.key;
    
    # 其他配置...
}
```

## SSL 性能优化

### 1. 启用 HTTP/2

HTTP/2 提供更好的性能，特别是对于 HTTPS：

```nginx
server {
    listen 443 ssl http2;
    server_name example.com;
    
    # SSL 配置...
}
```

### 2. 优化 SSL 会话复用

```nginx
ssl_session_cache shared:SSL:50m;  # 增加缓存大小
ssl_session_timeout 1h;            # 增加超时时间
```

### 3. 使用 OCSP Stapling 减少验证延迟

```nginx
ssl_stapling on;
ssl_stapling_verify on;
```

### 4. 启用 0-RTT 恢复（TLS 1.3）

```nginx
ssl_early_data on;
```

注意：这可能导致重放攻击，应谨慎使用。

### 5. 使用 CDN 分担 SSL 处理负担

将静态内容放在 CDN 上，减轻源服务器的 SSL 处理负担。

## 测试 SSL 配置

### 1. 使用 OpenSSL 测试

```bash
openssl s_client -connect example.com:443 -tls1_2
```

### 2. 在线 SSL 测试工具

- [SSL Labs Server Test](https://www.ssllabs.com/ssltest/)
- [ImmuniWeb SSL Security Test](https://www.immuniweb.com/ssl/)
- [SSL Checker](https://www.sslshopper.com/ssl-checker.html)

## 证书更新和管理

### 1. Let's Encrypt 自动更新

```bash
# 测试自动更新
sudo certbot renew --dry-run

# 添加到 crontab
echo "0 3 * * * root certbot renew --quiet --post-hook 'systemctl reload nginx'" | sudo tee -a /etc/crontab
```

### 2. 证书到期监控

设置监控系统检查证书到期日期：

```bash
# 检查证书到期日期
echo | openssl s_client -servername example.com -connect example.com:443 2>/dev/null | openssl x509 -noout -dates
```

### 3. 证书轮换最佳实践

1. 提前生成新证书
2. 在非高峰时段更新证书
3. 使用自动化工具管理证书
4. 保持私钥安全

## 常见问题和解决方案

### 1. 证书不受信任

原因：证书链不完整或使用自签名证书

解决方案：
- 确保包含完整的证书链
- 使用受信任的 CA 签发的证书

### 2. 证书与域名不匹配

原因：证书中的域名与访问的域名不一致

解决方案：
- 确保证书包含正确的域名
- 对于多域名，使用 SAN 证书或通配符证书

### 3. 混合内容警告

原因：HTTPS 页面加载 HTTP 资源

解决方案：
- 确保所有资源使用 HTTPS
- 使用相对 URL 或协议相对 URL（`//example.com/resource`）
- 添加 Content-Security-Policy 头

```nginx
add_header Content-Security-Policy "upgrade-insecure-requests";
```

### 4. 性能问题

原因：SSL 处理增加了服务器负载

解决方案：
- 启用会话缓存
- 使用 HTTP/2
- 考虑使用 CDN
- 优化加密套件选择

## SSL 配置检查清单

- [ ] 使用强加密套件和协议（TLS 1.2+）
- [ ] 启用 HSTS
- [ ] 配置证书链
- [ ] 启用 OCSP Stapling
- [ ] 配置 DH 参数
- [ ] 启用 HTTP/2
- [ ] 设置会话缓存
- [ ] 配置自动证书更新
- [ ] 测试 SSL 配置（使用 SSL Labs）
- [ ] 添加安全头（X-Frame-Options 等）
- [ ] 将 HTTP 重定向到 HTTPS

## 总结

本章介绍了 Nginx 的 SSL/HTTPS 配置，包括证书获取、安全配置、性能优化和最佳实践。正确配置 SSL 对于保护网站和用户数据至关重要。在下一章中，我们将探讨 Nginx 的安全最佳实践。
