# Nginx 常见使用场景

本章将介绍 Nginx 的常见使用场景和实际应用案例，包括配置示例和最佳实践。

## Nginx 的多功能性

Nginx 因其灵活性和高性能而被广泛应用于各种场景：

```mermaid
graph TD
    A[Nginx] --> B[静态内容服务器]
    A --> C[反向代理]
    A --> D[负载均衡器]
    A --> E[API 网关]
    A --> F[缓存服务器]
    A --> G[媒体流服务器]
    A --> H[微服务架构]
    A --> I[SSL 终结]
```

## 场景 1: 静态网站托管

### 基本配置

```nginx
server {
    listen 80;
    server_name example.com www.example.com;
    
    root /var/www/example.com;
    index index.html;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}
```

### 使用 HTTP/2 和 SSL

```nginx
server {
    listen 443 ssl http2;
    server_name example.com www.example.com;
    
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    
    # SSL 配置...
    
    root /var/www/example.com;
    index index.html;
    
    # 静态文件配置...
}
```

### 单页应用 (SPA)

```nginx
server {
    listen 80;
    server_name spa.example.com;
    
    root /var/www/spa;
    index index.html;
    
    # 所有请求路由到 index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源
    location /assets/ {
        expires 30d;
    }
}
```

## 场景 2: 反向代理

### 代理到 Node.js 应用

```nginx
server {
    listen 80;
    server_name api.example.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 代理到 PHP 应用

```nginx
server {
    listen 80;
    server_name php.example.com;
    
    root /var/www/php;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 代理到 Python 应用

```nginx
server {
    listen 80;
    server_name python.example.com;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件
    location /static/ {
        alias /var/www/python/static/;
        expires 30d;
    }
}
```

## 场景 3: 负载均衡

### HTTP 负载均衡

```nginx
upstream backend {
    server backend1.example.com weight=3;
    server backend2.example.com weight=2;
    server backend3.example.com weight=1 backup;
    
    keepalive 32;
}

server {
    listen 80;
    server_name lb.example.com;
    
    location / {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 基于地理位置的负载均衡

```nginx
# 根据国家/地区路由流量
geo $country {
    default US;
    10.0.0.0/8 US;
    ***********/16 EU;
    **********/12 ASIA;
}

upstream us_servers {
    server us1.example.com;
    server us2.example.com;
}

upstream eu_servers {
    server eu1.example.com;
    server eu2.example.com;
}

upstream asia_servers {
    server asia1.example.com;
    server asia2.example.com;
}

server {
    listen 80;
    server_name geo.example.com;
    
    location / {
        set $backend "";
        
        if ($country = "US") {
            set $backend "us_servers";
        }
        
        if ($country = "EU") {
            set $backend "eu_servers";
        }
        
        if ($country = "ASIA") {
            set $backend "asia_servers";
        }
        
        proxy_pass http://$backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 带有会话保持的负载均衡

```nginx
upstream backend {
    ip_hash;  # 基于客户端 IP 的会话保持
    server backend1.example.com;
    server backend2.example.com;
}

# 或使用 cookie 进行会话保持 (Nginx Plus)
upstream backend {
    sticky cookie srv_id expires=1h domain=.example.com path=/;
    server backend1.example.com;
    server backend2.example.com;
}
```

## 场景 4: API 网关

### 基本 API 网关

```nginx
server {
    listen 80;
    server_name api.example.com;
    
    # 用户服务
    location /api/users/ {
        proxy_pass http://user-service:8001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 产品服务
    location /api/products/ {
        proxy_pass http://product-service:8002/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 订单服务
    location /api/orders/ {
        proxy_pass http://order-service:8003/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API 限流
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
    }
}
```

### 带认证的 API 网关

```nginx
server {
    listen 80;
    server_name api.example.com;
    
    # JWT 验证
    location /api/ {
        auth_jwt "API";
        auth_jwt_key_file /etc/nginx/jwt/jwt.key;
        
        # 验证失败时
        error_page 401 = @error401;
    }
    
    location @error401 {
        return 401 '{"error":"Unauthorized","message":"Authentication required"}';
    }
    
    # 路由到不同服务...
}
```

### API 文档

```nginx
server {
    listen 80;
    server_name api.example.com;
    
    # API 文档
    location /docs/ {
        alias /var/www/api-docs/;
        index index.html;
    }
    
    # Swagger UI
    location /swagger/ {
        proxy_pass http://swagger-ui:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API 路由...
}
```

## 场景 5: 微服务架构

### 微服务网关

```nginx
server {
    listen 80;
    server_name example.com;
    
    # 前端应用
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 用户服务
    location /api/users/ {
        proxy_pass http://user-service:8001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 产品服务
    location /api/products/ {
        proxy_pass http://product-service:8002/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 订单服务
    location /api/orders/ {
        proxy_pass http://order-service:8003/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 通知服务
    location /api/notifications/ {
        proxy_pass http://notification-service:8004/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 服务发现集成

```nginx
# 使用 Consul 模板动态生成配置
upstream backend {
    # 由 Consul-Template 动态生成
    server service1.example.com:8080;
    server service2.example.com:8080;
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 场景 6: 媒体流服务器

### 视频流

```nginx
server {
    listen 80;
    server_name media.example.com;
    
    # HLS 流
    location /hls/ {
        root /var/www/media;
        add_header Cache-Control no-cache;
    }
    
    # MP4 视频
    location ~ \.mp4$ {
        root /var/www/media;
        mp4;
        mp4_buffer_size 1m;
        mp4_max_buffer_size 5m;
    }
    
    # 视频缩略图
    location ~ \.jpg$ {
        root /var/www/media/thumbnails;
        expires 30d;
    }
}
```

### 大文件下载

```nginx
server {
    listen 80;
    server_name download.example.com;
    
    # 大文件下载
    location /downloads/ {
        root /var/www;
        limit_rate 1m;  # 限制下载速度为 1MB/s
        limit_conn addr 1;  # 每个 IP 只允许一个连接
    }
}
```

## 场景 7: 多环境部署

### 开发环境

```nginx
server {
    listen 80;
    server_name dev.example.com;
    
    # 开发环境特定配置
    location / {
        proxy_pass http://dev-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # 禁用缓存
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }
}
```

### 测试环境

```nginx
server {
    listen 80;
    server_name test.example.com;
    
    # 基本认证
    auth_basic "Test Environment";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    location / {
        proxy_pass http://test-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 生产环境

```nginx
server {
    listen 443 ssl http2;
    server_name example.com;
    
    # SSL 配置...
    
    # 生产环境特定配置
    location / {
        proxy_pass http://prod-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # 启用缓存
        proxy_cache prod_cache;
        proxy_cache_valid 200 10m;
    }
}
```

## 场景 8: A/B 测试和金丝雀发布

### A/B 测试

```nginx
split_clients "${remote_addr}${http_user_agent}" $variant {
    20%     "A";
    20%     "B";
    *       "original";
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        if ($variant = "A") {
            proxy_pass http://version-a;
        }
        
        if ($variant = "B") {
            proxy_pass http://version-b;
        }
        
        if ($variant = "original") {
            proxy_pass http://original;
        }
        
        # 添加变体标识到响应头
        add_header X-Variant $variant;
    }
}
```

### 金丝雀发布

```nginx
# 将 10% 的流量发送到新版本
upstream backend {
    server old-version.example.com weight=9;
    server new-version.example.com weight=1;
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 场景 9: 高可用性设置

### 主动-被动设置

```nginx
# 主服务器
server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # 健康检查
        health_check interval=5s fails=3 passes=2;
    }
}

# 备份服务器
upstream backend {
    server primary.example.com;
    server backup.example.com backup;
}
```

### 使用 keepalived 实现 Nginx 高可用

```bash
# 安装 keepalived
sudo apt install keepalived

# 配置 keepalived
sudo nano /etc/keepalived/keepalived.conf
```

配置内容：

```
vrrp_script check_nginx {
    script "pidof nginx"
    interval 2
    weight 2
}

vrrp_instance VI_1 {
    state MASTER
    interface eth0
    virtual_router_id 51
    priority 101
    advert_int 1
    
    authentication {
        auth_type PASS
        auth_pass secret
    }
    
    virtual_ipaddress {
        *************
    }
    
    track_script {
        check_nginx
    }
}
```

## 总结

本章介绍了 Nginx 的常见使用场景和实际应用案例，包括静态网站托管、反向代理、负载均衡、API 网关、微服务架构、媒体流服务器、多环境部署、A/B 测试和高可用性设置。这些场景展示了 Nginx 的灵活性和强大功能，可以根据具体需求进行定制和优化。在下一章中，我们将比较 Nginx 与其他 Web 服务器的异同。
