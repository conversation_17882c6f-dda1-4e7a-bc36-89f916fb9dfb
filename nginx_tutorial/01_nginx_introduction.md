# Nginx 简介与架构

## 什么是 Nginx？

Nginx（发音为"engine-x"）是一个高性能的开源 Web 服务器、反向代理服务器、负载均衡器、邮件代理服务器和 HTTP 缓存。它以其高性能、稳定性、丰富的功能集、简单的配置和低资源消耗而闻名。

Nginx 最初由 Igor Sysoev 创建，用于解决 C10K 问题（同时处理 10,000 个客户端连接的问题）。如今，它已成为全球最受欢迎的 Web 服务器之一，被众多高流量网站如 Netflix、GitHub、Airbnb 等采用。

## Nginx 的主要特点

- **高性能**：能够处理大量并发连接，占用内存少
- **高可靠性**：稳定运行，很少出现崩溃
- **模块化设计**：功能可以通过模块扩展
- **异步非阻塞**：处理请求时不会阻塞进程
- **事件驱动架构**：高效处理连接和请求
- **单机支持 10 万以上的并发连接**：适合高流量网站
- **热部署**：不停机更新配置、升级服务
- **BSD 许可证**：可以免费使用，包括商业用途

## Nginx 架构

Nginx 采用主从架构（Master-Worker 模式），包含一个主进程（Master Process）和多个工作进程（Worker Processes）。

```mermaid
graph TD
    A[用户请求] --> B[Nginx]
    B --> C[主进程 Master]
    C --> D[工作进程 Worker 1]
    C --> E[工作进程 Worker 2]
    C --> F[工作进程 Worker n]
    D --> G[处理请求]
    E --> G
    F --> G
    G --> H[返回响应]
```

### 主进程（Master Process）

主进程负责：
- 读取和验证配置
- 创建、维护和监控工作进程
- 接收信号并传递给工作进程
- 平滑升级（优雅重启）

主进程不处理任何客户端请求。

### 工作进程（Worker Processes）

工作进程负责：
- 接受和处理客户端连接
- 处理请求
- 提供响应

Nginx 通常会创建与 CPU 核心数量相等的工作进程，以最大化硬件利用率。

## Nginx 的事件驱动模型

Nginx 使用事件驱动的异步非阻塞模型来处理连接，这是其高性能的关键。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Worker as 工作进程
    participant EventLoop as 事件循环
    participant Backend as 后端服务器
    
    Client->>Worker: 发送请求
    Worker->>EventLoop: 注册事件
    EventLoop->>Worker: 事件就绪通知
    Worker->>Backend: 转发请求
    Backend->>Worker: 返回响应
    Worker->>Client: 发送响应
```

这种模型允许 Nginx 使用少量的工作进程处理大量的并发连接，因为工作进程不会在 I/O 操作上阻塞。

## Nginx 处理请求的流程

下面是 Nginx 处理 HTTP 请求的基本流程：

```mermaid
flowchart TD
    A[客户端发送请求] --> B[Nginx 接收请求]
    B --> C{是静态内容?}
    C -->|是| D[直接提供文件]
    C -->|否| E[转发到后端服务器]
    E --> F[后端处理请求]
    F --> G[返回响应给 Nginx]
    G --> H[Nginx 处理响应]
    D --> H
    H --> I[返回响应给客户端]
```

## Nginx 与其他 Web 服务器的比较

与传统的 Web 服务器（如 Apache）相比，Nginx 采用了不同的架构方法：

```mermaid
graph TB
    subgraph "Apache 架构"
    A1[进程/线程 1] --> B1[客户端 1]
    A2[进程/线程 2] --> B2[客户端 2]
    A3[进程/线程 3] --> B3[客户端 3]
    end
    
    subgraph "Nginx 架构"
    C1[工作进程 1] --> D1[事件循环]
    D1 --> E1[客户端 1]
    D1 --> E2[客户端 2]
    D1 --> E3[客户端 3]
    end
```

- **Apache**：为每个请求创建一个进程或线程，在高并发下消耗大量资源
- **Nginx**：使用少量工作进程和非阻塞 I/O 处理多个请求，资源消耗低

## Nginx 的常见用途

1. **Web 服务器**：提供静态内容
2. **反向代理**：将请求转发到后端服务器
3. **负载均衡器**：分发流量到多个服务器
4. **API 网关**：管理和保护 API
5. **缓存服务器**：缓存静态和动态内容
6. **SSL 终结**：处理 HTTPS 加密/解密
7. **媒体流服务器**：提供视频和音频流

## 总结

Nginx 是一个功能强大、高性能的 Web 服务器和反向代理服务器，其事件驱动的异步架构使其能够高效处理大量并发连接。在接下来的章节中，我们将深入探讨 Nginx 的安装、配置和各种使用场景。
