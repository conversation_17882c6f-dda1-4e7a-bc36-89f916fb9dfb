# Nginx 配置示例详解

本章将详细解释各种 Nginx 配置示例中每个配置项的作用和效果，帮助您深入理解 Nginx 配置的工作原理。

## 基本 HTTP 服务器配置

```nginx
server {
    listen 80;                      # 监听80端口（HTTP）
    server_name example.com;        # 匹配的域名
    
    root /var/www/example.com;      # 网站根目录
    index index.html index.htm;     # 默认索引文件
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;                # 设置过期时间为30天
        add_header Cache-Control "public, no-transform";  # 允许公共缓存，禁止转换
    }
    
    # 错误页面
    error_page 404 /404.html;       # 自定义404错误页面
    error_page 500 502 503 504 /50x.html;  # 自定义服务器错误页面
}
```

**作用和效果**：
- `listen 80`: 让 Nginx 监听服务器的 80 端口，接收 HTTP 请求
- `server_name`: 定义虚拟主机的域名，当请求的 Host 头匹配此域名时，使用此配置块
- `root`: 指定网站文件的根目录，所有相对路径都基于此目录
- `index`: 当请求目录时，Nginx 会按顺序查找这些文件作为默认页面
- `location ~* \.(jpg|jpeg|png|gif|ico|css|js)$`: 使用正则表达式匹配静态资源文件
  - `expires 30d`: 告诉浏览器缓存这些文件 30 天，减少重复请求
  - `add_header Cache-Control`: 设置缓存控制头，允许 CDN 等公共缓存，防止内容被转换
- `error_page`: 自定义错误页面，提升用户体验

**效果**：这个配置创建了一个基本的 HTTP 服务器，提供静态文件服务，并对图片、CSS、JS 等静态资源进行缓存优化，同时自定义了错误页面。

## 反向代理配置

```nginx
server {
    listen 80;
    server_name api.example.com;
    
    location / {
        proxy_pass http://localhost:3000;  # 将请求转发到本地3000端口
        proxy_http_version 1.1;            # 使用HTTP/1.1协议
        proxy_set_header Upgrade $http_upgrade;  # 支持WebSocket
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;       # 传递原始主机名
        proxy_set_header X-Real-IP $remote_addr;  # 传递客户端真实IP
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;  # 传递代理链信息
        proxy_set_header X-Forwarded-Proto $scheme;  # 传递原始协议(http/https)
        proxy_cache_bypass $http_upgrade;  # WebSocket连接绕过缓存
    }
}
```

**作用和效果**：
- `proxy_pass`: 核心指令，将请求转发到指定的后端服务器
- `proxy_http_version 1.1`: 使用 HTTP/1.1 协议与后端通信，支持长连接
- `proxy_set_header Upgrade` 和 `Connection`: 支持 WebSocket 协议升级
- `proxy_set_header Host`: 将原始请求的主机名传递给后端，使后端能识别是哪个域名的请求
- `proxy_set_header X-Real-IP`: 传递客户端的真实 IP 地址给后端
- `proxy_set_header X-Forwarded-For`: 传递完整的客户端 IP 链，包括所有经过的代理
- `proxy_set_header X-Forwarded-Proto`: 告诉后端原始请求使用的协议(HTTP/HTTPS)
- `proxy_cache_bypass`: 确保 WebSocket 连接不被缓存

**效果**：这个配置将所有发送到 `api.example.com` 的请求转发到本地 3000 端口运行的服务，同时保留原始请求信息（如客户端 IP、主机名等），并支持 WebSocket 连接。这种配置常用于前端 Nginx 和后端应用服务器（如 Node.js、Python、Java 等）的架构中。

## 负载均衡配置

```nginx
upstream backend {
    server backend1.example.com weight=3;  # 权重为3
    server backend2.example.com weight=2;  # 权重为2
    server backend3.example.com weight=1 backup;  # 备份服务器
    
    keepalive 32;  # 保持连接数
}

server {
    listen 80;
    server_name lb.example.com;
    
    location / {
        proxy_pass http://backend;  # 引用upstream块
        proxy_http_version 1.1;
        proxy_set_header Connection "";  # 清除Connection头以启用keepalive
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

**作用和效果**：
- `upstream backend`: 定义一组后端服务器
  - `weight`: 设置服务器权重，权重越高分配的请求越多
  - `backup`: 标记为备份服务器，只有当主服务器不可用时才使用
  - `keepalive 32`: 每个 worker 进程保持 32 个到上游服务器的空闲连接
- `proxy_pass http://backend`: 将请求转发到定义的后端服务器组
- `proxy_set_header Connection ""`: 清除 Connection 头，允许与后端使用 keepalive 连接

**效果**：这个配置创建了一个负载均衡器，将请求按照 3:2:0 的比例分配给 backend1 和 backend2，只有当这两个服务器都不可用时才使用 backend3。使用 keepalive 连接提高性能，减少 TCP 连接建立的开销。

## 缓存配置

```nginx
# 定义缓存区域
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g 
                 inactive=60m use_temp_path=off;

server {
    listen 80;
    server_name cache.example.com;
    
    # 启用缓存
    proxy_cache my_cache;
    proxy_cache_valid 200 302 10m;  # 对成功响应缓存10分钟
    proxy_cache_valid 404 1m;       # 对404响应缓存1分钟
    proxy_cache_lock on;            # 防止缓存雪崩
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;  # 后端出错时使用过期缓存
    proxy_cache_revalidate on;      # 使用条件请求验证缓存
    add_header X-Cache-Status $upstream_cache_status;  # 添加缓存状态头
    
    location / {
        proxy_pass http://backend;
    }
}
```

**作用和效果**：
- `proxy_cache_path`: 定义缓存的存储位置和参数
  - `levels=1:2`: 创建两级目录结构存储缓存文件，提高文件系统性能
  - `keys_zone=my_cache:10m`: 分配 10MB 共享内存区域存储缓存键和元数据
  - `max_size=10g`: 缓存最大容量为 10GB
  - `inactive=60m`: 60 分钟未被访问的缓存项将被删除
  - `use_temp_path=off`: 直接写入缓存目录，提高性能
- `proxy_cache`: 启用缓存并指定使用的缓存区域
- `proxy_cache_valid`: 设置不同 HTTP 状态码的缓存时间
- `proxy_cache_lock`: 防止多个相同请求同时发送到后端（缓存雪崩）
- `proxy_cache_use_stale`: 当后端出错时使用过期的缓存，提高可用性
- `proxy_cache_revalidate`: 使用 If-Modified-Since 等条件请求验证缓存
- `add_header X-Cache-Status`: 添加响应头显示缓存状态，便于调试

**效果**：这个配置创建了一个高效的缓存代理，可以缓存后端响应，减少后端负载，提高响应速度，并在后端出现问题时仍能提供服务。

## SSL/HTTPS 配置

```nginx
server {
    listen 443 ssl http2;  # 监听443端口，启用SSL和HTTP/2
    server_name secure.example.com;
    
    # SSL证书
    ssl_certificate /etc/nginx/ssl/example.com.crt;  # 证书文件
    ssl_certificate_key /etc/nginx/ssl/example.com.key;  # 私钥文件
    
    # 协议和加密套件
    ssl_protocols TLSv1.2 TLSv1.3;  # 只使用TLS 1.2和1.3
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';  # 安全的加密套件
    ssl_prefer_server_ciphers on;  # 优先使用服务器的加密套件
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;  # 强制HTTPS
    
    # OCSP Stapling
    ssl_stapling on;  # 启用OCSP Stapling
    ssl_stapling_verify on;  # 验证OCSP响应
    
    # 会话缓存
    ssl_session_cache shared:SSL:10m;  # 10MB共享SSL会话缓存
    ssl_session_timeout 10m;  # 会话超时时间
    
    location / {
        root /var/www/secure;
        index index.html;
    }
}
```

**作用和效果**：
- `listen 443 ssl http2`: 在 443 端口监听 HTTPS 请求，并启用 HTTP/2 协议
- `ssl_certificate` 和 `ssl_certificate_key`: 指定 SSL 证书和私钥文件
- `ssl_protocols`: 限制只使用安全的 TLS 版本，禁用不安全的 SSL 版本
- `ssl_ciphers`: 指定允许的加密套件，只使用安全的现代加密算法
- `ssl_prefer_server_ciphers`: 优先使用服务器定义的加密套件顺序
- `add_header Strict-Transport-Security`: 启用 HSTS，告诉浏览器只使用 HTTPS 连接
  - `max-age=63072000`: HSTS 有效期为 2 年
  - `includeSubDomains`: 包括所有子域名
  - `preload`: 允许被包含在浏览器的预加载 HSTS 列表中
- `ssl_stapling`: 启用 OCSP Stapling，服务器主动获取证书状态并随 SSL 握手发送给客户端
- `ssl_session_cache`: 配置 SSL 会话缓存，减少完整 SSL 握手的次数

**效果**：这个配置创建了一个安全的 HTTPS 服务器，使用最新的安全标准和协议，提供强大的加密和性能优化。HSTS 确保浏览器总是使用 HTTPS 连接，OCSP Stapling 提高了证书验证的性能，会话缓存减少了 SSL 握手的开销。

## 安全配置

```nginx
server {
    # 隐藏版本信息
    server_tokens off;  # 隐藏Nginx版本
    
    # 安全响应头
    add_header X-Frame-Options "SAMEORIGIN" always;  # 防止点击劫持
    add_header X-Content-Type-Options "nosniff" always;  # 防止MIME类型嗅探
    add_header X-XSS-Protection "1; mode=block" always;  # 启用XSS保护
    add_header Content-Security-Policy "default-src 'self';" always;  # 内容安全策略
    
    # 限制请求方法
    if ($request_method !~ ^(GET|POST|HEAD)$) {
        return 405;  # 方法不允许
    }
    
    # 防止目录遍历
    autoindex off;  # 禁用目录列表
    
    # 保护敏感文件
    location ~ /\.ht {
        deny all;  # 拒绝访问.htaccess等文件
    }
    
    # 限制请求速率
    limit_req_zone $binary_remote_addr zone=req_limit:10m rate=10r/s;
    location / {
        limit_req zone=req_limit burst=20 nodelay;  # 限制每IP每秒10个请求，允许20个请求的突发
    }
}
```

**作用和效果**：
- `server_tokens off`: 隐藏 Nginx 版本信息，减少信息泄露
- 安全响应头：
  - `X-Frame-Options`: 控制页面是否可以在 frame 中显示，防止点击劫持攻击
  - `X-Content-Type-Options`: 防止浏览器猜测文件 MIME 类型，减少 XSS 风险
  - `X-XSS-Protection`: 启用浏览器内置的 XSS 过滤器
  - `Content-Security-Policy`: 限制页面可以加载的资源，防止 XSS 和数据注入
- 请求方法限制：只允许 GET、POST 和 HEAD 方法，拒绝其他可能危险的 HTTP 方法
- `autoindex off`: 禁止目录列表，防止目录遍历攻击
- `location ~ /\.ht`: 阻止访问以点开头的隐藏文件，如 .htaccess
- 请求速率限制：
  - `limit_req_zone`: 定义限制区域，每 IP 每秒最多 10 个请求
  - `limit_req`: 应用限制，允许 20 个请求的突发，超过立即拒绝

**效果**：这个配置实现了多层安全防护，包括信息隐藏、XSS 防护、点击劫持防护、目录遍历防护、敏感文件保护和请求速率限制，有效防止常见的 Web 攻击。

## 性能优化配置

```nginx
worker_processes auto;  # 自动设置为CPU核心数
worker_rlimit_nofile 65535;  # 增加worker进程文件描述符限制

events {
    worker_connections 4096;  # 每个worker进程的最大连接数
    multi_accept on;  # 一次接受所有新连接
    use epoll;  # 使用epoll事件模型
}

http {
    # 基本优化
    sendfile on;  # 使用sendfile系统调用
    tcp_nopush on;  # 优化数据包发送
    tcp_nodelay on;  # 禁用Nagle算法
    
    # 缓冲区优化
    client_body_buffer_size 16k;  # 请求体缓冲区
    client_header_buffer_size 1k;  # 请求头缓冲区
    client_max_body_size 10m;  # 最大请求体大小
    large_client_header_buffers 4 8k;  # 大请求头缓冲区
    
    # 超时设置
    client_body_timeout 12;  # 读取请求体超时
    client_header_timeout 12;  # 读取请求头超时
    keepalive_timeout 65;  # 保持连接超时
    send_timeout 10;  # 发送响应超时
    
    # 文件缓存
    open_file_cache max=200000 inactive=20s;  # 打开文件缓存
    open_file_cache_valid 30s;  # 缓存验证时间
    open_file_cache_min_uses 2;  # 最小使用次数
    open_file_cache_errors on;  # 缓存错误
    
    # 压缩
    gzip on;  # 启用gzip压缩
    gzip_comp_level 5;  # 压缩级别
    gzip_min_length 256;  # 最小压缩文件大小
    gzip_types text/plain text/css application/javascript;  # 压缩的MIME类型
}
```

**作用和效果**：
- `worker_processes auto`: 自动设置 worker 进程数为 CPU 核心数，充分利用多核
- `worker_rlimit_nofile`: 增加每个 worker 进程可打开的文件描述符数量，支持更多并发连接
- `worker_connections`: 设置每个 worker 进程的最大连接数
- `multi_accept`: 允许 worker 一次接受多个新连接，提高高并发下的性能
- `use epoll`: 使用 Linux 下高效的 epoll 事件模型
- `sendfile`、`tcp_nopush`、`tcp_nodelay`: 优化网络传输，减少系统调用和延迟
- 缓冲区设置：根据请求特点优化各种缓冲区大小，减少内存使用
- 超时设置：防止慢客户端占用连接，释放资源
- `open_file_cache`: 缓存打开的文件描述符，减少重复打开文件的开销
- `gzip`: 启用压缩，减少传输数据量，提高加载速度

**效果**：这个配置通过多方面的优化，显著提高了 Nginx 的性能和并发处理能力，减少了资源消耗，适合高流量网站使用。

## 微服务架构配置

```nginx
server {
    listen 80;
    server_name example.com;
    
    # 前端应用
    location / {
        proxy_pass http://frontend:3000;  # 前端服务
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 用户服务API
    location /api/users/ {
        proxy_pass http://user-service:8001/;  # 用户微服务
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 产品服务API
    location /api/products/ {
        proxy_pass http://product-service:8002/;  # 产品微服务
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 订单服务API
    location /api/orders/ {
        proxy_pass http://order-service:8003/;  # 订单微服务
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API限流
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;  # 限制API请求速率
    }
}
```

**作用和效果**：
- 前端应用代理：将根路径请求转发到前端应用服务
- 微服务 API 路由：
  - 基于路径前缀将请求路由到不同的微服务
  - `/api/users/` 路由到用户服务
  - `/api/products/` 路由到产品服务
  - `/api/orders/` 路由到订单服务
- 请求头传递：确保每个微服务都能获取原始请求信息
- API 限流：防止 API 被滥用，保护后端服务

**效果**：这个配置创建了一个 API 网关，将来自客户端的请求路由到适当的微服务，实现了服务的解耦和独立扩展。同时通过限流保护后端服务不被过载。

## 总结

通过详细解释这些配置示例，我们可以看到 Nginx 配置的强大和灵活性。每个配置指令都有其特定的作用，组合在一起可以实现各种复杂的功能。理解这些配置项的作用和效果，将帮助您更好地定制 Nginx 以满足特定需求。

在实际应用中，您可以根据自己的需求组合和调整这些配置，创建最适合您的环境的 Nginx 配置。
