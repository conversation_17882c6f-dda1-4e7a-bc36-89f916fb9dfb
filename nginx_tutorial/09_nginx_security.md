# Nginx 安全最佳实践

本章将详细介绍 Nginx 的安全配置和最佳实践，帮助您保护 Web 服务器免受常见威胁。

## Web 安全的重要性

Web 服务器是互联网上最常见的攻击目标之一。保护 Nginx 服务器对于维护网站的可用性、完整性和机密性至关重要。常见的 Web 安全威胁包括：

1. 分布式拒绝服务 (DDoS) 攻击
2. SQL 注入
3. 跨站脚本 (XSS)
4. 跨站请求伪造 (CSRF)
5. 点击劫持
6. 信息泄露
7. 中间人攻击
8. 暴力破解攻击

## Nginx 安全架构

```mermaid
graph TD
    A[客户端请求] --> B[防火墙]
    B --> C[DDoS 保护]
    C --> D[Nginx 前端]
    D --> E[访问控制]
    D --> F[请求过滤]
    D --> G[SSL/TLS]
    E --> H[应用服务器]
    F --> H
    G --> H
```

## 隐藏 Nginx 版本信息

默认情况下，Nginx 会在错误页面和响应头中显示版本信息，这可能帮助攻击者识别特定版本的漏洞。

```nginx
# 在 http 块中添加
http {
    server_tokens off;
}
```

## 配置安全的 HTTP 响应头

添加安全相关的 HTTP 头可以增强浏览器的安全性：

```nginx
server {
    # 防止点击劫持
    add_header X-Frame-Options "SAMEORIGIN" always;
    
    # 防止 MIME 类型嗅探
    add_header X-Content-Type-Options "nosniff" always;
    
    # 启用 XSS 保护
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 内容安全策略
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' https://trusted-cdn.com; img-src 'self' data: https://trusted-cdn.com; style-src 'self' https://trusted-cdn.com; font-src 'self' https://trusted-cdn.com; connect-src 'self'; frame-ancestors 'self'; form-action 'self';" always;
    
    # 引用策略
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 权限策略
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;
}
```

## 限制请求方法

限制只允许必要的 HTTP 方法：

```nginx
server {
    # 只允许 GET, POST 和 HEAD 方法
    if ($request_method !~ ^(GET|POST|HEAD)$) {
        return 405;
    }
}
```

## 访问控制

### IP 地址限制

限制特定路径只能从特定 IP 访问：

```nginx
location /admin/ {
    # 允许内部网络
    allow ***********/24;
    allow 10.0.0.0/8;
    # 拒绝其他所有 IP
    deny all;
}
```

### 基本认证

使用基本认证保护敏感区域：

```nginx
location /private/ {
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/.htpasswd;
}
```

创建密码文件：

```bash
sudo apt install apache2-utils
sudo htpasswd -c /etc/nginx/.htpasswd username
```

## 防止目录遍历

确保用户不能浏览目录内容：

```nginx
server {
    # 禁用目录列表
    autoindex off;
    
    # 尝试查找索引文件，如果不存在则返回 403
    location / {
        try_files $uri $uri/ =404;
    }
}
```

## 保护敏感文件

防止访问敏感文件和目录：

```nginx
# 拒绝访问隐藏文件
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

# 拒绝访问备份和源文件
location ~ ~$ {
    deny all;
}

# 保护配置文件
location ~ \.(conf|ini|sql|log)$ {
    deny all;
}
```

## 限制缓冲区大小

防止缓冲区溢出攻击：

```nginx
http {
    # 客户端请求体大小限制
    client_max_body_size 10m;
    
    # 请求缓冲区大小
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # 超时设置
    client_body_timeout 10s;
    client_header_timeout 10s;
    keepalive_timeout 65s;
    send_timeout 10s;
}
```

## 防止 DDoS 攻击

### 连接限制

```nginx
# 限制每个 IP 的连接数
http {
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    server {
        # 限制每个 IP 最多 10 个并发连接
        limit_conn conn_limit_per_ip 10;
    }
}
```

### 请求速率限制

```nginx
# 限制请求速率
http {
    limit_req_zone $binary_remote_addr zone=req_limit_per_ip:10m rate=10r/s;
    
    server {
        # 限制每个 IP 每秒最多 10 个请求，允许突发 20 个请求
        limit_req zone=req_limit_per_ip burst=20 nodelay;
    }
}
```

### 特定 URI 的限制

```nginx
# 对登录页面进行更严格的限制
location /login {
    limit_req zone=req_limit_per_ip burst=5 nodelay;
}
```

## 防止 SQL 注入和 XSS

虽然主要由应用程序负责防止 SQL 注入和 XSS，但 Nginx 可以提供额外的保护层：

```nginx
# 使用 ModSecurity Web 应用防火墙
load_module modules/ngx_http_modsecurity_module.so;

server {
    modsecurity on;
    modsecurity_rules_file /etc/nginx/modsecurity/main.conf;
}
```

## 文件上传安全

限制文件上传大小和类型：

```nginx
location /upload {
    # 限制上传大小
    client_max_body_size 10m;
    
    # 限制文件类型（在应用层进一步验证）
    if ($request_uri ~* \.(php|pl|py|jsp|asp|cgi|exe)$) {
        return 403;
    }
    
    # 代理到后端
    proxy_pass http://backend;
}
```

## SSL/TLS 安全配置

确保使用安全的 SSL/TLS 配置（详见上一章）：

```nginx
server {
    listen 443 ssl http2;
    
    # 证书
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    
    # 协议和加密套件
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
}
```

## 安全的 Nginx 用户

确保 Nginx 以非 root 用户运行：

```nginx
# 在 nginx.conf 的顶部
user nginx;
```

确保该用户只有必要的权限：

```bash
# 创建 nginx 用户和组
sudo groupadd -r nginx
sudo useradd -r -g nginx -s /sbin/nologin -d /var/cache/nginx -c "Nginx user" nginx

# 设置目录权限
sudo chown -R nginx:nginx /var/cache/nginx
sudo chown -R nginx:nginx /var/log/nginx
```

## 定期更新 Nginx

保持 Nginx 更新到最新版本，以修复已知的安全漏洞：

```bash
# Ubuntu/Debian
sudo apt update
sudo apt upgrade nginx

# CentOS/RHEL
sudo yum update nginx
```

## 安全监控和日志

配置详细的日志记录以便监控安全事件：

```nginx
http {
    # 定义详细的日志格式
    log_format detailed '$remote_addr - $remote_user [$time_local] '
                        '"$request" $status $body_bytes_sent '
                        '"$http_referer" "$http_user_agent" '
                        '"$http_x_forwarded_for" $request_time '
                        '$upstream_response_time $pipe';
    
    # 访问日志
    access_log /var/log/nginx/access.log detailed;
    
    # 错误日志
    error_log /var/log/nginx/error.log warn;
}
```

## 使用 fail2ban 防止暴力破解

安装和配置 fail2ban 以检测和阻止恶意活动：

```bash
# 安装 fail2ban
sudo apt install fail2ban

# 创建 Nginx 配置
sudo nano /etc/fail2ban/jail.d/nginx.conf
```

配置内容：

```ini
[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 5
findtime = 600
bantime = 3600

[nginx-login]
enabled = true
filter = nginx-login
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 5
findtime = 600
bantime = 3600
```

## 使用 WAF 保护 Nginx

Web 应用防火墙 (WAF) 可以提供额外的安全层：

### ModSecurity

```bash
# 安装 ModSecurity
sudo apt install libmodsecurity3 libmodsecurity-dev

# 下载和编译 Nginx 连接器
git clone --depth 1 https://github.com/SpiderLabs/ModSecurity-nginx.git
cd ModSecurity-nginx
sudo nginx-build --add-module=$(pwd)

# 配置 ModSecurity
sudo mkdir -p /etc/nginx/modsecurity
sudo cp /etc/modsecurity/modsecurity.conf-recommended /etc/nginx/modsecurity/modsecurity.conf
sudo nano /etc/nginx/modsecurity/modsecurity.conf
```

修改配置：

```
SecRuleEngine On
```

### 商业 WAF

考虑使用商业 WAF 解决方案，如：
- Cloudflare
- AWS WAF
- F5 Advanced WAF
- Imperva WAF

## 安全配置检查清单

- [ ] 隐藏 Nginx 版本信息
- [ ] 配置安全的 HTTP 响应头
- [ ] 限制请求方法
- [ ] 实施 IP 访问控制
- [ ] 保护敏感目录和文件
- [ ] 配置缓冲区大小限制
- [ ] 实施连接和请求速率限制
- [ ] 配置安全的 SSL/TLS
- [ ] 以非 root 用户运行 Nginx
- [ ] 定期更新 Nginx
- [ ] 配置详细的日志记录
- [ ] 使用 fail2ban 防止暴力破解
- [ ] 考虑使用 WAF

## 安全测试工具

定期使用以下工具测试 Nginx 配置的安全性：

1. **Nikto**：Web 服务器扫描器
   ```bash
   nikto -h example.com
   ```

2. **OWASP ZAP**：Web 应用安全扫描器
   ```bash
   zaproxy -quickurl https://example.com
   ```

3. **Nmap**：网络扫描器
   ```bash
   nmap -sV --script=http-enum example.com
   ```

4. **SSL Labs**：SSL/TLS 配置测试
   ```
   https://www.ssllabs.com/ssltest/analyze.html?d=example.com
   ```

5. **Mozilla Observatory**：Web 安全配置测试
   ```
   https://observatory.mozilla.org/analyze/example.com
   ```

## 安全事件响应计划

制定安全事件响应计划，包括：

1. **检测**：如何检测安全事件
2. **遏制**：如何限制损害
3. **消除**：如何移除威胁
4. **恢复**：如何恢复正常运行
5. **学习**：如何从事件中学习并改进

## 总结

本章介绍了 Nginx 的安全最佳实践，包括隐藏版本信息、配置安全头、访问控制、防止 DDoS 攻击、文件保护等。实施这些安全措施可以显著提高 Nginx 服务器的安全性。在下一章中，我们将探讨 Nginx 的性能优化。
