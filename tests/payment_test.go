package tests

import (
	"testing"
	"time"

	"github.com/yourusername/storehouse/internal/domain/entity"
)

// 测试用例4.1：订单全额支付
func TestFullPayment(t *testing.T) {
	// 准备测试数据
	orderDate := time.Now()
	orderNumber := "TEST-PAYMENT-FULL"

	// 创建已确认且已出库的订单
	order := entity.Order{
		OrderNumber:         orderNumber,
		CustomerID:          1,
		OrderDate:           orderDate,
		Status:              entity.Confirmed,
		TotalAmount:         1000,
		PaidAmount:          0,
		IsOutboundConfirmed: true,
		OutboundDate:        &orderDate,
		Remarks:             "测试全额支付",
	}

	// 保存订单
	if err := db.Create(&order).Error; err != nil {
		t.Fatalf("Failed to create order: %v", err)
	}

	// 创建支付记录
	paymentDate := time.Now()
	payment := entity.Payment{
		OrderID:       order.ID,
		Amount:        1000,
		PaymentMethod: entity.PaymentMethodCorporate,
		PaymentDate:   paymentDate,
		Remarks:       "测试全额支付",
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	// 保存支付记录
	if err := tx.Create(&payment).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create payment: %v", err)
	}

	// 更新订单已付金额
	newPaidAmount := order.PaidAmount + payment.Amount
	updates := map[string]interface{}{
		"paid_amount": newPaidAmount,
	}

	// 检查是否已全部付清，如果是，则自动将订单状态更新为"已完成"
	if newPaidAmount >= order.TotalAmount {
		updates["status"] = entity.Completed
	}

	if err := tx.Model(&order).Updates(updates).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to update order paid amount: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
	}

	// 验证支付记录
	var createdPayment entity.Payment
	if err := db.First(&createdPayment, payment.ID).Error; err != nil {
		t.Fatalf("Failed to get created payment: %v", err)
	}

	if createdPayment.Amount != 1000 {
		t.Errorf("Expected payment amount 1000, got %f", createdPayment.Amount)
	}

	if createdPayment.PaymentMethod != entity.PaymentMethodCorporate {
		t.Errorf("Expected payment method %s, got %s", entity.PaymentMethodCorporate, createdPayment.PaymentMethod)
	}

	// 验证订单状态
	var updatedOrder entity.Order
	if err := db.First(&updatedOrder, order.ID).Error; err != nil {
		t.Fatalf("Failed to get updated order: %v", err)
	}

	if updatedOrder.PaidAmount != 1000 {
		t.Errorf("Expected paid amount 1000, got %f", updatedOrder.PaidAmount)
	}

	if updatedOrder.Status != entity.Completed {
		t.Errorf("Expected order status %d (Completed), got %d", entity.Completed, updatedOrder.Status)
	}
}

// 测试用例4.2：订单部分支付
func TestPartialPayment(t *testing.T) {
	// 准备测试数据
	orderDate := time.Now()
	orderNumber := "TEST-PAYMENT-PARTIAL"

	// 创建已确认且已出库的订单
	order := entity.Order{
		OrderNumber:         orderNumber,
		CustomerID:          1,
		OrderDate:           orderDate,
		Status:              entity.Confirmed,
		TotalAmount:         1000,
		PaidAmount:          0,
		IsOutboundConfirmed: true,
		OutboundDate:        &orderDate,
		Remarks:             "测试部分支付",
	}

	// 保存订单
	if err := db.Create(&order).Error; err != nil {
		t.Fatalf("Failed to create order: %v", err)
	}

	// 创建支付记录
	paymentDate := time.Now()
	payment := entity.Payment{
		OrderID:       order.ID,
		Amount:        600,
		PaymentMethod: entity.PaymentMethodWechat,
		PaymentDate:   paymentDate,
		Remarks:       "测试部分支付",
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	// 保存支付记录
	if err := tx.Create(&payment).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create payment: %v", err)
	}

	// 更新订单已付金额
	newPaidAmount := order.PaidAmount + payment.Amount
	updates := map[string]interface{}{
		"paid_amount": newPaidAmount,
	}

	// 检查是否已全部付清，如果是，则自动将订单状态更新为"已完成"
	if newPaidAmount >= order.TotalAmount {
		updates["status"] = entity.Completed
	}

	if err := tx.Model(&order).Updates(updates).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to update order paid amount: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
	}

	// 验证支付记录
	var createdPayment entity.Payment
	if err := db.First(&createdPayment, payment.ID).Error; err != nil {
		t.Fatalf("Failed to get created payment: %v", err)
	}

	if createdPayment.Amount != 600 {
		t.Errorf("Expected payment amount 600, got %f", createdPayment.Amount)
	}

	// 验证订单状态
	var updatedOrder entity.Order
	if err := db.First(&updatedOrder, order.ID).Error; err != nil {
		t.Fatalf("Failed to get updated order: %v", err)
	}

	if updatedOrder.PaidAmount != 600 {
		t.Errorf("Expected paid amount 600, got %f", updatedOrder.PaidAmount)
	}

	// 计算欠款金额
	unpaidAmount := updatedOrder.TotalAmount - updatedOrder.PaidAmount
	if unpaidAmount != 400 {
		t.Errorf("Expected unpaid amount 400, got %f", unpaidAmount)
	}

	// 验证订单状态未变
	if updatedOrder.Status != entity.Confirmed {
		t.Errorf("Expected order status to remain %d (Confirmed), got %d", entity.Confirmed, updatedOrder.Status)
	}
}
