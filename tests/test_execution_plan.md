# 测试执行计划

## 测试环境准备

### 数据库准备
1. 准备测试数据库，与生产环境分离
2. 执行数据库初始化脚本，创建必要的表结构
3. 准备基础测试数据：
   - 创建测试用户
   - 创建测试仓库
   - 创建测试商品
   - 创建测试客户

### 测试数据准备
1. 商品数据：
   - 商品A：库存100，单价100元
   - 商品B：库存50，单价200元
   - 商品C：库存20，单价300元
   - 商品D：库存15，单价400元
   - 商品E：库存30，单价150元
   - 商品F：库存20，单价250元
   - 商品G：库存0，单价200元
   - 商品H：库存25，单价180元
   - 商品I：库存15，单价220元
   - 商品J：库存50，单价120元
   - 商品K：库存10，单价350元
   - 商品L：库存50，单价280元
   - 商品M：库存40，单价190元
   - 商品N：库存20，单价210元
   - 商品P：库存20，单价230元
   - 商品Q：库存100，单价200元
   - 商品R：库存80，单价300元
   - 商品S：库存60，单价150元
   - 商品T：库存40，单价123.45元
   - 商品U：库存0，单价50元
   - 商品V：库存50，单价60元

2. 仓库数据：
   - 仓库1：主仓库
   - 仓库2：辅助仓库

3. 客户数据：
   - 客户1：测试客户A
   - 客户2：测试客户B

## 测试执行顺序

### 第一阶段：基础功能测试
1. 订单创建测试（测试用例1.1 - 1.4）
2. 订单编辑测试（测试用例2.1 - 2.4）
3. 入库操作测试（测试用例5.1 - 5.3）
4. 出库操作测试（测试用例6.1 - 6.3）
5. 库存调拨测试（测试用例7.1 - 7.2）

### 第二阶段：订单状态和支付测试
1. 订单状态变更测试（测试用例3.1 - 3.4）
2. 订单支付测试（测试用例4.1 - 4.3）

### 第三阶段：联动测试
1. 订单与库存联动测试（测试用例8.1 - 8.4）
2. 财务计算测试（测试用例9.1 - 11.2）

### 第四阶段：报表和统计测试
1. 销售报表测试（测试用例12.1 - 12.2）
2. 收款报表测试（测试用例13.1 - 13.2）
3. 库存报表测试（测试用例14.1 - 14.2）

## 测试结果记录

| 测试用例ID | 测试用例名称 | 测试结果 | 问题描述 | 修复状态 |
|------------|------------|---------|---------|---------|
| 1.1 | 创建基本订单 | | | |
| 1.2 | 创建多商品订单 | | | |
| 1.3 | 创建库存边界订单 | | | |
| 1.4 | 尝试创建超出库存的订单 | | | |
| 2.1 | 修改订单商品数量 | | | |
| 2.2 | 修改订单商品价格 | | | |
| 2.3 | 向订单添加新商品 | | | |
| 2.4 | 从订单中移除商品 | | | |
| 3.1 | 确认订单（库存充足） | | | |
| 3.2 | 确认订单（库存不足） | | | |
| 3.3 | 取消订单 | | | |
| 3.4 | 确认订单出库 | | | |
| 4.1 | 订单全额支付 | | | |
| 4.2 | 订单部分支付 | | | |
| 4.3 | 订单多次支付 | | | |
| 5.1 | 基本入库 | | | |
| 5.2 | 追加入库 | | | |
| 5.3 | 批量入库 | | | |
| 6.1 | 基本出库 | | | |
| 6.2 | 全部出库 | | | |
| 6.3 | 尝试超量出库 | | | |
| 7.1 | 仓库间调拨 | | | |
| 7.2 | 尝试超量调拨 | | | |
| 8.1 | 订单出库对库存的影响 | | | |
| 8.2 | 取消已出库订单 | | | |
| 8.3 | 库存不足时确认订单 | | | |
| 8.4 | 库存调整后确认订单 | | | |
| 9.1 | 单商品金额计算 | | | |
| 9.2 | 多商品金额计算 | | | |
| 9.3 | 小数位金额计算 | | | |
| 10.1 | 部分支付后欠款计算 | | | |
| 10.2 | 多次支付后欠款计算 | | | |
| 10.3 | 全额支付后欠款计算 | | | |
| 11.1 | 入库后库存价值计算 | | | |
| 11.2 | 不同批次入库价值计算 | | | |
| 12.1 | 销售总额统计 | | | |
| 12.2 | 时间段筛选 | | | |
| 13.1 | 收款总额和欠款总额统计 | | | |
| 13.2 | 不同支付方式统计 | | | |
| 14.1 | 库存总量和价值统计 | | | |
| 14.2 | 库存预警 | | | |

## 测试注意事项

1. 每个测试用例执行前，确保数据库处于预期的初始状态
2. 对于修改数据的测试，执行完成后应恢复数据状态，以免影响后续测试
3. 对于发现的问题，详细记录问题描述、复现步骤和影响范围
4. 测试过程中注意检查日志，确保系统行为符合预期
5. 对于金额计算，特别注意小数位的处理和四舍五入规则
6. 对于库存变动，确保每次操作后库存数量正确无误
7. 对于订单状态变更，确保状态流转符合业务规则
