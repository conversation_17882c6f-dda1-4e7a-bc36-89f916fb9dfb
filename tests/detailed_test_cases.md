# 详细测试用例

## 一、订单管理测试用例

### 1. 订单创建测试

**测试用例1.1：创建基本订单**
```
前置条件：
- 系统中存在至少一个客户
- 系统中存在至少一个商品，库存充足（如商品A，库存100）
- 系统中存在至少一个仓库

测试步骤：
1. 创建一个新订单，选择客户
2. 添加商品A，数量为10，单价为商品默认价格
3. 提交订单

预期结果：
1. 订单创建成功，状态为"已创建"
2. 订单总金额 = 商品数量 × 单价
3. 订单欠款金额 = 订单总金额
4. 商品库存不变（此时仅创建订单，未确认出库）
```

**测试用例1.2：创建多商品订单**
```
前置条件：
- 系统中存在至少一个客户
- 系统中存在多个商品，库存充足（如商品A库存100，商品B库存50）
- 系统中存在至少一个仓库

测试步骤：
1. 创建一个新订单，选择客户
2. 添加商品A，数量为10，单价为商品默认价格
3. 添加商品B，数量为5，单价为商品默认价格
4. 提交订单

预期结果：
1. 订单创建成功，状态为"已创建"
2. 订单总金额 = (商品A数量 × 单价) + (商品B数量 × 单价)
3. 订单欠款金额 = 订单总金额
4. 所有商品库存不变
```

**测试用例1.3：创建库存边界订单**
```
前置条件：
- 系统中存在至少一个客户
- 系统中存在商品C，库存为20
- 系统中存在至少一个仓库

测试步骤：
1. 创建一个新订单，选择客户
2. 添加商品C，数量为20（刚好等于库存量）
3. 提交订单

预期结果：
1. 订单创建成功，状态为"已创建"
2. 订单总金额正确计算
3. 商品库存不变
```

**测试用例1.4：尝试创建超出库存的订单**
```
前置条件：
- 系统中存在至少一个客户
- 系统中存在商品D，库存为15
- 系统中存在至少一个仓库

测试步骤：
1. 创建一个新订单，选择客户
2. 添加商品D，数量为20（超过库存量）
3. 提交订单

预期结果：
1. 订单创建成功（因为创建订单时不检查库存）
2. 订单总金额正确计算
3. 商品库存不变
```

### 2. 订单编辑测试

**测试用例2.1：修改订单商品数量**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品A数量为10

测试步骤：
1. 编辑该订单
2. 将商品A的数量从10修改为15
3. 保存订单

预期结果：
1. 订单更新成功
2. 订单总金额重新计算 = 新数量 × 单价
3. 订单欠款金额更新
4. 商品库存不变
```

**测试用例2.2：修改订单商品价格**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品B数量为5，单价为100

测试步骤：
1. 编辑该订单
2. 将商品B的单价从100修改为120
3. 保存订单

预期结果：
1. 订单更新成功
2. 订单总金额重新计算 = 数量 × 新单价
3. 订单欠款金额更新
4. 商品库存不变
```

**测试用例2.3：向订单添加新商品**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品A
- 系统中存在商品E，未添加到该订单中

测试步骤：
1. 编辑该订单
2. 添加商品E，数量为8，单价为商品默认价格
3. 保存订单

预期结果：
1. 订单更新成功
2. 订单总金额重新计算，包含新添加的商品
3. 订单欠款金额更新
4. 商品库存不变
```

**测试用例2.4：从订单中移除商品**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品A和商品B

测试步骤：
1. 编辑该订单
2. 移除商品B
3. 保存订单

预期结果：
1. 订单更新成功
2. 订单总金额重新计算，不包含已移除的商品
3. 订单欠款金额更新
4. 商品库存不变
```

### 3. 订单状态变更测试

**测试用例3.1：确认订单（库存充足）**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品A数量为10
- 商品A库存为100

测试步骤：
1. 确认该订单（将状态从"已创建"变更为"已确认"）

预期结果：
1. 订单状态成功更新为"已确认"
2. 商品库存不变（确认订单时不减库存，只在出库确认时减库存）
```

**测试用例3.2：确认订单（库存不足）**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品F数量为30
- 商品F库存为20

测试步骤：
1. 确认该订单（将状态从"已创建"变更为"已确认"）

预期结果：
1. 订单确认失败
2. 系统提示库存不足
3. 订单状态保持为"已创建"
4. 商品库存不变
```

**测试用例3.3：取消订单**
```
前置条件：
- 系统中存在一个状态为"已确认"的订单

测试步骤：
1. 取消该订单

预期结果：
1. 订单状态成功更新为"已取消"
2. 商品库存不变
3. 该订单不再计入销售统计
```

**测试用例3.4：确认订单出库**
```
前置条件：
- 系统中存在一个状态为"已确认"的订单，包含商品A数量为10
- 商品A库存为100

测试步骤：
1. 确认该订单出库

预期结果：
1. 订单出库状态更新为"已出库"
2. 商品A库存减少10，新库存为90
3. 系统生成出库记录，记录类型为"出库"
```

### 4. 订单支付测试

**测试用例4.1：订单全额支付**
```
前置条件：
- 系统中存在一个状态为"已确认"且已出库的订单，总金额为1000元，未支付

测试步骤：
1. 为该订单添加支付记录，金额为1000元，支付方式为"对公"
2. 提交支付

预期结果：
1. 支付记录创建成功
2. 订单已付金额更新为1000元
3. 订单欠款金额更新为0元
4. 订单状态自动更新为"已完成"
```

**测试用例4.2：订单部分支付**
```
前置条件：
- 系统中存在一个状态为"已确认"且已出库的订单，总金额为1000元，未支付

测试步骤：
1. 为该订单添加支付记录，金额为600元，支付方式为"微信"
2. 提交支付

预期结果：
1. 支付记录创建成功
2. 订单已付金额更新为600元
3. 订单欠款金额更新为400元
4. 订单状态保持为"已确认"
```

**测试用例4.3：订单多次支付**
```
前置条件：
- 系统中存在一个状态为"已确认"且已出库的订单，总金额为1000元，已支付600元

测试步骤：
1. 为该订单添加第二笔支付记录，金额为400元，支付方式为"支付宝"
2. 提交支付

预期结果：
1. 支付记录创建成功
2. 订单已付金额更新为1000元
3. 订单欠款金额更新为0元
4. 订单状态自动更新为"已完成"
```

## 二、库存管理测试用例

### 1. 入库操作测试

**测试用例5.1：基本入库**
```
前置条件：
- 系统中存在商品G，初始库存为0
- 系统中存在仓库1

测试步骤：
1. 创建入库记录，商品为G，仓库为1，数量为50，单价为200
2. 提交入库

预期结果：
1. 入库记录创建成功
2. 商品G在仓库1的库存更新为50
3. 系统生成库存移动记录，类型为"入库"
```

**测试用例5.2：追加入库**
```
前置条件：
- 系统中存在商品A，在仓库1的库存为90
- 系统中存在仓库1

测试步骤：
1. 创建入库记录，商品为A，仓库为1，数量为30，单价为150
2. 提交入库

预期结果：
1. 入库记录创建成功
2. 商品A在仓库1的库存更新为120
3. 系统生成库存移动记录，类型为"入库"
```

**测试用例5.3：批量入库**
```
前置条件：
- 系统中存在多个商品
- 系统中存在仓库1

测试步骤：
1. 创建多个入库记录，包含不同商品
2. 提交入库

预期结果：
1. 所有入库记录创建成功
2. 所有相关商品的库存正确更新
3. 系统为每个商品生成库存移动记录
```
