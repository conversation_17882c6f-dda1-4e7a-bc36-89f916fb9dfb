# 详细测试用例（续）

### 2. 出库操作测试

**测试用例6.1：基本出库**
```
前置条件：
- 系统中存在商品A，在仓库1的库存为120
- 系统中存在仓库1

测试步骤：
1. 创建出库记录，商品为A，仓库为1，数量为20
2. 提交出库

预期结果：
1. 出库记录创建成功
2. 商品A在仓库1的库存更新为100
3. 系统生成库存移动记录，类型为"出库"
```

**测试用例6.2：全部出库**
```
前置条件：
- 系统中存在商品H，在仓库1的库存为25
- 系统中存在仓库1

测试步骤：
1. 创建出库记录，商品为H，仓库为1，数量为25（全部库存）
2. 提交出库

预期结果：
1. 出库记录创建成功
2. 商品H在仓库1的库存更新为0
3. 系统生成库存移动记录，类型为"出库"
```

**测试用例6.3：尝试超量出库**
```
前置条件：
- 系统中存在商品I，在仓库1的库存为15
- 系统中存在仓库1

测试步骤：
1. 创建出库记录，商品为I，仓库为1，数量为20（超过库存）
2. 提交出库

预期结果：
1. 出库操作失败
2. 系统提示库存不足
3. 商品I的库存保持不变
4. 不生成库存移动记录
```

### 3. 库存调拨测试

**测试用例7.1：仓库间调拨**
```
前置条件：
- 系统中存在商品J，在仓库1的库存为50
- 系统中存在仓库1和仓库2

测试步骤：
1. 创建调拨记录，商品为J，源仓库为1，目标仓库为2，数量为20
2. 提交调拨

预期结果：
1. 调拨记录创建成功
2. 商品J在仓库1的库存减少为30
3. 商品J在仓库2的库存增加为20（如果之前为0）或增加20
4. 系统生成库存移动记录，类型为"调拨"
```

**测试用例7.2：尝试超量调拨**
```
前置条件：
- 系统中存在商品K，在仓库1的库存为10
- 系统中存在仓库1和仓库2

测试步骤：
1. 创建调拨记录，商品为K，源仓库为1，目标仓库为2，数量为15（超过库存）
2. 提交调拨

预期结果：
1. 调拨操作失败
2. 系统提示库存不足
3. 商品K在两个仓库的库存保持不变
4. 不生成库存移动记录
```

## 三、订单与库存联动测试用例

**测试用例8.1：订单出库对库存的影响**
```
前置条件：
- 系统中存在一个状态为"已确认"的订单，包含商品L数量为15
- 商品L在仓库1的库存为50

测试步骤：
1. 确认该订单出库

预期结果：
1. 订单出库状态更新为"已出库"
2. 商品L库存减少15，新库存为35
3. 系统生成出库记录，记录类型为"出库"
```

**测试用例8.2：取消已出库订单**
```
前置条件：
- 系统中存在一个状态为"已确认"且已出库的订单，包含商品M数量为10
- 商品M当前库存为40（已减去订单数量）

测试步骤：
1. 取消该订单

预期结果：
1. 订单状态更新为"已取消"
2. 商品M库存恢复，新库存为50
3. 系统生成入库记录，记录类型为"取消订单退回"
```

**测试用例8.3：库存不足时确认订单**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品N数量为30
- 商品N库存为20

测试步骤：
1. 尝试确认该订单

预期结果：
1. 订单确认失败
2. 系统提示库存不足
3. 订单状态保持为"已创建"
```

**测试用例8.4：库存调整后确认订单**
```
前置条件：
- 系统中存在一个状态为"已创建"的订单，包含商品P数量为25
- 商品P初始库存为20（不足以确认订单）

测试步骤：
1. 为商品P创建入库记录，数量为10
2. 提交入库，使商品P库存增加到30
3. 尝试确认该订单

预期结果：
1. 入库成功，商品P库存更新为30
2. 订单确认成功
3. 订单状态更新为"已确认"
```
