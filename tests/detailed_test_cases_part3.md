# 详细测试用例（续2）

## 四、财务计算测试用例

### 1. 订单金额计算

**测试用例9.1：单商品金额计算**
```
前置条件：
- 系统中存在商品Q，单价为200元

测试步骤：
1. 创建一个新订单
2. 添加商品Q，数量为5
3. 提交订单

预期结果：
1. 订单创建成功
2. 商品Q的金额计算为 5 × 200 = 1000元
3. 订单总金额为1000元
```

**测试用例9.2：多商品金额计算**
```
前置条件：
- 系统中存在商品R，单价为300元
- 系统中存在商品S，单价为150元

测试步骤：
1. 创建一个新订单
2. 添加商品R，数量为2
3. 添加商品S，数量为4
4. 提交订单

预期结果：
1. 订单创建成功
2. 商品R的金额计算为 2 × 300 = 600元
3. 商品S的金额计算为 4 × 150 = 600元
4. 订单总金额为 600 + 600 = 1200元
```

**测试用例9.3：小数位金额计算**
```
前置条件：
- 系统中存在商品T，单价为123.45元

测试步骤：
1. 创建一个新订单
2. 添加商品T，数量为3.5
3. 提交订单

预期结果：
1. 订单创建成功
2. 商品T的金额计算为 3.5 × 123.45 = 432.08元（四舍五入到小数点后2位）
3. 订单总金额为432.08元
```

### 2. 支付金额计算

**测试用例10.1：部分支付后欠款计算**
```
前置条件：
- 系统中存在一个订单，总金额为2000元，未支付

测试步骤：
1. 为该订单添加支付记录，金额为1200元
2. 提交支付

预期结果：
1. 支付记录创建成功
2. 订单已付金额更新为1200元
3. 订单欠款金额计算为 2000 - 1200 = 800元
```

**测试用例10.2：多次支付后欠款计算**
```
前置条件：
- 系统中存在一个订单，总金额为3000元，已支付1000元

测试步骤：
1. 为该订单添加第二笔支付记录，金额为1500元
2. 提交支付

预期结果：
1. 支付记录创建成功
2. 订单已付金额更新为 1000 + 1500 = 2500元
3. 订单欠款金额计算为 3000 - 2500 = 500元
```

**测试用例10.3：全额支付后欠款计算**
```
前置条件：
- 系统中存在一个订单，总金额为1500元，已支付1000元

测试步骤：
1. 为该订单添加支付记录，金额为500元
2. 提交支付

预期结果：
1. 支付记录创建成功
2. 订单已付金额更新为 1000 + 500 = 1500元
3. 订单欠款金额计算为 1500 - 1500 = 0元
4. 订单状态自动更新为"已完成"
```

### 3. 库存价值计算

**测试用例11.1：入库后库存价值计算**
```
前置条件：
- 系统中存在商品U，初始库存为0

测试步骤：
1. 创建入库记录，商品为U，数量为100，单价为50元
2. 提交入库

预期结果：
1. 入库成功
2. 商品U库存更新为100
3. 商品U库存价值计算为 100 × 50 = 5000元
```

**测试用例11.2：不同批次入库价值计算**
```
前置条件：
- 系统中存在商品V，初始库存为50，入库单价为60元

测试步骤：
1. 创建第二批入库记录，商品为V，数量为30，单价为70元
2. 提交入库

预期结果：
1. 入库成功
2. 商品V库存更新为 50 + 30 = 80
3. 商品V库存价值计算为 (50 × 60) + (30 × 70) = 3000 + 2100 = 5100元
```

## 五、报表和统计测试用例

### 1. 销售报表测试

**测试用例12.1：销售总额统计**
```
前置条件：
- 系统中存在多个已完成的订单，总金额分别为1000元、2000元和3000元
- 系统中存在一个已取消的订单，金额为1500元

测试步骤：
1. 生成销售报表，不设置时间筛选

预期结果：
1. 报表生成成功
2. 销售总额计算为 1000 + 2000 + 3000 = 6000元（不包含已取消订单）
```

**测试用例12.2：时间段筛选**
```
前置条件：
- 系统中存在多个已完成的订单，创建时间分布在不同日期

测试步骤：
1. 生成销售报表，设置时间筛选为最近7天

预期结果：
1. 报表生成成功
2. 销售总额只计算最近7天内的订单金额
3. 不包含已取消订单
```

### 2. 收款报表测试

**测试用例13.1：收款总额和欠款总额统计**
```
前置条件：
- 系统中存在多个订单，总金额合计为10000元，已收款合计为7000元

测试步骤：
1. 生成收款报表

预期结果：
1. 报表生成成功
2. 收款总额显示为7000元
3. 欠款总额显示为 10000 - 7000 = 3000元
4. 收款率计算为 (7000 / 10000) × 100% = 70%
```

**测试用例13.2：不同支付方式统计**
```
前置条件：
- 系统中存在多笔支付记录，使用不同支付方式

测试步骤：
1. 生成收款报表，按支付方式分组

预期结果：
1. 报表生成成功
2. 各支付方式（对公、对私、微信、支付宝）的收款金额正确统计
```

### 3. 库存报表测试

**测试用例14.1：库存总量和价值统计**
```
前置条件：
- 系统中存在多种商品，各有不同库存量和价值

测试步骤：
1. 生成库存报表

预期结果：
1. 报表生成成功
2. 各商品库存量正确显示
3. 各商品库存价值正确计算
4. 库存总价值为所有商品库存价值之和
```

**测试用例14.2：库存预警**
```
前置条件：
- 系统中存在多种商品，部分商品库存低于预警值

测试步骤：
1. 生成库存预警报表

预期结果：
1. 报表生成成功
2. 只显示库存低于预警值的商品
3. 显示当前库存量和预警值
```
