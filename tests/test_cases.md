# 出入库和订单操作测试用例文档

## 一、订单管理测试场景

### 1. 订单创建场景
- **基本订单创建**：创建包含单个商品的基本订单
- **多商品订单创建**：创建包含多个不同商品的订单
- **大量商品订单创建**：创建包含大量商品的订单，测试系统处理能力
- **边界情况**：创建包含库存刚好足够的商品订单
- **异常情况**：尝试创建包含库存不足商品的订单

### 2. 订单编辑场景
- **修改商品数量**：增加/减少订单中商品的数量
- **修改商品价格**：调整订单中商品的单价
- **添加新商品**：向现有订单添加新的商品
- **移除商品**：从订单中移除商品
- **边界情况**：编辑订单至库存刚好足够的状态
- **异常情况**：尝试编辑订单至超出库存的状态

### 3. 订单状态变更场景
- **订单确认**：将订单从"已创建"变更为"已确认"状态
- **订单取消**：取消处于不同状态的订单
- **出库确认**：确认订单出库
- **订单完成**：订单全额支付后自动完成

### 4. 订单支付场景
- **全额支付**：一次性支付订单全部金额
- **部分支付**：分多次支付订单金额
- **多种支付方式**：使用不同支付方式（对公、对私、微信、支付宝）
- **超额支付**：支付金额超过订单总额
- **订单状态自动变更**：验证全额支付后订单状态自动变为"已完成"

## 二、库存管理测试场景

### 1. 入库操作场景
- **基本入库**：向仓库添加新商品
- **追加入库**：向已有库存的商品添加数量
- **批量入库**：同时入库多种商品
- **零数量入库**：测试系统对零数量入库的处理

### 2. 出库操作场景
- **基本出库**：从仓库减少商品数量
- **全部出库**：将商品库存全部出库
- **批量出库**：同时出库多种商品
- **边界情况**：出库数量刚好等于库存数量
- **异常情况**：尝试出库超过库存数量的商品

### 3. 库存调拨场景
- **仓库间调拨**：将商品从一个仓库调拨到另一个仓库
- **部分调拨**：只调拨部分库存
- **全部调拨**：调拨全部库存
- **异常情况**：尝试调拨超过源仓库库存的数量

## 三、订单与库存联动测试场景

### 1. 订单确认对库存的影响
- **订单确认时库存检查**：验证订单确认时系统是否正确检查库存
- **多订单并发**：多个订单同时确认时对同一商品库存的影响

### 2. 订单出库对库存的影响
- **出库确认减库存**：验证出库确认时库存是否正确减少
- **取消订单库存恢复**：验证取消已出库订单时库存是否恢复

### 3. 库存变动对订单的影响
- **库存不足对订单确认的影响**：验证库存不足时无法确认订单
- **库存调整后对待确认订单的影响**：验证库存调整后能否确认之前因库存不足而无法确认的订单

## 四、财务计算测试场景

### 1. 订单金额计算
- **单商品金额计算**：验证单个商品的金额计算（数量×单价）
- **多商品金额计算**：验证多个商品的总金额计算
- **小数位处理**：验证含小数的金额计算精度

### 2. 支付金额计算
- **部分支付后欠款计算**：验证部分支付后的欠款金额计算
- **多次支付后欠款计算**：验证多次支付后的欠款金额计算
- **全额支付后欠款计算**：验证全额支付后欠款金额为零

### 3. 库存价值计算
- **入库后库存价值计算**：验证入库后库存总价值的计算
- **出库后库存价值计算**：验证出库后库存总价值的计算
- **不同批次价格的处理**：验证不同批次入库的商品价格对库存价值的影响

## 五、报表和统计测试场景

### 1. 销售报表测试
- **销售总额统计**：验证销售报表中的销售总额计算
- **商品销售量统计**：验证商品销售量的统计
- **时间段筛选**：验证不同时间段的销售数据统计
- **取消订单不计入**：验证已取消订单不计入销售统计

### 2. 收款报表测试
- **收款总额统计**：验证收款报表中的收款总额计算
- **欠款总额统计**：验证欠款总额的计算
- **收款率计算**：验证收款率的计算（已收款/应收款）
- **不同支付方式统计**：验证不同支付方式的收款统计

### 3. 库存报表测试
- **库存总量统计**：验证库存报表中的库存总量计算
- **库存价值统计**：验证库存总价值的计算
- **库存预警**：验证库存低于预警值的商品统计
