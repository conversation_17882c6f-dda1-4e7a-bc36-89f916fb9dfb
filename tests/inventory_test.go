package tests

import (
	"testing"
	"time"

	"github.com/yourusername/storehouse/internal/domain/entity"
)

// 测试用例5.1：基本入库
func TestBasicStockIn(t *testing.T) {
	// 准备测试数据 - 创建一个新商品
	product := entity.Product{
		Code:  "G001",
		Name:  "商品G",
		Unit:  "个",
		Price: 200,
	}

	if err := db.Create(&product).Error; err != nil {
		t.Fatalf("Failed to create product: %v", err)
	}

	// 检查是否已有库存记录
	var existingInventory entity.Inventory
	result := db.Where("product_id = ? AND warehouse_id = ?", product.ID, 1).First(&existingInventory)
	
	// 如果已有库存记录，删除它
	if result.Error == nil {
		if err := db.Delete(&existingInventory).Error; err != nil {
			t.Fatalf("Failed to delete existing inventory: %v", err)
		}
	}

	// 创建入库记录
	movementDate := time.Now()
	movement := entity.StockMovement{
		ProductID:         product.ID,
		SourceWarehouseID: 1,
		Quantity:          50,
		UnitPrice:         200,
		MovementType:      entity.StockIn,
		Reason:            "测试入库",
		MovementDate:      movementDate,
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	// 保存入库记录
	if err := tx.Create(&movement).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create stock movement: %v", err)
	}

	// 查询是否存在库存记录
	var inventory entity.Inventory
	result = tx.Where("product_id = ? AND warehouse_id = ?", product.ID, 1).First(&inventory)
	
	// 如果不存在，创建新记录
	if result.Error != nil {
		inventory = entity.Inventory{
			ProductID:   product.ID,
			WarehouseID: 1,
			Quantity:    movement.Quantity,
		}
		
		if err := tx.Create(&inventory).Error; err != nil {
			tx.Rollback()
			t.Fatalf("Failed to create inventory: %v", err)
		}
	} else {
		// 更新现有库存
		if err := tx.Model(&inventory).Update("quantity", inventory.Quantity+movement.Quantity).Error; err != nil {
			tx.Rollback()
			t.Fatalf("Failed to update inventory: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
	}

	// 验证库存记录
	var updatedInventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", product.ID, 1).First(&updatedInventory).Error; err != nil {
		t.Fatalf("Failed to get updated inventory: %v", err)
	}

	if updatedInventory.Quantity != 50 {
		t.Errorf("Expected inventory quantity 50, got %f", updatedInventory.Quantity)
	}

	// 验证入库记录
	var createdMovement entity.StockMovement
	if err := db.First(&createdMovement, movement.ID).Error; err != nil {
		t.Fatalf("Failed to get created stock movement: %v", err)
	}

	if createdMovement.MovementType != entity.StockIn {
		t.Errorf("Expected movement type %d, got %d", entity.StockIn, createdMovement.MovementType)
	}
}

// 测试用例6.1：基本出库
func TestBasicStockOut(t *testing.T) {
	// 准备测试数据 - 确保商品A有足够库存
	var inventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", 1, 1).First(&inventory).Error; err != nil {
		t.Fatalf("Failed to get inventory: %v", err)
	}

	initialQuantity := inventory.Quantity

	// 创建出库记录
	movementDate := time.Now()
	movement := entity.StockMovement{
		ProductID:         1,
		SourceWarehouseID: 1,
		Quantity:          20,
		MovementType:      entity.StockOut,
		Reason:            "测试出库",
		MovementDate:      movementDate,
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	// 保存出库记录
	if err := tx.Create(&movement).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create stock movement: %v", err)
	}

	// 检查库存是否足够
	if inventory.Quantity < movement.Quantity {
		tx.Rollback()
		t.Fatalf("Insufficient inventory: required %f, available %f", movement.Quantity, inventory.Quantity)
	}

	// 更新库存
	newQuantity := inventory.Quantity - movement.Quantity
	if err := tx.Model(&inventory).Update("quantity", newQuantity).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to update inventory: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
	}

	// 验证库存记录
	var updatedInventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", 1, 1).First(&updatedInventory).Error; err != nil {
		t.Fatalf("Failed to get updated inventory: %v", err)
	}

	expectedQuantity := initialQuantity - 20
	if updatedInventory.Quantity != expectedQuantity {
		t.Errorf("Expected inventory quantity %f, got %f", expectedQuantity, updatedInventory.Quantity)
	}

	// 验证出库记录
	var createdMovement entity.StockMovement
	if err := db.First(&createdMovement, movement.ID).Error; err != nil {
		t.Fatalf("Failed to get created stock movement: %v", err)
	}

	if createdMovement.MovementType != entity.StockOut {
		t.Errorf("Expected movement type %d, got %d", entity.StockOut, createdMovement.MovementType)
	}
}

// 测试用例7.1：仓库间调拨
func TestWarehouseTransfer(t *testing.T) {
	// 准备测试数据 - 创建一个新商品
	product := entity.Product{
		Code:  "J001",
		Name:  "商品J",
		Unit:  "个",
		Price: 120,
	}

	if err := db.Create(&product).Error; err != nil {
		t.Fatalf("Failed to create product: %v", err)
	}

	// 为商品在仓库1创建库存
	sourceInventory := entity.Inventory{
		ProductID:   product.ID,
		WarehouseID: 1,
		Quantity:    50,
	}

	if err := db.Create(&sourceInventory).Error; err != nil {
		t.Fatalf("Failed to create source inventory: %v", err)
	}

	// 检查是否已有目标仓库库存记录
	var destInventory entity.Inventory
	result := db.Where("product_id = ? AND warehouse_id = ?", product.ID, 2).First(&destInventory)
	
	// 如果已有库存记录，删除它
	if result.Error == nil {
		if err := db.Delete(&destInventory).Error; err != nil {
			t.Fatalf("Failed to delete existing destination inventory: %v", err)
		}
	}

	// 创建调拨记录
	movementDate := time.Now()
	destWarehouseID := uint(2)
	movement := entity.StockMovement{
		ProductID:              product.ID,
		SourceWarehouseID:      1,
		DestinationWarehouseID: &destWarehouseID,
		Quantity:               20,
		MovementType:           entity.Transfer,
		Reason:                 "测试调拨",
		MovementDate:           movementDate,
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	// 保存调拨记录
	if err := tx.Create(&movement).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to create stock movement: %v", err)
	}

	// 更新源仓库库存
	if err := tx.Model(&sourceInventory).Update("quantity", sourceInventory.Quantity-movement.Quantity).Error; err != nil {
		tx.Rollback()
		t.Fatalf("Failed to update source inventory: %v", err)
	}

	// 查询目标仓库是否存在库存记录
	result = tx.Where("product_id = ? AND warehouse_id = ?", product.ID, *movement.DestinationWarehouseID).First(&destInventory)
	
	// 如果不存在，创建新记录
	if result.Error != nil {
		destInventory = entity.Inventory{
			ProductID:   product.ID,
			WarehouseID: *movement.DestinationWarehouseID,
			Quantity:    movement.Quantity,
		}
		
		if err := tx.Create(&destInventory).Error; err != nil {
			tx.Rollback()
			t.Fatalf("Failed to create destination inventory: %v", err)
		}
	} else {
		// 更新现有库存
		if err := tx.Model(&destInventory).Update("quantity", destInventory.Quantity+movement.Quantity).Error; err != nil {
			tx.Rollback()
			t.Fatalf("Failed to update destination inventory: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
	}

	// 验证源仓库库存
	var updatedSourceInventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", product.ID, 1).First(&updatedSourceInventory).Error; err != nil {
		t.Fatalf("Failed to get updated source inventory: %v", err)
	}

	if updatedSourceInventory.Quantity != 30 {
		t.Errorf("Expected source inventory quantity 30, got %f", updatedSourceInventory.Quantity)
	}

	// 验证目标仓库库存
	var updatedDestInventory entity.Inventory
	if err := db.Where("product_id = ? AND warehouse_id = ?", product.ID, 2).First(&updatedDestInventory).Error; err != nil {
		t.Fatalf("Failed to get updated destination inventory: %v", err)
	}

	if updatedDestInventory.Quantity != 20 {
		t.Errorf("Expected destination inventory quantity 20, got %f", updatedDestInventory.Quantity)
	}
}
