{"name": "storehouse-management-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.6.2", "crypto-js": "^4.2.0", "echarts": "^5.4.3", "element-plus": "^2.4.3", "pinia": "^2.1.7", "vue": "^3.3.9", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.1", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "sass": "^1.69.5", "vite": "^5.0.4"}}