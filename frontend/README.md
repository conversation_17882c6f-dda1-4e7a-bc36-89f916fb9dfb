# 仓库管理系统前端

基于Vue.js的仓库管理系统前端项目。

## 技术栈

- Vue.js 3
- Vue Router
- Pinia (状态管理)
- Axios (HTTP请求)
- Element Plus (UI组件库)
- ECharts (图表库)

## 项目结构

```
frontend/
├── public/              # 静态资源
├── src/
│   ├── assets/          # 静态资源
│   ├── components/      # 通用组件
│   │   ├── product/     # 产品相关组件
│   │   ├── inventory/   # 库存相关组件
│   │   ├── movement/    # 出入库相关组件
│   │   ├── customer/    # 客户相关组件
│   │   ├── order/       # 订单相关组件
│   │   └── common/      # 通用UI组件
│   ├── views/           # 页面视图
│   ├── router/          # 路由配置
│   ├── store/           # Pinia状态管理
│   ├── api/             # API调用封装
│   ├── utils/           # 工具函数
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── .env                 # 环境变量
├── package.json         # 项目依赖
└── vite.config.js       # Vite配置
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

### 生产环境构建

```bash
npm run build
```
