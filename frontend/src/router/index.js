import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../store/user'

// 页面组件
const Dashboard = () => import('../views/Dashboard.vue')
const Login = () => import('../views/Login.vue')

// 产品相关
const ProductList = () => import('../views/product/ProductList.vue')
const ProductDetail = () => import('../views/product/ProductDetail.vue')
const ProductCreate = () => import('../views/product/ProductCreate.vue')
const ProductEdit = () => import('../views/product/ProductEdit.vue')

// 库存相关
const InventoryList = () => import('../views/inventory/InventoryList.vue')
const InventoryDetail = () => import('../views/inventory/InventoryDetail.vue')

// 出入库相关
const StockMovementList = () => import('../views/stock/StockMovementList.vue')
const StockMovementDetail = () => import('../views/stock/StockMovementDetail.vue')

// 客户相关
const CustomerList = () => import('../views/customer/CustomerList.vue')
const CustomerDetail = () => import('../views/customer/CustomerDetail.vue')
const CustomerCreate = () => import('../views/customer/CustomerCreate.vue')
const CustomerEdit = () => import('../views/customer/CustomerEdit.vue')

// 订单相关
const OrderList = () => import('../views/order/OrderList.vue')
const OrderDetail = () => import('../views/order/OrderDetail.vue')
const OrderCreate = () => import('../views/order/OrderCreate.vue')
const PrintOutbound = () => import('../views/order/PrintOutbound.vue')

// 报表相关
const ReportPage = () => import('../views/report/ReportPage.vue')

// 其他页面
const Settings = () => import('../views/Settings.vue')
const NotFound = () => import('../views/NotFound.vue')
const CryptoTest = () => import('../views/CryptoTest.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/products',
    name: 'ProductList',
    component: ProductList,
    meta: { requiresAuth: true }
  },
  {
    path: '/products/create',
    name: 'ProductCreate',
    component: ProductCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/products/:id',
    name: 'ProductDetail',
    component: ProductDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/products/:id/edit',
    name: 'ProductEdit',
    component: ProductEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory',
    name: 'InventoryList',
    component: InventoryList,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/:id',
    name: 'InventoryDetail',
    component: InventoryDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/stock-movements',
    name: 'StockMovementList',
    component: StockMovementList,
    meta: { requiresAuth: true }
  },
  {
    path: '/stock-movements/:id',
    name: 'StockMovementDetail',
    component: StockMovementDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/customers',
    name: 'CustomerList',
    component: CustomerList,
    meta: { requiresAuth: true }
  },
  {
    path: '/customers/create',
    name: 'CustomerCreate',
    component: CustomerCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/customers/:id',
    name: 'CustomerDetail',
    component: CustomerDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/customers/:id/edit',
    name: 'CustomerEdit',
    component: CustomerEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/orders',
    name: 'OrderList',
    component: OrderList,
    meta: { requiresAuth: true }
  },
  {
    path: '/orders/create',
    name: 'OrderCreate',
    component: OrderCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/orders/:id',
    name: 'OrderDetail',
    component: OrderDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/orders/:id/print-outbound',
    name: 'PrintOutbound',
    component: PrintOutbound,
    meta: { requiresAuth: true }
  },
  {
    path: '/reports',
    name: 'ReportPage',
    component: ReportPage,
    meta: { requiresAuth: true }
  },
  {
    path: '/reports/inventory',
    name: 'InventoryReport',
    component: ReportPage,
    meta: { requiresAuth: true, defaultTab: 'inventory' }
  },
  {
    path: '/reports/customers',
    name: 'CustomerReport',
    component: ReportPage,
    meta: { requiresAuth: true, defaultTab: 'customer' }
  },
  {
    path: '/reports/payment',
    name: 'PaymentReport',
    component: ReportPage,
    meta: { requiresAuth: true, defaultTab: 'payment' }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: { requiresAuth: true }
  },
  {
    path: '/crypto-test',
    name: 'CryptoTest',
    component: CryptoTest,
    meta: { requiresAuth: false }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

export default router
