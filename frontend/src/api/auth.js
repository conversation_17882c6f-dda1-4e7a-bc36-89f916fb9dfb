import request from './request'
import { encryptPassword } from '../utils/crypto'

// 用户登录
export function login(username, password) {
  // 对密码进行加密
  const encryptedPassword = encryptPassword(password)

  return request({
    url: '/auth/login',
    method: 'post',
    data: {
      username,
      // 发送加密后的密码
      password: encryptedPassword,
      // 标记密码已加密
      passwordEncrypted: true
    },
    // 确保返回完整的响应
    transformResponse: [function (data) {
      try {
        // 尝试解析JSON
        return JSON.parse(data)
      } catch (e) {
        // 如果解析失败，返回原始数据
        console.error('Failed to parse response:', e)
        return data
      }
    }]
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/auth/user',
    method: 'get'
  })
}

// 修改密码
export function changePassword(oldPassword, newPassword) {
  // 对新旧密码进行加密
  const encryptedOldPassword = encryptPassword(oldPassword)
  const encryptedNewPassword = encryptPassword(newPassword)

  return request({
    url: '/auth/change-password',
    method: 'post',
    data: {
      oldPassword: encryptedOldPassword,
      newPassword: encryptedNewPassword,
      passwordEncrypted: true
    }
  })
}
