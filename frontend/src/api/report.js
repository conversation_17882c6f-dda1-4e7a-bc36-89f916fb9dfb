import request from './request'

// 获取销售报表
export function getSalesReport(params) {
  return request({
    url: '/reports/sales',
    method: 'get',
    params
  })
}

// 导出销售报表
export function exportSalesReport(params) {
  return request({
    url: '/reports/sales/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取库存报表
export function getInventoryReport(params) {
  return request({
    url: '/reports/inventory',
    method: 'get',
    params
  })
}

// 导出库存报表
export function exportInventoryReport(params) {
  return request({
    url: '/reports/inventory/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取客户报表
export function getCustomerReport(params) {
  return request({
    url: '/reports/customers',
    method: 'get',
    params
  })
}

// 导出客户报表
export function exportCustomerReport(params) {
  return request({
    url: '/reports/customers/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取销售人员业绩报表
export function getSalesPersonReport(params) {
  return request({
    url: '/reports/salespersons',
    method: 'get',
    params
  })
}

// 导出销售人员业绩报表
export function exportSalesPersonReport(params) {
  return request({
    url: '/reports/salespersons/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
