import request from './request'

// 获取库存列表
export function getInventories(params) {
  return request({
    url: '/inventories',
    method: 'get',
    params
  })
}

// 获取库存详情
export function getInventoryById(id) {
  return request({
    url: `/inventories/${id}`,
    method: 'get'
  })
}

// 获取产品库存
export function getProductInventory(productId) {
  console.log('调用获取产品库存API, 产品ID:', productId)
  return request({
    url: `/products/${productId}/inventory`,
    method: 'get'
  })
}

// 获取仓库库存
export function getWarehouseInventory(warehouseId) {
  return request({
    url: `/warehouses/${warehouseId}/inventory`,
    method: 'get',
  })
}

// 调整库存
export function adjustInventory(data) {
  return request({
    url: '/stock-movements',
    method: 'post',
    data: {
      ...data,
      movementType: 4 // 调整类型
    }
  })
}
