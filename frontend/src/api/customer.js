import request from './request'

// 获取客户列表
export function getCustomers(params) {
  return request({
    url: '/customers',
    method: 'get',
    params
  })
}

// 获取客户详情
export function getCustomerById(id) {
  return request({
    url: `/customers/${id}`,
    method: 'get'
  })
}

// 创建客户
export function createCustomer(data) {
  return request({
    url: '/customers',
    method: 'post',
    data
  })
}

// 更新客户
export function updateCustomer(id, data) {
  return request({
    url: `/customers/${id}`,
    method: 'put',
    data
  })
}

// 删除客户
export function deleteCustomer(id) {
  return request({
    url: `/customers/${id}`,
    method: 'delete'
  })
}

// 获取客户订单
export function getCustomerOrders(id, params) {
  return request({
    url: `/customers/${id}/orders`,
    method: 'get',
    params
  })
}

// 获取客户统计信息
export function getCustomerStats(id) {
  return request({
    url: `/customers/${id}/stats`,
    method: 'get'
  })
}
