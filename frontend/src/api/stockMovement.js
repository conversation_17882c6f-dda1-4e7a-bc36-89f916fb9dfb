import request from './request'

// 获取出入库记录列表
export function getStockMovements(params) {
  return request({
    url: '/stock-movements',
    method: 'get',
    params
  })
}

// 导出出入库记录
export function exportStockMovements(params) {
  return request({
    url: '/stock-movements/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取出入库记录详情
export function getStockMovementById(id) {
  return request({
    url: `/stock-movements/${id}`,
    method: 'get'
  })
}

// 创建入库记录
export function createStockIn(data) {
  return request({
    url: '/stock-movements',
    method: 'post',
    data: {
      ...data,
      movementType: 1 // 1表示入库
    }
  })
}

// 创建出库记录
export function createStockOut(data) {
  console.log('调用出库API，参数:', {
    ...data,
    movementType: 2 // 2表示出库
  })
  return request({
    url: '/stock-movements',
    method: 'post',
    data: {
      ...data,
      movementType: 2 // 2表示出库
    }
  })
}

// 创建调拨记录
export function createStockTransfer(data) {
  return request({
    url: '/stock-movements',
    method: 'post',
    data: {
      ...data,
      movementType: 3 // 3表示调拨
    }
  })
}

// 创建库存调整记录
export function createStockAdjustment(data) {
  return request({
    url: '/stock-movements',
    method: 'post',
    data: {
      ...data,
      movementType: 4 // 4表示调整
    }
  })
}

// 获取产品的出入库记录
export function getProductStockMovements(productId, params) {
  return request({
    url: '/stock-movements',
    method: 'get',
    params: {
      ...params,
      productId
    }
  })
}

// 获取仓库的出入库记录
export function getWarehouseStockMovements(warehouseId, params) {
  return request({
    url: '/stock-movements',
    method: 'get',
    params: {
      ...params,
      warehouseId
    }
  })
}

// 获取产品的出入库记录（别名，用于ProductDetail.vue）
export function getProductMovements(productId, params = {}) {
  return getProductStockMovements(productId, params)
}

// 获取库存的出入库记录（别名，用于InventoryDetail.vue）
export function getInventoryMovements(params = {}) {
  return getStockMovements(params)
}

// 获取出入库记录详情（别名，用于StockMovementDetail.vue）
export function getMovementById(id) {
  return getStockMovementById(id)
}
