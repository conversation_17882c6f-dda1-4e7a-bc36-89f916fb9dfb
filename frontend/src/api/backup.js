import request from './request'

/**
 * 获取备份列表
 * @returns {Promise} 返回备份列表数据
 */
export function getBackups() {
  return request({
    url: '/backups',
    method: 'get'
  })
}

/**
 * 创建备份
 * @returns {Promise} 返回创建结果
 */
export function createBackup() {
  return request({
    url: '/backups',
    method: 'post'
  })
}

/**
 * 下载备份
 * @param {string|number} id 备份ID
 * @returns {Promise} 返回下载结果
 */
export function downloadBackup(id) {
  return request({
    url: `/backups/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 删除备份
 * @param {string|number} id 备份ID
 * @returns {Promise} 返回删除结果
 */
export function deleteBackup(id) {
  return request({
    url: `/backups/${id}`,
    method: 'delete'
  })
}

/**
 * 恢复备份
 * @param {string|number} id 备份ID
 * @returns {Promise} 返回恢复结果
 */
export function restoreBackup(id) {
  return request({
    url: `/backups/${id}/restore`,
    method: 'post'
  })
}
