import request from './request'

// 获取仓库列表
export function getWarehouses(params) {
  return request({
    url: '/warehouses',
    method: 'get',
    params
  })
}

// 获取仓库详情
export function getWarehouseById(id) {
  return request({
    url: `/warehouses/${id}`,
    method: 'get'
  })
}

// 创建仓库
export function createWarehouse(data) {
  return request({
    url: '/warehouses',
    method: 'post',
    data
  })
}

// 更新仓库
export function updateWarehouse(id, data) {
  return request({
    url: `/warehouses/${id}`,
    method: 'put',
    data
  })
}

// 删除仓库
export function deleteWarehouse(id) {
  return request({
    url: `/warehouses/${id}`,
    method: 'delete'
  })
}

// 获取仓库库存
export function getWarehouseInventory(id, params) {
  return request({
    url: `/warehouses/${id}/inventory`,
    method: 'get',
    params
  })
}

// 获取仓库统计信息
export function getWarehouseStats(id) {
  return request({
    url: `/warehouses/${id}/stats`,
    method: 'get'
  })
}
