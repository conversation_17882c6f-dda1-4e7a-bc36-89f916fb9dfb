import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 15000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')

    // 如果请求需要认证但没有token，则取消请求
    if (config.url !== '/auth/login' && !token) {
      console.warn('请求被取消：未登录状态下尝试访问需要认证的接口', config.url)
      // 创建一个已取消的请求
      const cancelToken = axios.CancelToken
      const source = cancelToken.source()
      config.cancelToken = source.token
      source.cancel('未登录状态下取消请求')
      return config
    }

    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    console.log('API响应成功:', response.config.url, response.status, response.data)

    // 处理204 No Content响应
    if (response.status === 204) {
      return { success: true }
    }

    // 处理201 Created响应
    if (response.status === 201) {
      return response.data
    }

    const res = response.data

    // 如果返回的状态码不是200、201和204，说明接口请求有误
    if (response.status !== 200 && response.status !== 201 && response.status !== 204) {
      ElMessage({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      // 401: 未登录或token过期
      if (response.status === 401) {
        // 重新登录
        localStorage.removeItem('token')
        router.push('/login')
      }

      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    // 如果是请求取消的错误，不显示错误信息
    if (axios.isCancel(error)) {
      console.log('请求已取消:', error.message)
      return Promise.reject(error)
    }

    console.error('API响应错误:', error.config?.url, error)

    if (error.response) {
      console.error('错误状态码:', error.response.status)
      console.error('错误数据:', error.response.data)

      // 处理401错误
      if (error.response.status === 401) {
        localStorage.removeItem('token')
        router.push('/login')
      }

      // 显示后端返回的错误信息，并提供更具体的指导
      let errorMsg = error.response.data?.message || error.message || '请求失败'
      let errorTip = ''

      // 根据错误信息提供具体的操作建议
      if (errorMsg.includes('not found in warehouse')) {
        const productId = errorMsg.match(/ID (\d+)/)?.[1]
        errorTip = `提示：产品ID ${productId || '未知'} 在仓库中不存在，请先在库存管理中添加该产品的库存记录`
      } else if (errorMsg.includes('Insufficient inventory')) {
        const productId = errorMsg.match(/ID (\d+)/)?.[1]
        errorTip = `提示：产品ID ${productId || '未知'} 库存不足，请先在库存管理中补充库存`
      } else if (error.response.status === 400) {
        errorTip = '提示：请检查输入数据是否正确'
      } else if (error.response.status === 404) {
        errorTip = '提示：请求的资源不存在'
      } else if (error.response.status === 500) {
        errorTip = '提示：服务器内部错误，请联系管理员'
      }

      // 如果有操作建议，添加到错误信息中
      if (errorTip) {
        errorMsg = `${errorMsg}\n${errorTip}`
      }

      ElMessage({
        message: errorMsg,
        type: 'error',
        duration: 8 * 1000, // 增加显示时间，让用户有足够时间阅读
        showClose: true,    // 显示关闭按钮
        dangerouslyUseHTMLString: false // 不使用HTML，避免XSS风险
      })
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('无响应:', error.request)
      ElMessage({
        message: '服务器无响应，请检查网络连接或联系管理员',
        type: 'error',
        duration: 8 * 1000,
        showClose: true
      })
    } else {
      // 请求配置出错
      console.error('请求配置错误:', error.message)
      ElMessage({
        message: `请求配置错误: ${error.message || '未知错误'}，请联系管理员`,
        type: 'error',
        duration: 8 * 1000,
        showClose: true
      })
    }

    return Promise.reject(error)
  }
)

export default service
