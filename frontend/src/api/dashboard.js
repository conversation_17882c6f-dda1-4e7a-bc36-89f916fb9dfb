import request from './request'

// 获取仪表盘概览数据
export function getDashboardOverview() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  })
}

// 获取销售统计数据
export function getSalesStats(params) {
  return request({
    url: '/dashboard/sales',
    method: 'get',
    params
  })
}

// 获取库存统计数据
export function getInventoryStats() {
  return request({
    url: '/dashboard/inventory',
    method: 'get'
  })
}

// 获取产品销售排行
export function getProductRanking(params) {
  return request({
    url: '/dashboard/product-ranking',
    method: 'get',
    params
  })
}

// 获取客户销售排行
export function getCustomerRanking(params) {
  return request({
    url: '/dashboard/customer-ranking',
    method: 'get',
    params
  })
}

// 获取销售趋势数据
export function getSalesTrend(params) {
  return request({
    url: '/dashboard/sales-trend',
    method: 'get',
    params
  })
}

// 获取库存预警数据
export function getInventoryAlert() {
  return request({
    url: '/dashboard/inventory-alert',
    method: 'get'
  })
}

// 获取最近订单
export function getRecentOrders(limit = 5) {
  return request({
    url: '/dashboard/recent-orders',
    method: 'get',
    params: { limit }
  })
}

// 获取最近出入库记录
export function getRecentStockMovements(limit = 5) {
  return request({
    url: '/dashboard/recent-stock-movements',
    method: 'get',
    params: { limit }
  })
}
