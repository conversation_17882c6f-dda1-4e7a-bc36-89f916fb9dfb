import request from './request'

// 获取订单列表
export function getOrders(params) {
  return request({
    url: '/orders',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderById(id) {
  return request({
    url: `/orders/${id}`,
    method: 'get'
  })
}

// 创建订单
export function createOrder(data) {
  return request({
    url: '/orders',
    method: 'post',
    data
  })
}

// 更新订单状态
export function updateOrderStatus(id, status) {
  return request({
    url: `/orders/${id}`,
    method: 'put',
    data: {
      status
    }
  })
}

// 更新订单支付金额
export function updateOrderPayment(id, paidAmount) {
  return request({
    url: `/orders/${id}`,
    method: 'put',
    data: {
      paidAmount
    }
  })
}

// 更新订单信息
export function updateOrder(id, data) {
  return request({
    url: `/orders/${id}`,
    method: 'put',
    data
  })
}

// 取消订单
export function cancelOrder(id) {
  return updateOrderStatus(id, 7) // 7表示已取消
}

// 确认订单
export function confirmOrder(id) {
  return updateOrderStatus(id, 2) // 2表示已确认
}

// 完成订单
export function completeOrder(id) {
  return updateOrderStatus(id, 6) // 6表示已完成
}

// 发货订单
export function shipOrder(id, shipmentData) {
  // 使用updateOrder函数，将状态更新为4（已发货），并添加发货信息
  return updateOrder(id, {
    status: 4, // 4表示已发货
    isShipped: true,
    shipmentInfo: shipmentData
  })
}

// 确认到货
export function confirmDelivery(id) {
  return updateOrder(id, {
    status: 5, // 5表示已交付
    isDelivered: true
  })
}

// 确认出库
export function confirmOutbound(id) {
  return updateOrder(id, {
    isOutboundConfirmed: true,
    status: 2 // 同时将订单状态更新为"已确认"
  })
}

// 创建支付记录
export function createPayment(data) {
  return request({
    url: '/payments',
    method: 'post',
    data
  })
}

// 获取订单支付记录
export function getOrderPayments(orderId) {
  return request({
    url: `/orders/${orderId}/payments`,
    method: 'get'
  })
}

// 获取客户订单
export function getCustomerOrders(customerId, params = {}) {
  return request({
    url: `/customers/${customerId}/orders`,
    method: 'get',
    params
  })
}

// 获取客户订单统计
export function getCustomerStats(customerId) {
  return request({
    url: `/customers/${customerId}/stats`,
    method: 'get'
  })
}
