import request from './request'

// 获取所有用户
export function getUsers(params) {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserById(id) {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/users/${id}`,
    method: 'put',
    data
  })
}

// 切换用户状态（启用/禁用）
export function toggleUserActive(id, isActive) {
  return request({
    url: `/users/${id}/status`,
    method: 'put',
    data: {
      isActive
    }
  })
}

// 重置用户密码
export function resetPassword(id) {
  return request({
    url: `/users/${id}/reset-password`,
    method: 'post'
  })
}
