/**
 * 密码加密工具
 * 使用AES-256-CBC对称加密算法和固定盐值
 */
import CryptoJS from 'crypto-js'

// 固定的加密密钥和盐值 (生产环境应从环境变量或配置中获取)
// 使用32字节密钥 (AES-256)
const SECRET_KEY = 'storehouse-secure-key-2023-secret32'
const SALT = 'storehouse-salt-value'

// 固定的初始化向量 (IV) - 16字节
const IV = 'storehouse-iv-123'

/**
 * 使用AES-256-CBC加密密码
 * @param {string} password - 原始密码
 * @returns {string} - 加密后的密码 (Base64编码)
 */
export function encryptPassword(password) {
  if (!password) return ''

  try {
    // 将盐值与密码组合
    const saltedPassword = password + SALT

    // 创建密钥和IV
    const key = CryptoJS.enc.Utf8.parse(SECRET_KEY.substring(0, 32))
    const iv = CryptoJS.enc.Utf8.parse(IV.substring(0, 16))

    // 使用CryptoJS进行AES-CBC加密，使用PKCS7填充
    const encrypted = CryptoJS.AES.encrypt(saltedPassword, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    // 返回Base64编码的加密结果
    return encrypted.toString()
  } catch (error) {
    console.error('Password encryption failed:', error)
    return ''
  }
}

/**
 * 解密密码（仅用于测试，实际使用中前端不需要解密）
 * @param {string} encryptedPassword - 加密后的密码
 * @returns {string} - 原始密码
 */
export function decryptPassword(encryptedPassword) {
  if (!encryptedPassword) return ''

  try {
    // 创建密钥和IV
    const key = CryptoJS.enc.Utf8.parse(SECRET_KEY.substring(0, 32))
    const iv = CryptoJS.enc.Utf8.parse(IV.substring(0, 16))

    // 使用CryptoJS进行AES-CBC解密
    const decrypted = CryptoJS.AES.decrypt(encryptedPassword, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    // 转换为UTF-8字符串
    const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8)

    // 移除盐值
    return decryptedStr.substring(0, decryptedStr.length - SALT.length)
  } catch (error) {
    console.error('Password decryption failed:', error)
    return ''
  }
}
