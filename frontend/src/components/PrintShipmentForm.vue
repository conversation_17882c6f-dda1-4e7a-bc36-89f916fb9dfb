<template>
  <div class="print-shipment-container">
    <div class="shipment-form">
      <div class="company-title">{{ order.company || '具体公司' }}</div>
      <h1 class="form-title">销 售 出 库 单</h1>

      <div class="form-header">
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">客户名称：</span>
            <span class="field-value">{{ order.customer?.name || '无' }}</span>
          </div>
          <div class="form-field">
            <span class="field-label">联系人姓名/电话：</span>
            <span class="field-value">{{ order.contactPerson || '无' }} / {{ order.contactPhone || '无' }}</span>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <span class="field-label">客户地址：</span>
            <span class="field-value">{{ order.deliveryAddress || '无' }}</span>
          </div>
          <div class="form-field">
            <span class="field-label">发货日期：</span>
            <span class="field-value">{{ formatDateTime(new Date()) }}</span>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <span class="field-label">检测报告数量：</span>
            <span class="field-value">_______份</span>
          </div>
        </div>
      </div>

      <table class="shipment-table">
        <thead>
          <tr>
            <th class="col-seq">序号</th>
            <th class="col-name">商品名称</th>
            <th class="col-spec">规格</th>
            <th class="col-unit">单位</th>
            <th class="col-quantity">数量</th>
            <th class="col-remark">备注</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in orderItems" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ item.product?.name }}</td>
            <td>{{ item.product?.specification }}</td>
            <td>{{ item.product?.unit }}</td>
            <td>{{ item.quantity }}</td>
            <td></td>
          </tr>
          <!-- 添加空行，确保至少有10行 -->
          <tr v-for="i in getEmptyRows()" :key="`empty-${i}`">
            <td>{{ orderItems.length + i }}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tbody>
      </table>

      <div class="form-footer">
        <p class="warning-text">如有质量问题或者型号，数量不对请勿损坏。一经损坏，恕不接受退换货，请知悉！</p>
        <p class="address-text">发货地址：四川省宜宾市翠屏区宜宾东方雨虹(宜宾)仓储批发中心</p>
        <div class="signature-row">
          <div class="signature-field">
            <span class="field-label">业务员/发货人：</span>
            <span class="field-value">邓雄丽</span>
          </div>
          <div class="signature-field">
            <span class="field-label">联系电话：</span>
            <span class="field-value">18408241074</span>
          </div>
        </div>
        <div class="signature-row">
          <div class="signature-field">
            <span class="field-label">收货人签字：</span>
            <span class="field-value"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue'

const props = defineProps({
  order: {
    type: Object,
    required: true
  },
  orderItems: {
    type: Array,
    required: true
  }
})

const printContainer = ref(null)

// 格式化日期时间为北京时间，精确到秒
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  // 使用toLocaleString设置为zh-CN区域，显示北京时间
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 计算需要添加的空行数，确保表格至少有10行
const getEmptyRows = () => {
  const currentRows = props.orderItems.length
  return currentRows < 10 ? 10 - currentRows : 0
}
</script>

<style scoped>
/* 在非打印模式下隐藏 */
.print-shipment-container {
  display: none;
}

/* 共享样式（预览和打印都使用） */
.shipment-form {
  width: 100%;
  font-family: SimSun, "宋体", sans-serif;
  font-size: 14px;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.company-title {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 15px;
  width: 100%;
}

.form-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  letter-spacing: 8px;
}

.form-header {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.form-field {
  display: flex;
  align-items: flex-start;
}

.field-label {
  font-weight: bold;
  min-width: 120px;
}

.field-value {
  flex: 1;
}

/* 表格样式 */
.shipment-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  table-layout: fixed;
}

.shipment-table th, .shipment-table td {
  border: 1px solid #000;
  padding: 8px;
  text-align: center;
  font-size: 14px;
  height: 30px;
  overflow: hidden;
  word-break: break-all;
}

/* 列宽设置 */
.col-seq {
  width: 8%;
}

.col-name {
  width: 40%;
}

.col-spec {
  width: 12%;
}

.col-unit {
  width: 10%;
}

.col-quantity {
  width: 10%;
}

.col-remark {
  width: 20%;
}

/* 底部样式 */
.form-footer {
  margin-top: 30px;
  font-size: 14px;
}

.warning-text {
  font-weight: bold;
  margin-bottom: 20px;
}

.address-text {
  margin-bottom: 20px;
}

.signature-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.signature-field {
  display: flex;
  align-items: center;
}

/* 打印样式 */
@media print {
  .print-shipment-container {
    display: block !important;
    width: 100%;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    page-break-after: always;
  }

  /* 隐藏其他元素 */
  body > *:not(.print-shipment-container) {
    display: none !important;
  }

  .shipment-form {
    padding: 0;
  }

  /* 确保表格在打印时不会被分页 */
  .shipment-table {
    page-break-inside: avoid;
  }

  /* 确保页脚在打印时不会被分页 */
  .form-footer {
    page-break-inside: avoid;
  }

  /* 隐藏浏览器默认的页眉页脚 */
  @page {
    margin: 0;
    size: auto;
  }
}
</style>
