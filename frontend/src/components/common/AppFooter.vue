<template>
  <div class="footer-container">
    <span>仓库管理系统 &copy; {{ currentYear }}</span>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const currentYear = ref(new Date().getFullYear())
</script>

<style scoped>
.footer-container {
  text-align: center;
  color: #606266;
  font-size: 12px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
