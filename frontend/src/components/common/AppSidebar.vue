<template>
  <div class="sidebar-container">
    <div class="logo-container">
      <h1 class="logo-title">仓库管理系统</h1>
    </div>
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      router
      unique-opened
    >
      <el-menu-item index="/dashboard">
        <el-icon><DataLine /></el-icon>
        <span>仪表盘</span>
      </el-menu-item>

      <el-menu-item index="/orders">
        <el-icon><ShoppingCart /></el-icon>
        <span>订单管理</span>
      </el-menu-item>

      <el-menu-item index="/products">
        <el-icon><Goods /></el-icon>
        <span>产品管理</span>
      </el-menu-item>

      <el-menu-item index="/inventory">
        <el-icon><List /></el-icon>
        <span>库存管理</span>
      </el-menu-item>

      <el-menu-item index="/stock-movements">
        <el-icon><SwitchButton /></el-icon>
        <span>出入库管理</span>
      </el-menu-item>

      <el-menu-item index="/customers">
        <el-icon><User /></el-icon>
        <span>客户管理</span>
      </el-menu-item>



      <el-menu-item index="/reports">
        <el-icon><Document /></el-icon>
        <span>报表管理</span>
      </el-menu-item>

       <el-menu-item index="/settings">
        <el-icon><Setting /></el-icon>
        <span>系统设置</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { DataLine, Goods, List, SwitchButton, User, ShoppingCart, Document, Setting, Money } from '@element-plus/icons-vue'

const route = useRoute()

const activeMenu = computed(() => {
  return route.path
})
</script>

<style scoped>
.sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b2f3a;
}

.logo-title {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin: 0;
  white-space: nowrap;
}

.sidebar-menu {
  border-right: none;
  height: calc(100% - 60px);
}
</style>
