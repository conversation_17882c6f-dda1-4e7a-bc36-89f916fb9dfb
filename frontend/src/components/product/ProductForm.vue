<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    label-position="right"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="产品编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入产品编码" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入产品名称" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="产品类别" prop="category">
          <el-input v-model="form.category" placeholder="请输入产品类别" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入产品规格" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入计量单位" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="form.price" :precision="2" :min="0" style="width: 100%" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="最低库存" prop="minStock">
          <el-input-number v-model="form.minStock" :precision="2" :min="0" style="width: 100%" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="最高库存" prop="maxStock">
          <el-input-number v-model="form.maxStock" :precision="2" :min="0" style="width: 100%" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="条形码" prop="barcode">
          <el-input v-model="form.barcode" placeholder="请输入条形码" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="form.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="描述" prop="description">
      <el-input
        v-model="form.description"
        type="textarea"
        :rows="3"
        placeholder="请输入产品描述"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, defineProps, defineExpose, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createProduct, updateProduct } from '../../api/product'

const props = defineProps({
  product: {
    type: Object,
    default: () => ({
      id: '',
      code: '',
      name: '',
      description: '',
      specification: '',
      unit: '',
      minStock: 0,
      maxStock: 0,
      isActive: true,
      barcode: '',
      category: '',
      price: 0
    })
  },
  type: {
    type: String,
    default: 'add' // 'add' or 'edit'
  }
})

const formRef = ref(null)
const form = reactive({
  id: '',
  code: '',
  name: '',
  description: '',
  specification: '',
  unit: '',
  minStock: 0,
  maxStock: 0,
  isActive: true,
  barcode: '',
  category: '',
  price: 0
})

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入产品编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格必须大于等于0', trigger: 'blur' }
  ],
  minStock: [
    { type: 'number', min: 0, message: '最低库存必须大于等于0', trigger: 'blur' }
  ],
  maxStock: [
    { type: 'number', min: 0, message: '最高库存必须大于等于0', trigger: 'blur' }
  ]
}

// 监听产品数据变化
watch(() => props.product, (newVal) => {
  Object.assign(form, newVal)
}, { deep: true, immediate: true })

// 表单验证
const validate = async () => {
  if (!formRef.value) return false
  
  return await formRef.value.validate()
    .then(() => true)
    .catch(() => {
      ElMessage.warning('请正确填写表单')
      return false
    })
}

// 保存产品
const save = async () => {
  try {
    if (props.type === 'add') {
      await createProduct(form)
      ElMessage.success('创建产品成功')
    } else {
      await updateProduct(form.id, form)
      ElMessage.success('更新产品成功')
    }
    return true
  } catch (error) {
    console.error('Failed to save product:', error)
    ElMessage.error(props.type === 'add' ? '创建产品失败' : '更新产品失败')
    return false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  save,
  resetForm
})
</script>

<style scoped>
.el-form {
  max-width: 100%;
}
</style>
