<template>
  <el-config-provider>
    <div class="app-container">
      <el-container v-if="isAuthenticated" class="main-container">
        <el-aside width="220px">
          <app-sidebar />
        </el-aside>
        <el-container class="content-container">
          <el-header>
            <app-header />
          </el-header>
          <el-main>
            <router-view />
          </el-main>
          <el-footer>
            <app-footer />
          </el-footer>
        </el-container>
      </el-container>
      <router-view v-else />
    </div>
  </el-config-provider>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from './store/user'
import AppSidebar from './components/common/AppSidebar.vue'
import AppHeader from './components/common/AppHeader.vue'
import AppFooter from './components/common/AppFooter.vue'

const userStore = useUserStore()
const router = useRouter()

const isAuthenticated = computed(() => userStore.isAuthenticated)

// 检查用户认证状态
if (!isAuthenticated.value && router.currentRoute.value.meta.requiresAuth) {
  router.push('/login')
}
</script>

<style>
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-container {
  height: 100%;
}

.content-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.el-header {
  padding: 0;
  height: 60px;
  line-height: 60px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.el-aside {
  background-color: #304156;
  color: #fff;
}

.el-main {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.el-footer {
  padding: 0;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #f5f7fa;
  position: relative;
  bottom: 0;
  width: 100%;
}
</style>
