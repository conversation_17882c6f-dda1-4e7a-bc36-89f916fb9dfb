<template>
  <div class="crypto-test-container">
    <h1>密码加密测试</h1>
    
    <el-card class="test-card">
      <div class="test-section">
        <h2>加密测试</h2>
        <el-form>
          <el-form-item label="原始密码">
            <el-input v-model="originalPassword" placeholder="请输入密码"></el-input>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="encryptTest">加密</el-button>
          </el-form-item>
          
          <el-form-item label="加密结果" v-if="encryptedResult">
            <el-input v-model="encryptedResult" readonly></el-input>
          </el-form-item>
        </el-form>
      </div>
      
      <el-divider></el-divider>
      
      <div class="test-section">
        <h2>解密测试</h2>
        <p class="note">注意：此功能仅用于测试，实际应用中前端不需要解密</p>
        
        <el-form>
          <el-form-item label="加密密码">
            <el-input v-model="encryptedPassword" placeholder="请输入加密后的密码"></el-input>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="decryptTest">解密</el-button>
          </el-form-item>
          
          <el-form-item label="解密结果" v-if="decryptedResult">
            <el-input v-model="decryptedResult" readonly></el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { encryptPassword, decryptPassword } from '../utils/crypto'
import { ElMessage } from 'element-plus'

// 加密测试
const originalPassword = ref('')
const encryptedResult = ref('')

const encryptTest = () => {
  if (!originalPassword.value) {
    ElMessage.warning('请输入原始密码')
    return
  }
  
  try {
    encryptedResult.value = encryptPassword(originalPassword.value)
    ElMessage.success('加密成功')
  } catch (error) {
    console.error('加密失败:', error)
    ElMessage.error('加密失败: ' + error.message)
  }
}

// 解密测试
const encryptedPassword = ref('')
const decryptedResult = ref('')

const decryptTest = () => {
  if (!encryptedPassword.value) {
    ElMessage.warning('请输入加密后的密码')
    return
  }
  
  try {
    decryptedResult.value = decryptPassword(encryptedPassword.value)
    ElMessage.success('解密成功')
  } catch (error) {
    console.error('解密失败:', error)
    ElMessage.error('解密失败: ' + error.message)
  }
}
</script>

<style scoped>
.crypto-test-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.test-card {
  margin-top: 20px;
}

.test-section {
  margin: 20px 0;
}

.note {
  font-size: 12px;
  color: #999;
  margin-bottom: 15px;
}
</style>
