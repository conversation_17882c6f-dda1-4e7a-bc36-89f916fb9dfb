<template>
  <div class="page-container">
    <div class="page-title">收款情况报表</div>

    <div class="action-bar">
      <div class="left-actions">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>导出
        </el-button>
      </div>

      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索订单号或客户..."
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="el-input__icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select v-model="filterPaymentStatus" placeholder="收款状态" clearable @change="handleSearch">
          <el-option label="全部状态" value="" />
          <el-option label="已付清" value="paid" />
          <el-option label="部分付款" value="partial" />
          <el-option label="未付款" value="unpaid" />
        </el-select>

        <el-select v-model="filterCustomerId" placeholder="客户" clearable @change="handleSearch">
          <el-option label="全部客户" value="" />
          <el-option
            v-for="customer in customerOptions"
            :key="customer.id"
            :label="customer.name"
            :value="customer.id"
          />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="handleSearch"
        />
      </div>
    </div>

    <!-- 收款统计卡片 -->
    <div class="stats-cards">
      <el-card class="stats-card">
        <div class="stats-title">订单总金额</div>
        <div class="stats-value">¥{{ formatNumber(totalOrderAmount) }}</div>
      </el-card>
      <el-card class="stats-card">
        <div class="stats-title">已收款总额</div>
        <div class="stats-value">¥{{ formatNumber(totalPaidAmount) }}</div>
      </el-card>
      <el-card class="stats-card">
        <div class="stats-title">未收款总额</div>
        <div class="stats-value unpaid-amount">¥{{ formatNumber(totalUnpaidAmount) }}</div>
      </el-card>
      <el-card class="stats-card">
        <div class="stats-title">收款率</div>
        <div class="stats-value">{{ paymentRate }}%</div>
      </el-card>
    </div>

    <!-- 订单收款表格 -->
    <el-table
      v-loading="loading"
      :data="orderList.filter(order => order.status !== 7)"
      border
      class="data-table"
    >

      <el-table-column prop="orderNumber" label="订单号" width="150" />
      <el-table-column label="客户" width="120">
        <template #default="scope">
          <div>{{ scope.row.customer.name }}</div>
          <div class="text-muted">{{ scope.row.customer.code }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单日期" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.orderDate) }}
        </template>
      </el-table-column>
      <el-table-column label="金额" width="120">
        <template #default="scope">
          <div>总额: ¥{{ formatNumber(scope.row.totalAmount) }}</div>
          <div>已付: ¥{{ formatNumber(scope.row.paidAmount) }}</div>
          <div v-if="scope.row.unpaidAmount > 0" class="unpaid-amount">
            欠款: ¥{{ formatNumber(scope.row.unpaidAmount) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="收款状态" width="100">
        <template #default="scope">
          <el-tag :type="getPaymentStatusType(scope.row)">
            {{ getPaymentStatusText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" width="100">
        <template #default="scope">
          {{ scope.row.paymentMethod || '未指定' }}
        </template>
      </el-table-column>
      <el-table-column label="最近付款" width="180">
        <template #default="scope">
          <template v-if="scope.row.payments && scope.row.payments.length > 0">
            {{ formatDateTime(scope.row.payments[0].paymentDate) }}
          </template>
          <template v-else>
            <span class="text-muted">无付款记录</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <div class="operation-buttons">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleAddPayment(scope.row)">添加付款</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加付款对话框 -->
    <el-dialog v-model="paymentDialogVisible" title="添加付款记录" width="500px">
      <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="paymentForm.orderNumber" disabled />
        </el-form-item>
        <el-form-item label="客户">
          <el-input v-model="paymentForm.customerName" disabled />
        </el-form-item>
        <el-form-item label="欠款金额">
          <el-input v-model="paymentForm.unpaidAmount" disabled>
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="支付金额" prop="amount">
          <el-input-number v-model="paymentForm.amount" :precision="2" :min="0.01" :max="paymentForm.unpaidAmount" style="width: 100%" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="paymentForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="对公" value="corporate" />
            <el-option label="对私" value="personal" />
            <el-option label="微信" value="wechat" />
            <el-option label="支付宝" value="alipay" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付日期" prop="paymentDate">
          <el-date-picker v-model="paymentForm.paymentDate" type="date" placeholder="选择日期" style="width: 100%" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="paymentForm.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="paymentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPayment" :loading="submitting">确认添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download, Search } from '@element-plus/icons-vue'
import { getOrders } from '../../api/order'
import { getCustomers } from '../../api/customer'
import { createPayment } from '../../api/order'

const router = useRouter()
const loading = ref(false)
const submitting = ref(false)
const orderList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filterPaymentStatus = ref('')
const filterCustomerId = ref('')
const dateRange = ref([])
const customerOptions = ref([])
const paymentDialogVisible = ref(false)
const paymentFormRef = ref(null)

// 支付表单
const paymentForm = reactive({
  orderId: '',
  orderNumber: '',
  customerName: '',
  unpaidAmount: 0,
  amount: 0,
  paymentMethod: '',
  paymentDate: '',
  remarks: ''
})

// 支付表单验证规则
const paymentRules = {
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  paymentDate: [
    { required: true, message: '请选择支付日期', trigger: 'change' }
  ]
}

// 计算统计数据
const totalOrderAmount = computed(() => {
  return orderList.value
    .filter(order => order.status !== 7) // 排除已取消的订单
    .reduce((sum, order) => sum + order.totalAmount, 0)
})

const totalPaidAmount = computed(() => {
  return orderList.value
    .filter(order => order.status !== 7) // 排除已取消的订单
    .reduce((sum, order) => sum + order.paidAmount, 0)
})

const totalUnpaidAmount = computed(() => {
  return orderList.value
    .filter(order => order.status !== 7) // 排除已取消的订单
    .reduce((sum, order) => sum + (order.unpaidAmount || 0), 0)
})

const paymentRate = computed(() => {
  if (totalOrderAmount.value === 0) return '0.00'
  return ((totalPaidAmount.value / totalOrderAmount.value) * 100).toFixed(2)
})

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      query: searchQuery.value,
      customerId: filterCustomerId.value,
      excludeCancelled: true // 排除已取消的订单
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    if (filterPaymentStatus.value) {
      params.paymentStatus = filterPaymentStatus.value
    }

    const res = await getOrders(params)
    orderList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('Failed to fetch orders:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取客户选项
const fetchCustomerOptions = async () => {
  try {
    const res = await getCustomers({ pageSize: 100 })
    customerOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch customers:', error)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchOrders()
}

// 处理刷新
const handleRefresh = () => {
  searchQuery.value = ''
  filterPaymentStatus.value = ''
  filterCustomerId.value = ''
  dateRange.value = []
  currentPage.value = 1
  fetchOrders()
}

// 处理导出
const handleExport = () => {
  ElMessage.success('导出功能待实现')
}

// 处理查看
const handleView = (row) => {
  router.push(`/orders/${row.id}`)
}

// 处理添加付款
const handleAddPayment = (row) => {
  paymentForm.orderId = row.id
  paymentForm.orderNumber = row.orderNumber
  paymentForm.customerName = row.customer.name
  paymentForm.unpaidAmount = row.unpaidAmount || 0
  paymentForm.amount = row.unpaidAmount || 0
  paymentForm.paymentMethod = row.paymentMethod || 'corporate'
  paymentForm.paymentDate = new Date().toISOString().split('T')[0]
  paymentForm.remarks = ''

  paymentDialogVisible.value = true
}

// 提交付款
const submitPayment = async () => {
  if (!paymentFormRef.value) return

  await paymentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await createPayment({
          orderId: paymentForm.orderId,
          amount: paymentForm.amount,
          paymentMethod: paymentForm.paymentMethod,
          paymentDate: paymentForm.paymentDate,
          remarks: paymentForm.remarks
        })

        ElMessage.success('付款记录添加成功')
        paymentDialogVisible.value = false

        // 刷新订单数据
        await fetchOrders()
      } catch (error) {
        console.error('添加付款记录失败:', error)
        ElMessage.error('添加付款记录失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 获取收款状态文本
const getPaymentStatusText = (order) => {
  if (order.totalAmount === 0) return '无需付款'
  if (order.paidAmount >= order.totalAmount) return '已付清'
  if (order.paidAmount > 0) return '部分付款'
  return '未付款'
}

// 获取收款状态标签类型
const getPaymentStatusType = (order) => {
  if (order.totalAmount === 0) return 'info'
  if (order.paidAmount >= order.totalAmount) return 'success'
  if (order.paidAmount > 0) return 'warning'
  return 'danger'
}

// 格式化日期时间为北京时间，精确到秒
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  // 使用toLocaleString设置为zh-CN区域，显示北京时间
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 格式化数字为两位小数
const formatNumber = (num) => {
  return num ? num.toFixed(2) : '0.00'
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchOrders()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchOrders()
}

onMounted(() => {
  fetchOrders()
  fetchCustomerOptions()
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  gap: 10px;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.left-actions {
  display: flex;
  gap: 10px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.unpaid-amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 12px;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  flex: 1;
  text-align: center;
}

.stats-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}
</style>
