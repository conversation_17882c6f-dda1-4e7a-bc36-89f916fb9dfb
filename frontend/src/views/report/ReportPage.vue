<template>
  <div class="page-container">
    <div class="page-title">数据报表</div>

    <el-tabs v-model="activeTab" class="report-tabs">
      <el-tab-pane label="销售报表" name="sales">
        <div class="filter-bar">
          <el-date-picker
            v-model="salesDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSalesDateChange"
          />

          <el-select v-model="salesGroupBy" placeholder="分组方式" @change="handleSalesGroupChange">
            <el-option label="按日" value="day" />
            <el-option label="按周" value="week" />
            <el-option label="按月" value="month" />
          </el-select>

          <el-button type="primary" @click="fetchSalesReport">
            <el-icon><Search /></el-icon>查询
          </el-button>

          <el-button @click="exportSalesReport">
            <el-icon><Download /></el-icon>导出
          </el-button>
        </div>

        <div class="report-summary">
          <el-row :gutter="20">
            <el-col :span="6" v-for="(card, index) in salesSummary" :key="index">
              <el-card class="summary-card" :body-style="{ padding: '20px' }">
                <div class="summary-card-title">{{ card.title }}</div>
                <div class="summary-card-value">{{ card.value }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div class="chart-container">
          <div ref="salesChartRef" class="chart"></div>
        </div>

        <el-table
          v-loading="loadingSales"
          :data="salesData"
          border
          class="data-table"
        >
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="orderCount" label="订单数" width="100" />
          <el-table-column prop="productCount" label="商品数" width="100" />
          <el-table-column prop="amount" label="销售额" width="120">
            <template #default="scope">
              ¥{{ scope.row.amount ? scope.row.amount.toFixed(2) : '0.00' }}
            </template>
          </el-table-column>
          <el-table-column prop="avgOrderAmount" label="平均订单金额" width="150">
            <template #default="scope">
              ¥{{ scope.row.avgOrderAmount ? scope.row.avgOrderAmount.toFixed(2) : '0.00' }}
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="库存报表" name="inventory">
        <div class="filter-bar">
          <el-input
            v-model="inventorySearchQuery"
            placeholder="搜索产品..."
            clearable
            style="width: 300px"
            @keyup.enter="fetchInventoryReport"
          >
            <template #suffix>
              <el-icon class="el-input__icon" @click="fetchInventoryReport">
                <Search />
              </el-icon>
            </template>
          </el-input>

          <el-select v-model="inventoryStatus" placeholder="库存状态" clearable @change="fetchInventoryReport">
            <el-option label="全部" value="" />
            <el-option label="库存不足" value="low" />
            <el-option label="库存正常" value="normal" />
            <el-option label="库存过多" value="high" />
          </el-select>

          <el-button type="primary" @click="fetchInventoryReport">
            <el-icon><Search /></el-icon>查询
          </el-button>

          <el-button @click="exportInventoryReport">
            <el-icon><Download /></el-icon>导出
          </el-button>
        </div>

        <div class="report-summary">
          <el-row :gutter="20">
            <el-col :span="6" v-for="(card, index) in inventorySummary" :key="index">
              <el-card class="summary-card" :body-style="{ padding: '20px' }">
                <div class="summary-card-title">{{ card.title }}</div>
                <div class="summary-card-value" :class="card.class">{{ card.value }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div class="chart-container">
          <div ref="inventoryChartRef" class="chart"></div>
        </div>

        <el-table
          v-loading="loadingInventory"
          :data="inventoryData"
          border
          class="data-table"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="code" label="产品编码" width="120" />
          <el-table-column prop="name" label="产品名称" min-width="150" />
          <el-table-column prop="specification" label="规格" width="120" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="currentStock" label="当前库存" width="100" />
          <el-table-column prop="minStock" label="最低库存" width="100" />
          <el-table-column prop="maxStock" label="最高库存" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getInventoryStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button size="small" type="primary" @click="viewProduct(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="inventoryCurrentPage"
            v-model:page-size="inventoryPageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="inventoryTotal"
            @size-change="handleInventorySizeChange"
            @current-change="handleInventoryCurrentChange"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="客户报表" name="customer">
        <div class="filter-bar">
          <el-date-picker
            v-model="customerDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleCustomerDateChange"
          />

          <el-input
            v-model="customerSearchQuery"
            placeholder="搜索客户..."
            clearable
            style="width: 300px"
            @keyup.enter="fetchCustomerReport"
          >
            <template #suffix>
              <el-icon class="el-input__icon" @click="fetchCustomerReport">
                <Search />
              </el-icon>
            </template>
          </el-input>

          <el-button type="primary" @click="fetchCustomerReport">
            <el-icon><Search /></el-icon>查询
          </el-button>

          <el-button @click="exportCustomerReport">
            <el-icon><Download /></el-icon>导出
          </el-button>
        </div>

        <div class="chart-container">
          <div ref="customerChartRef" class="chart"></div>
        </div>

        <el-table
          v-loading="loadingCustomer"
          :data="customerData"
          border
          class="data-table"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="code" label="客户编码" width="120" />
          <el-table-column prop="name" label="客户名称" min-width="150" />
          <el-table-column prop="orderCount" label="订单数" width="100" />
          <el-table-column prop="totalAmount" label="总金额" width="120">
            <template #default="scope">
              ¥{{ scope.row.totalAmount ? scope.row.totalAmount.toFixed(2) : '0.00' }}
            </template>
          </el-table-column>
          <el-table-column prop="avgOrderAmount" label="平均订单金额" width="150">
            <template #default="scope">
              ¥{{ scope.row.avgOrderAmount ? scope.row.avgOrderAmount.toFixed(2) : '0.00' }}
            </template>
          </el-table-column>
          <el-table-column prop="lastOrderDate" label="最近订单" width="180" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button size="small" type="primary" @click="viewCustomer(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="customerCurrentPage"
            v-model:page-size="customerPageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="customerTotal"
            @size-change="handleCustomerSizeChange"
            @current-change="handleCustomerCurrentChange"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="销售人员业绩" name="salesperson">
        <SalesPersonReport />
      </el-tab-pane>

      <el-tab-pane label="收款情况" name="payment">
        <div class="filter-bar">
          <div class="left-actions">
            <el-button @click="handlePaymentRefresh">
              <el-icon><Refresh /></el-icon>刷新
            </el-button>
            <el-button @click="handlePaymentExport">
              <el-icon><Download /></el-icon>导出
            </el-button>
          </div>

          <div class="search-bar">
            <el-input
              v-model="paymentSearchQuery"
              placeholder="搜索订单号或客户..."
              clearable
              @keyup.enter="handlePaymentSearch"
            >
              <template #suffix>
                <el-icon class="el-input__icon" @click="handlePaymentSearch">
                  <Search />
                </el-icon>
              </template>
            </el-input>

            <el-select v-model="paymentStatus" placeholder="收款状态" clearable @change="handlePaymentSearch">
              <el-option label="全部状态" value="" />
              <el-option label="已付清" value="paid" />
              <el-option label="部分付款" value="partial" />
              <el-option label="未付款" value="unpaid" />
            </el-select>

            <el-select v-model="paymentCustomerId" placeholder="客户" clearable @change="handlePaymentSearch">
              <el-option label="全部客户" value="" />
              <el-option
                v-for="customer in paymentCustomerOptions"
                :key="customer.id"
                :label="customer.name"
                :value="customer.id"
              />
            </el-select>

            <el-date-picker
              v-model="paymentDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handlePaymentSearch"
            />
          </div>
        </div>

        <!-- 收款统计卡片 -->
        <div class="stats-cards">
          <el-card class="stats-card">
            <div class="stats-title">订单总金额</div>
            <div class="stats-value">¥{{ formatNumber(totalOrderAmount) }}</div>
          </el-card>
          <el-card class="stats-card">
            <div class="stats-title">已收款总额</div>
            <div class="stats-value">¥{{ formatNumber(totalPaidAmount) }}</div>
          </el-card>
          <el-card class="stats-card">
            <div class="stats-title">未收款总额</div>
            <div class="stats-value unpaid-amount">¥{{ formatNumber(totalUnpaidAmount) }}</div>
          </el-card>
          <el-card class="stats-card">
            <div class="stats-title">收款率</div>
            <div class="stats-value">{{ paymentRate }}%</div>
          </el-card>
        </div>

        <!-- 订单收款表格 -->
        <el-table
          v-loading="loadingPayment"
          :data="orderList"
          border
          class="data-table"
        >
          <el-table-column prop="orderNumber" label="订单号" width="150" />
          <el-table-column label="客户" width="120">
            <template #default="scope">
              <div>{{ scope.row.customer.name }}</div>
              <div class="text-muted">{{ scope.row.customer.code }}</div>
            </template>
          </el-table-column>
          <el-table-column label="订单日期" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.orderDate) }}
            </template>
          </el-table-column>
          <el-table-column label="金额" width="120">
            <template #default="scope">
              <div>总额: ¥{{ formatNumber(scope.row.totalAmount) }}</div>
              <div>已付: ¥{{ formatNumber(scope.row.paidAmount) }}</div>
              <div v-if="scope.row.unpaidAmount > 0" class="unpaid-amount">
                欠款: ¥{{ formatNumber(scope.row.unpaidAmount) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="收款状态" width="100">
            <template #default="scope">
              <el-tag :type="getPaymentStatusType(scope.row)">
                {{ getPaymentStatusText(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="支付方式" width="100">
            <template #default="scope">
              {{ scope.row.paymentMethod || '未指定' }}
            </template>
          </el-table-column>
          <el-table-column label="最近付款" width="180">
            <template #default="scope">
              <template v-if="scope.row.payments && scope.row.payments.length > 0">
                {{ formatDateTime(scope.row.payments[0].paymentDate) }}
              </template>
              <template v-else>
                <span class="text-muted">无付款记录</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <div class="operation-buttons">
                <el-button size="small" @click="handleViewOrder(scope.row)">查看</el-button>
                <el-button size="small" type="primary" @click="handleAddPayment(scope.row)">添加付款</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="paymentCurrentPage"
            v-model:page-size="paymentPageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paymentTotal"
            @size-change="handlePaymentSizeChange"
            @current-change="handlePaymentCurrentChange"
          />
        </div>

        <!-- 添加付款对话框 -->
        <el-dialog v-model="paymentDialogVisible" title="添加付款记录" width="500px">
          <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="100px">
            <el-form-item label="订单号">
              <el-input v-model="paymentForm.orderNumber" disabled />
            </el-form-item>
            <el-form-item label="客户">
              <el-input v-model="paymentForm.customerName" disabled />
            </el-form-item>
            <el-form-item label="欠款金额">
              <el-input v-model="paymentForm.unpaidAmount" disabled>
                <template #prepend>¥</template>
              </el-input>
            </el-form-item>
            <el-form-item label="支付金额" prop="amount">
              <el-input-number v-model="paymentForm.amount" :precision="2" :min="0.01" :max="paymentForm.unpaidAmount" style="width: 100%" />
            </el-form-item>
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-select v-model="paymentForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
                <el-option label="对公" value="corporate" />
                <el-option label="对私" value="personal" />
                <el-option label="微信" value="wechat" />
                <el-option label="支付宝" value="alipay" />
              </el-select>
            </el-form-item>
            <el-form-item label="支付日期" prop="paymentDate">
              <el-date-picker v-model="paymentForm.paymentDate" type="date" placeholder="选择日期" style="width: 100%" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="paymentForm.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="paymentDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="submitPayment" :loading="submitting">确认添加</el-button>
            </span>
          </template>
        </el-dialog>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Download, Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getSalesReport, exportSalesReport as exportSales } from '../../api/report'
import { getInventoryReport, exportInventoryReport as exportInventory } from '../../api/report'
import { getCustomerReport, exportCustomerReport as exportCustomer } from '../../api/report'
import { getOrders } from '../../api/order'
import { getCustomers } from '../../api/customer'
import { createPayment } from '../../api/order'
import SalesPersonReport from './SalesPersonReport.vue'

const router = useRouter()
const route = useRoute()
// 根据路由的meta信息设置默认标签页
const activeTab = ref(route.meta.defaultTab || 'sales')
const salesChartRef = ref(null)
const inventoryChartRef = ref(null)
const customerChartRef = ref(null)
let salesChart = null
let inventoryChart = null
let customerChart = null

// 销售报表相关
const loadingSales = ref(false)
const salesDateRange = ref([])
const salesGroupBy = ref('day')
const salesData = ref([])
const salesSummary = ref([
  { title: '总订单数', value: '0' },
  { title: '总销售额', value: '¥0.00' },
  { title: '平均订单金额', value: '¥0.00' },
  { title: '总商品数', value: '0' }
])

// 库存报表相关
const loadingInventory = ref(false)
const inventorySearchQuery = ref('')
const inventoryStatus = ref('')
const inventoryData = ref([])
const inventoryCurrentPage = ref(1)
const inventoryPageSize = ref(10)
const inventoryTotal = ref(0)
const inventorySummary = ref([
  { title: '总产品数', value: '0', class: '' },
  { title: '库存不足', value: '0', class: 'text-danger' },
  { title: '库存正常', value: '0', class: 'text-success' },
  { title: '库存过多', value: '0', class: 'text-warning' }
])

// 客户报表相关
const loadingCustomer = ref(false)
const customerDateRange = ref([])
const customerSearchQuery = ref('')
const customerData = ref([])
const customerCurrentPage = ref(1)
const customerPageSize = ref(10)
const customerTotal = ref(0)

// 收款情况报表相关
const loadingPayment = ref(false)
const submitting = ref(false)
const orderList = ref([])
const paymentTotal = ref(0)
const paymentCurrentPage = ref(1)
const paymentPageSize = ref(10)
const paymentSearchQuery = ref('')
const paymentStatus = ref('')
const paymentCustomerId = ref('')
const paymentDateRange = ref([])
const paymentCustomerOptions = ref([])
const paymentDialogVisible = ref(false)
const paymentFormRef = ref(null)

// 支付表单
const paymentForm = reactive({
  orderId: '',
  orderNumber: '',
  customerName: '',
  unpaidAmount: 0,
  amount: 0,
  paymentMethod: '',
  paymentDate: '',
  remarks: ''
})

// 支付表单验证规则
const paymentRules = {
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  paymentDate: [
    { required: true, message: '请选择支付日期', trigger: 'change' }
  ]
}

// 计算统计数据
const totalOrderAmount = computed(() => {
  return orderList.value.reduce((sum, order) => sum + order.totalAmount, 0)
})

const totalPaidAmount = computed(() => {
  return orderList.value.reduce((sum, order) => sum + order.paidAmount, 0)
})

const totalUnpaidAmount = computed(() => {
  return orderList.value.reduce((sum, order) => sum + (order.unpaidAmount || 0), 0)
})

const paymentRate = computed(() => {
  if (totalOrderAmount.value === 0) return '0.00'
  return ((totalPaidAmount.value / totalOrderAmount.value) * 100).toFixed(2)
})

// 获取销售报表
const fetchSalesReport = async () => {
  loadingSales.value = true
  try {
    const params = {
      startDate: salesDateRange.value ? salesDateRange.value[0] : undefined,
      endDate: salesDateRange.value ? salesDateRange.value[1] : undefined,
      groupBy: salesGroupBy.value
    }

    const res = await getSalesReport(params)
    salesData.value = res.data

    // 更新销售摘要
    salesSummary.value[0].value = res.summary.totalOrders ? res.summary.totalOrders.toString() : '0'
    salesSummary.value[1].value = `¥${res.summary.totalAmount ? res.summary.totalAmount.toFixed(2) : '0.00'}`
    salesSummary.value[2].value = `¥${res.summary.avgOrderAmount ? res.summary.avgOrderAmount.toFixed(2) : '0.00'}`
    salesSummary.value[3].value = res.summary.totalProducts ? res.summary.totalProducts.toString() : '0'

    // 更新图表
    initSalesChart(res.data)
  } catch (error) {
    console.error('Failed to fetch sales report:', error)
    ElMessage.error('获取销售报表失败')
  } finally {
    loadingSales.value = false
  }
}

// 获取库存报表
const fetchInventoryReport = async () => {
  loadingInventory.value = true
  try {
    const params = {
      page: inventoryCurrentPage.value,
      pageSize: inventoryPageSize.value,
      query: inventorySearchQuery.value,
      status: inventoryStatus.value
    }

    const res = await getInventoryReport(params)
    inventoryData.value = res.data
    inventoryTotal.value = res.total

    // 更新库存摘要
    inventorySummary.value[0].value = res.summary.totalProducts.toString()
    inventorySummary.value[1].value = res.summary.lowStock.toString()
    inventorySummary.value[2].value = res.summary.normalStock.toString()
    inventorySummary.value[3].value = res.summary.highStock.toString()

    // 更新图表
    initInventoryChart(res.summary)
  } catch (error) {
    console.error('Failed to fetch inventory report:', error)
    ElMessage.error('获取库存报表失败')
  } finally {
    loadingInventory.value = false
  }
}

// 获取客户报表
const fetchCustomerReport = async () => {
  loadingCustomer.value = true
  try {
    const params = {
      page: customerCurrentPage.value,
      pageSize: customerPageSize.value,
      query: customerSearchQuery.value,
      startDate: customerDateRange.value ? customerDateRange.value[0] : undefined,
      endDate: customerDateRange.value ? customerDateRange.value[1] : undefined
    }

    const res = await getCustomerReport(params)
    customerData.value = res.data
    customerTotal.value = res.total

    // 更新图表
    initCustomerChart(res.data.slice(0, 10))
  } catch (error) {
    console.error('Failed to fetch customer report:', error)
    ElMessage.error('获取客户报表失败')
  } finally {
    loadingCustomer.value = false
  }
}

// 初始化销售图表
const initSalesChart = (data) => {
  if (salesChart) {
    salesChart.dispose()
  }

  salesChart = echarts.init(salesChartRef.value)

  const dates = data.map(item => item.date || '')
  const amounts = data.map(item => item.amount || 0)
  const orderCounts = data.map(item => item.orderCount || 0)

  const option = {
    title: {
      text: '销售趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['销售额', '订单数'],
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额',
        position: 'left',
        axisLabel: {
          formatter: '¥{value}'
        }
      },
      {
        type: 'value',
        name: '订单数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: amounts,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '订单数',
        type: 'line',
        yAxisIndex: 1,
        data: orderCounts,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }

  salesChart.setOption(option)
}

// 初始化库存图表
const initInventoryChart = (summary) => {
  if (inventoryChart) {
    inventoryChart.dispose()
  }

  inventoryChart = echarts.init(inventoryChartRef.value)

  const option = {
    title: {
      text: '库存状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      data: ['库存不足', '库存正常', '库存过多']
    },
    series: [
      {
        name: '库存状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: summary.lowStock || 0, name: '库存不足', itemStyle: { color: '#F56C6C' } },
          { value: summary.normalStock || 0, name: '库存正常', itemStyle: { color: '#67C23A' } },
          { value: summary.highStock || 0, name: '库存过多', itemStyle: { color: '#E6A23C' } }
        ]
      }
    ]
  }

  inventoryChart.setOption(option)
}

// 初始化客户图表
const initCustomerChart = (data) => {
  if (customerChart) {
    customerChart.dispose()
  }

  customerChart = echarts.init(customerChartRef.value)

  // 按照总金额排序
  data.sort((a, b) => (b.totalAmount || 0) - (a.totalAmount || 0))

  const names = data.map(item => item.name || '')
  const amounts = data.map(item => item.totalAmount || 0)
  const orderCounts = data.map(item => item.orderCount || 0)

  const option = {
    title: {
      text: '客户销售排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['销售额', '订单数'],
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: names,
      axisLabel: {
        width: 100,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: amounts,
        itemStyle: {
          color: '#409EFF'
        },
        label: {
          show: true,
          position: 'right',
          formatter: '¥{c}'
        }
      },
      {
        name: '订单数',
        type: 'bar',
        data: orderCounts,
        itemStyle: {
          color: '#67C23A'
        },
        label: {
          show: true,
          position: 'right'
        }
      }
    ]
  }

  customerChart.setOption(option)
}

// 导出销售报表
const exportSalesReport = async () => {
  try {
    const params = {
      startDate: salesDateRange.value ? salesDateRange.value[0] : undefined,
      endDate: salesDateRange.value ? salesDateRange.value[1] : undefined,
      groupBy: salesGroupBy.value
    }

    await exportSales(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Failed to export sales report:', error)
    ElMessage.error('导出失败')
  }
}

// 导出库存报表
const exportInventoryReport = async () => {
  try {
    const params = {
      query: inventorySearchQuery.value,
      status: inventoryStatus.value
    }

    await exportInventory(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Failed to export inventory report:', error)
    ElMessage.error('导出失败')
  }
}

// 导出客户报表
const exportCustomerReport = async () => {
  try {
    const params = {
      query: customerSearchQuery.value,
      startDate: customerDateRange.value ? customerDateRange.value[0] : undefined,
      endDate: customerDateRange.value ? customerDateRange.value[1] : undefined
    }

    await exportCustomer(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Failed to export customer report:', error)
    ElMessage.error('导出失败')
  }
}

// 获取库存状态对应的标签类型
const getInventoryStatusType = (status) => {
  const statusMap = {
    '库存不足': 'danger',
    '库存正常': 'success',
    '库存过多': 'warning'
  }
  return statusMap[status] || 'info'
}

// 查看产品详情
const viewProduct = (product) => {
  router.push(`/products/${product.id}`)
}

// 查看客户详情
const viewCustomer = (customer) => {
  router.push(`/customers/${customer.id}`)
}

// 处理销售日期变化
const handleSalesDateChange = () => {
  // 日期变化时不自动查询，等用户点击查询按钮
}

// 处理销售分组方式变化
const handleSalesGroupChange = () => {
  // 分组方式变化时不自动查询，等用户点击查询按钮
}

// 处理客户日期变化
const handleCustomerDateChange = () => {
  // 日期变化时不自动查询，等用户点击查询按钮
}

// 库存分页处理
const handleInventorySizeChange = (val) => {
  inventoryPageSize.value = val
  fetchInventoryReport()
}

const handleInventoryCurrentChange = (val) => {
  inventoryCurrentPage.value = val
  fetchInventoryReport()
}

// 客户分页处理
const handleCustomerSizeChange = (val) => {
  customerPageSize.value = val
  fetchCustomerReport()
}

const handleCustomerCurrentChange = (val) => {
  customerCurrentPage.value = val
  fetchCustomerReport()
}

// 获取订单列表（收款情况报表）
const fetchOrders = async () => {
  loadingPayment.value = true
  try {
    const params = {
      page: paymentCurrentPage.value,
      pageSize: paymentPageSize.value,
      query: paymentSearchQuery.value,
      customerId: paymentCustomerId.value,
      excludeCancelled: true // 排除已取消的订单
    }

    if (paymentDateRange.value && paymentDateRange.value.length === 2) {
      params.startDate = paymentDateRange.value[0]
      params.endDate = paymentDateRange.value[1]
    }

    if (paymentStatus.value) {
      params.paymentStatus = paymentStatus.value
    }

    const res = await getOrders(params)
    orderList.value = res.data
    paymentTotal.value = res.total
  } catch (error) {
    console.error('Failed to fetch orders:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loadingPayment.value = false
  }
}

// 获取客户选项（收款情况报表）
const fetchPaymentCustomerOptions = async () => {
  try {
    const res = await getCustomers({ pageSize: 100 })
    paymentCustomerOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch customers:', error)
  }
}

// 处理收款情况搜索
const handlePaymentSearch = () => {
  paymentCurrentPage.value = 1
  fetchOrders()
}

// 处理收款情况刷新
const handlePaymentRefresh = () => {
  paymentSearchQuery.value = ''
  paymentStatus.value = ''
  paymentCustomerId.value = ''
  paymentDateRange.value = []
  paymentCurrentPage.value = 1
  fetchOrders()
}

// 处理收款情况导出
const handlePaymentExport = () => {
  ElMessage.success('导出功能待实现')
}

// 处理查看订单
const handleViewOrder = (row) => {
  router.push(`/orders/${row.id}`)
}

// 处理添加付款
const handleAddPayment = (row) => {
  paymentForm.orderId = row.id
  paymentForm.orderNumber = row.orderNumber
  paymentForm.customerName = row.customer.name
  paymentForm.unpaidAmount = row.unpaidAmount || 0
  paymentForm.amount = row.unpaidAmount || 0
  paymentForm.paymentMethod = row.paymentMethod || 'corporate'
  paymentForm.paymentDate = new Date().toISOString().split('T')[0]
  paymentForm.remarks = ''

  paymentDialogVisible.value = true
}

// 提交付款
const submitPayment = async () => {
  if (!paymentFormRef.value) return

  await paymentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await createPayment({
          orderId: paymentForm.orderId,
          amount: paymentForm.amount,
          paymentMethod: paymentForm.paymentMethod,
          paymentDate: paymentForm.paymentDate,
          remarks: paymentForm.remarks
        })

        ElMessage.success('付款记录添加成功')
        paymentDialogVisible.value = false

        // 刷新订单数据
        await fetchOrders()
      } catch (error) {
        console.error('添加付款记录失败:', error)
        ElMessage.error('添加付款记录失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 获取收款状态文本
const getPaymentStatusText = (order) => {
  if (order.totalAmount === 0) return '无需付款'
  if (order.paidAmount >= order.totalAmount) return '已付清'
  if (order.paidAmount > 0) return '部分付款'
  return '未付款'
}

// 获取收款状态标签类型
const getPaymentStatusType = (order) => {
  if (order.totalAmount === 0) return 'info'
  if (order.paidAmount >= order.totalAmount) return 'success'
  if (order.paidAmount > 0) return 'warning'
  return 'danger'
}

// 格式化日期时间为北京时间，精确到秒
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  // 使用toLocaleString设置为zh-CN区域，显示北京时间
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 格式化数字为两位小数
const formatNumber = (num) => {
  return num ? num.toFixed(2) : '0.00'
}

// 收款情况分页处理
const handlePaymentSizeChange = (val) => {
  paymentPageSize.value = val
  fetchOrders()
}

const handlePaymentCurrentChange = (val) => {
  paymentCurrentPage.value = val
  fetchOrders()
}

// 监听标签页变化
watch(activeTab, (newVal) => {
  if (newVal === 'sales' && !salesData.value.length) {
    fetchSalesReport()
  } else if (newVal === 'inventory' && !inventoryData.value.length) {
    fetchInventoryReport()
  } else if (newVal === 'customer' && !customerData.value.length) {
    fetchCustomerReport()
  } else if (newVal === 'payment' && !orderList.value.length) {
    fetchOrders()
    fetchPaymentCustomerOptions()
  }
})

// 监听窗口大小变化，重新调整图表大小
window.addEventListener('resize', () => {
  salesChart?.resize()
  inventoryChart?.resize()
  customerChart?.resize()
})

onMounted(() => {
  // 根据当前标签页加载相应的数据
  if (activeTab.value === 'sales') {
    fetchSalesReport()
  } else if (activeTab.value === 'inventory') {
    fetchInventoryReport()
  } else if (activeTab.value === 'customer') {
    fetchCustomerReport()
  } else if (activeTab.value === 'payment') {
    fetchOrders()
    fetchPaymentCustomerOptions()
  }
})
</script>

<style scoped>
.report-links {
  margin-bottom: 20px;
}

.report-tabs {
  margin-top: 20px;
}

.filter-bar {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.report-summary {
  margin: 20px 0;
}

.summary-card {
  text-align: center;
  margin-bottom: 20px;
}

.summary-card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.summary-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.text-danger {
  color: #F56C6C;
}

.text-success {
  color: #67C23A;
}

.text-warning {
  color: #E6A23C;
}

.chart-container {
  height: 400px;
  margin-bottom: 20px;
}

.chart {
  width: 100%;
  height: 100%;
}

.data-table {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  gap: 10px;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.left-actions {
  display: flex;
  gap: 10px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.unpaid-amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 12px;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  flex: 1;
  text-align: center;
}

.stats-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}
</style>
