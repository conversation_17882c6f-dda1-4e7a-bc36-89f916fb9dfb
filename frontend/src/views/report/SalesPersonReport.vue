<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>销售人员业绩报表</span>
          <div class="right-panel">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
            <el-button type="primary" @click="fetchData">查询</el-button>
            <el-button type="success" @click="exportData">导出</el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        :default-sort="{ prop: 'totalAmount', order: 'descending' }"
      >
        <el-table-column prop="salesPersonName" label="销售人员" min-width="120" />
        <el-table-column prop="customerCount" label="客户数量" min-width="100" sortable />
        <el-table-column prop="orderCount" label="订单数量" min-width="100" sortable />
        <el-table-column prop="totalAmount" label="销售总额" min-width="120" sortable>
          <template #default="scope">
            {{ formatCurrency(scope.row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgOrderAmount" label="平均订单金额" min-width="120" sortable>
          <template #default="scope">
            {{ formatCurrency(scope.row.avgOrderAmount) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:page-size="pageSize"
          v-model:current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getSalesPersonReport, exportSalesPersonReport } from '@/api/report'

const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const dateRange = ref([])

// 格式化货币
const formatCurrency = (value) => {
  if (value === null || value === undefined) return '¥0.00'
  return '¥' + value.toFixed(2)
}

// 处理日期变化
const handleDateChange = (val) => {
  dateRange.value = val
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    console.log('开始获取销售人员业绩报表数据')
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    console.log('请求参数:', params)
    console.log('API URL:', '/reports/salespersons')

    const response = await getSalesPersonReport(params)
    console.log('获取销售人员业绩报表响应:', response)

    tableData.value = response.data
    total.value = response.total

    // 客户端分页
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    tableData.value = response.data.slice(start, end)
    total.value = response.data.length

    console.log('处理后的表格数据:', tableData.value)
  } catch (error) {
    console.error('获取销售人员业绩报表失败:', error)
    console.error('错误详情:', error.response ? error.response.data : '无响应数据')
    ElMessage.error('获取销售人员业绩报表失败')
  } finally {
    loading.value = false
  }
}

// 导出数据
const exportData = async () => {
  try {
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    await exportSalesPersonReport(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Failed to export sales person report:', error)
    ElMessage.error('导出销售人员业绩报表失败')
  }
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

onMounted(() => {
  // 设置默认日期范围为最近30天
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 30)

  dateRange.value = [
    start.toISOString().split('T')[0],
    end.toISOString().split('T')[0]
  ]

  fetchData()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-panel {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
