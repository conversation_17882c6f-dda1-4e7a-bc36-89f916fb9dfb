<template>
  <div class="page-container">
    <div class="page-title">系统设置</div>

    <el-tabs v-model="activeTab" class="settings-tabs">
      <el-tab-pane label="个人信息" name="profile">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
              <el-button type="primary" size="small" @click="editProfile">编辑</el-button>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
            <el-descriptions-item label="姓名">{{ userInfo.name }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ userInfo.email }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{ userInfo.phone }}</el-descriptions-item>
            <el-descriptions-item label="角色">{{ userInfo.role }}</el-descriptions-item>
            <el-descriptions-item label="上次登录">{{ formatDateTime(userInfo.lastLogin) }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>修改密码</span>
            </div>
          </template>

          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="120px"
          >
            <el-form-item label="当前密码" prop="oldPassword">
              <el-input
                v-model="passwordForm.oldPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="loading">修改密码</el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="系统参数" name="system">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>系统参数设置</span>
              <el-button type="primary" size="small" @click="saveSystemSettings" :loading="loading">保存设置</el-button>
            </div>
          </template>

          <el-form
            ref="systemFormRef"
            :model="systemSettings"
            label-width="180px"
          >
            <el-form-item label="系统名称">
              <el-input v-model="systemSettings.systemName" />
            </el-form-item>
            <el-form-item label="公司名称">
              <el-input v-model="systemSettings.companyName" />
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input v-model="systemSettings.contactPhone" />
            </el-form-item>
            <el-form-item label="联系邮箱">
              <el-input v-model="systemSettings.contactEmail" />
            </el-form-item>
            <el-form-item label="地址">
              <el-input v-model="systemSettings.address" />
            </el-form-item>
            <el-form-item label="订单编号前缀">
              <el-input v-model="systemSettings.orderPrefix" />
            </el-form-item>
            <el-form-item label="产品编号前缀">
              <el-input v-model="systemSettings.productPrefix" />
            </el-form-item>
            <el-form-item label="客户编号前缀">
              <el-input v-model="systemSettings.customerPrefix" />
            </el-form-item>
            <el-form-item label="数据分页大小">
              <el-input-number v-model="systemSettings.pageSize" :min="5" :max="100" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="用户管理" name="users" v-if="isAdmin">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>用户列表</span>
              <el-button type="primary" size="small" @click="addUser">添加用户</el-button>
            </div>
          </template>

          <el-table
            v-loading="loadingUsers"
            :data="userList"
            border
            class="data-table"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="email" label="邮箱" min-width="180" />
            <el-table-column prop="phone" label="电话" width="150" />
            <el-table-column prop="role" label="角色" width="100" />
            <el-table-column prop="isActive" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
                  {{ scope.row.isActive ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastLogin" label="上次登录" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.lastLogin) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" @click="editUser(scope.row)">编辑</el-button>
                <el-button
                  size="small"
                  :type="scope.row.isActive ? 'danger' : 'success'"
                  @click="toggleUserStatus(scope.row)"
                >
                  {{ scope.row.isActive ? '禁用' : '启用' }}
                </el-button>
                <el-button size="small" type="warning" @click="resetUserPassword(scope.row)">重置密码</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="数据备份" name="backup" v-if="isAdmin">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>数据备份</span>
              <el-button type="primary" size="small" @click="createBackup" :loading="backupLoading">创建备份</el-button>
            </div>
          </template>

          <el-table
            v-loading="loadingBackups"
            :data="backupList"
            border
            class="data-table"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="filename" label="文件名" min-width="200" />
            <el-table-column prop="size" label="大小" width="120">
              <template #default="scope">
                {{ formatFileSize(scope.row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column prop="createdBy" label="创建人" width="120" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" @click="downloadBackup(scope.row)">下载</el-button>
                <el-button size="small" type="danger" @click="deleteBackup(scope.row)">删除</el-button>
                <el-button size="small" type="warning" @click="restoreBackup(scope.row)">恢复</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="userDialogType === 'add' ? '添加用户' : '编辑用户'"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="userDialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="userForm.phone" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="操作员" value="operator" />
            <el-option label="查看者" value="viewer" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="userForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="userDialogType === 'add'">
          <el-input v-model="userForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="userDialogType === 'add'">
          <el-input v-model="userForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUser" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '../store/user'
import { getUserInfo, changePassword as changeUserPassword } from '../api/auth'
import { getSystemSettings, updateSystemSettings } from '../api/settings'
import { getUsers, createUser, updateUser, toggleUserActive, resetPassword } from '../api/user'
import { getBackups, createBackup as createBackupApi, downloadBackup as downloadBackupApi, deleteBackup as deleteBackupApi, restoreBackup as restoreBackupApi } from '../api/backup'

const userStore = useUserStore()
const activeTab = ref('profile')
const loading = ref(false)
const backupLoading = ref(false)
const loadingUsers = ref(false)
const loadingBackups = ref(false)
const userDialogVisible = ref(false)
const userDialogType = ref('add') // 'add' or 'edit'
const passwordFormRef = ref(null)
const systemFormRef = ref(null)
const userFormRef = ref(null)

// 用户信息
const userInfo = ref({
  username: '',
  name: '',
  email: '',
  phone: '',
  role: '',
  lastLogin: null
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 系统设置
const systemSettings = reactive({
  systemName: '仓库管理系统',
  companyName: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  orderPrefix: 'ORD',
  productPrefix: 'PRD',
  customerPrefix: 'CUS',
  pageSize: 10
})

// 用户列表
const userList = ref([])

// 备份列表
const backupList = ref([])

// 用户表单
const userForm = reactive({
  id: '',
  username: '',
  name: '',
  email: '',
  phone: '',
  role: 'operator',
  isActive: true,
  password: '',
  confirmPassword: ''
})

// 密码表单验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 用户表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur', when: () => userDialogType.value === 'add' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur', when: () => userDialogType.value === 'add' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur', when: () => userDialogType.value === 'add' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
      when: () => userDialogType.value === 'add'
    }
  ]
}

// 是否为管理员
const isAdmin = computed(() => {
  return userInfo.value.role === 'admin'
})

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getUserInfo()
    userInfo.value = res.data
  } catch (error) {
    console.error('Failed to fetch user info:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 获取系统设置
const fetchSystemSettings = async () => {
  try {
    const res = await getSystemSettings()
    Object.assign(systemSettings, res.data)
  } catch (error) {
    console.error('Failed to fetch system settings:', error)
    ElMessage.error('获取系统设置失败')
  }
}

// 获取用户列表
const fetchUsers = async () => {
  loadingUsers.value = true
  try {
    const res = await getUsers()
    userList.value = res.data
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loadingUsers.value = false
  }
}

// 获取备份列表
const fetchBackups = async () => {
  loadingBackups.value = true
  try {
    const res = await getBackups()
    backupList.value = res.data
  } catch (error) {
    console.error('Failed to fetch backups:', error)
    ElMessage.error('获取备份列表失败')
  } finally {
    loadingBackups.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await changeUserPassword(passwordForm.oldPassword, passwordForm.newPassword)
        ElMessage.success('密码修改成功')
        resetPasswordForm()
      } catch (error) {
        console.error('Failed to change password:', error)
        ElMessage.error('密码修改失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置密码表单
const resetPasswordForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

// 保存系统设置
const saveSystemSettings = async () => {
  loading.value = true
  try {
    await updateSystemSettings(systemSettings)
    ElMessage.success('系统设置保存成功')
  } catch (error) {
    console.error('Failed to save system settings:', error)
    ElMessage.error('系统设置保存失败')
  } finally {
    loading.value = false
  }
}

// 编辑个人信息
const editProfile = () => {
  ElMessage.info('个人信息编辑功能待实现')
}

// 添加用户
const addUser = () => {
  userDialogType.value = 'add'
  userForm.id = ''
  userForm.username = ''
  userForm.name = ''
  userForm.email = ''
  userForm.phone = ''
  userForm.role = 'operator'
  userForm.isActive = true
  userForm.password = ''
  userForm.confirmPassword = ''
  userDialogVisible.value = true
}

// 编辑用户
const editUser = (user) => {
  userDialogType.value = 'edit'
  userForm.id = user.id
  userForm.username = user.username
  userForm.name = user.name
  userForm.email = user.email
  userForm.phone = user.phone
  userForm.role = user.role
  userForm.isActive = user.isActive
  userDialogVisible.value = true
}

// 保存用户
const saveUser = async () => {
  if (!userFormRef.value) return

  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        if (userDialogType.value === 'add') {
          await createUser(userForm)
          ElMessage.success('用户创建成功')
        } else {
          await updateUser(userForm.id, userForm)
          ElMessage.success('用户更新成功')
        }
        userDialogVisible.value = false
        fetchUsers()
      } catch (error) {
        console.error('Failed to save user:', error)
        ElMessage.error('保存用户失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 切换用户状态
const toggleUserStatus = async (user) => {
  try {
    await toggleUserActive(user.id, !user.isActive)
    ElMessage.success(`用户${user.isActive ? '禁用' : '启用'}成功`)
    fetchUsers()
  } catch (error) {
    console.error('Failed to toggle user status:', error)
    ElMessage.error('操作失败')
  }
}

// 重置用户密码
const resetUserPassword = async (user) => {
  ElMessageBox.confirm(
    `确定要重置用户 "${user.username}" 的密码吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await resetPassword(user.id)
      ElMessage.success(`密码重置成功，新密码: ${res.data.password}`)
    } catch (error) {
      console.error('Failed to reset password:', error)
      ElMessage.error('密码重置失败')
    }
  }).catch(() => {})
}

// 创建备份
const createBackup = async () => {
  backupLoading.value = true
  try {
    await createBackupApi()
    ElMessage.success('备份创建成功')
    fetchBackups()
  } catch (error) {
    console.error('Failed to create backup:', error)
    ElMessage.error('备份创建失败')
  } finally {
    backupLoading.value = false
  }
}

// 下载备份
const downloadBackup = async (backup) => {
  try {
    await downloadBackupApi(backup.id)
    ElMessage.success('备份下载成功')
  } catch (error) {
    console.error('Failed to download backup:', error)
    ElMessage.error('备份下载失败')
  }
}

// 删除备份
const deleteBackup = async (backup) => {
  ElMessageBox.confirm(
    `确定要删除备份 "${backup.filename}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteBackupApi(backup.id)
      ElMessage.success('备份删除成功')
      fetchBackups()
    } catch (error) {
      console.error('Failed to delete backup:', error)
      ElMessage.error('备份删除失败')
    }
  }).catch(() => {})
}

// 恢复备份
const restoreBackup = async (backup) => {
  ElMessageBox.confirm(
    `确定要恢复备份 "${backup.filename}" 吗？这将覆盖当前所有数据！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await restoreBackupApi(backup.id)
      ElMessage.success('备份恢复成功')
    } catch (error) {
      console.error('Failed to restore backup:', error)
      ElMessage.error('备份恢复失败')
    }
  }).catch(() => {})
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听标签页变化
watch(activeTab, (newVal) => {
  if (newVal === 'users' && isAdmin.value && !userList.value.length) {
    fetchUsers()
  } else if (newVal === 'backup' && isAdmin.value && !backupList.value.length) {
    fetchBackups()
  }
})

onMounted(() => {
  fetchUserInfo()
  fetchSystemSettings()
})
</script>

<style scoped>
.settings-tabs {
  margin-top: 20px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-table {
  margin-bottom: 20px;
}
</style>
