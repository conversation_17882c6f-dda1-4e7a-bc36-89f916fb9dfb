<template>
  <div class="print-page-container">
    <div class="print-controls" v-if="!isPrinting">
      <div class="page-header">
        <div class="page-title">出库单打印</div>
        <div class="page-actions">
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>返回
          </el-button>
          <el-button type="primary" @click="handlePrint">
            <el-icon><Printer /></el-icon>打印
          </el-button>
        </div>
      </div>
    </div>

    <div class="print-content" ref="printContainer">
      <div class="shipment-form">
        <div class="company-title">{{ order.company || '具体公司' }}</div>
        <div class="form-title">出库单</div>

        <div class="form-header">
          <div class="form-row">
            <div class="form-field">
              <span class="field-label">客户名称：</span>
              <span class="field-value">{{ order.customer?.name || '无' }}</span>
            </div>
            <div class="form-field">
              <span class="field-label">联系人姓名/电话：</span>
              <span class="field-value">{{ order.contactPerson || '无' }} / {{ order.contactPhone || '无' }}</span>
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <span class="field-label">客户地址：</span>
              <span class="field-value">{{ order.deliveryAddress || '无' }}</span>
            </div>
            <div class="form-field">
              <span class="field-label">出库日期：</span>
              <span class="field-value">{{ formatDateTime(order.outboundDate || new Date()) }}</span>
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <span class="field-label">订单编号：</span>
              <span class="field-value">{{ order.orderNumber || '无' }}</span>
            </div>
          </div>
        </div>

        <table class="shipment-table">
          <thead>
            <tr>
              <th class="col-seq">序号</th>
              <th class="col-name">商品名称</th>
              <th class="col-spec">规格</th>
              <th class="col-unit">单位</th>
              <th class="col-quantity">数量</th>
              <th class="col-remark">备注</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in orderItems" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.product?.name }}</td>
              <td>{{ item.product?.specification }}</td>
              <td>{{ item.product?.unit }}</td>
              <td>{{ item.quantity }}</td>
              <td></td>
            </tr>
            <!-- 添加空行，确保表格根据实际产品数量显示 -->
            <tr v-for="i in getEmptyRows()" :key="`empty-${i}`">
              <td>{{ orderItems.length + i }}</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>

        <div class="form-footer">
          <p class="warning-text">如有质量问题或者型号，数量不对请勿损坏。一经损坏，恕不接受退换货，请知悉！</p>
          <p class="address-text">发货地址：四川省宜宾市翠屏区宜宾东方雨虹(宜宾)仓储批发中心</p>
          <div class="signature-row">
            <div class="signature-field">
              <span class="field-label">业务员/发货人：</span>
              <span class="field-value">邓雄丽</span>
            </div>
            <div class="signature-field">
              <span class="field-label">联系电话：</span>
              <span class="field-value">18408241074</span>
            </div>
          </div>
          <div class="signature-row">
            <div class="signature-field">
              <span class="field-label">收货人签字：</span>
              <span class="field-value"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Printer } from '@element-plus/icons-vue'
import { getOrderById } from '../../api/order'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const order = ref({})
const orderItems = ref([])
const isPrinting = ref(false)
const printContainer = ref(null)

// 获取订单详情
const fetchOrderDetail = async () => {
  loading.value = true
  try {
    const orderId = route.params.id
    const res = await getOrderById(orderId)

    if (res && res.id) {
      order.value = res
      orderItems.value = res.items || []
    } else if (res && res.data) {
      order.value = res.data
      orderItems.value = res.data.items || []
    } else {
      ElMessage.warning('获取订单数据失败')
      order.value = {}
      orderItems.value = []
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期时间为北京时间，精确到秒
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  // 使用toLocaleString设置为zh-CN区域，显示北京时间
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 计算需要添加的空行数，确保表格根据实际产品数量显示
const getEmptyRows = () => {
  const currentRows = orderItems.value.length
  // 如果当前行数小于10，则补充到10行
  return currentRows < 10 ? 10 - currentRows : 0
}

// 处理打印
const handlePrint = () => {
  isPrinting.value = true
  setTimeout(() => {
    window.print()
    isPrinting.value = false
  }, 300)
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.print-page-container {
  padding: 20px;
}

.print-controls {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.shipment-form {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ddd;
}

.company-title {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 15px;
  width: 100%;
}

.form-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.form-header {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  margin-bottom: 10px;
}

.form-field {
  flex: 1;
  display: flex;
}

.field-label {
  font-weight: bold;
  margin-right: 5px;
  min-width: 120px;
}

.shipment-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.shipment-table th,
.shipment-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

.shipment-table th {
  background-color: #f2f2f2;
}

.col-seq {
  width: 50px;
}

.col-name {
  width: 200px;
}

.col-spec {
  width: 120px;
}

.col-unit {
  width: 80px;
}

.col-quantity {
  width: 80px;
}

.col-remark {
  width: 120px;
}

.form-footer {
  margin-top: 30px;
}

.warning-text {
  font-weight: bold;
  margin-bottom: 10px;
}

.address-text {
  margin-bottom: 20px;
}

.signature-row {
  display: flex;
  margin-bottom: 10px;
}

.signature-field {
  flex: 1;
  display: flex;
}

@media print {
  .print-controls {
    display: none;
  }

  body {
    margin: 0;
    padding: 0;
  }

  .shipment-form {
    border: none;
    padding: 0;
  }

  /* 隐藏浏览器默认的页眉页脚 */
  @page {
    margin: 0;
    size: auto;
  }
}
</style>
