<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">订单详情</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
        <template v-if="isEditMode">
          <el-button type="primary" @click="handlePrint">
            <el-icon><Printer /></el-icon>打印订单
          </el-button>
          <el-button type="primary" @click="handlePrintShipment">
            <el-icon><Printer /></el-icon>打印出库单
          </el-button>
          <el-button type="success" @click="handleShip" v-if="Number(order.status) === 2 || Number(order.status) === 3">
            <el-icon><Van /></el-icon>发货
          </el-button>
          <el-button type="success" @click="handleConfirmDelivery" v-if="order.isShipped && !order.isDelivered">
            <el-icon><Check /></el-icon>确认到货
          </el-button>
          <el-button type="primary" @click="handleConfirmOutbound" v-if="!order.isOutboundConfirmed">
            <el-icon><Box /></el-icon>确认出库
          </el-button>
          <el-button type="primary" @click="handleAddPayment">
            <el-icon><Money /></el-icon>添加付款
          </el-button>
          <el-button type="primary" @click="handleEdit" :disabled="Number(order.status) === 6 || Number(order.status) === 7">
            <el-icon><Edit /></el-icon>编辑订单
          </el-button>
          <el-button type="danger" @click="handleCancel" v-if="Number(order.status) === 1 || Number(order.status) === 2">
            <el-icon><Close /></el-icon>取消订单
          </el-button>
        </template>
        <template v-else>
          <el-button type="primary" @click="handlePrintShipment">
            <el-icon><Printer /></el-icon>打印出库单
          </el-button>
        </template>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>订单信息</span>
          <el-tag :type="getOrderStatusType(order.status)">
            {{ order.statusText || getStatusText(order.status) }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单ID">{{ order.id }}</el-descriptions-item>
        <el-descriptions-item label="订单编号">{{ order.orderNumber }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">¥{{ order.totalAmount ? order.totalAmount.toFixed(2) : '0.00' }}</el-descriptions-item>
        <el-descriptions-item label="已付金额">¥{{ order.paidAmount ? order.paidAmount.toFixed(2) : '0.00' }}</el-descriptions-item>
        <el-descriptions-item label="欠款金额">
          <span :class="{ 'unpaid-amount': order.unpaidAmount > 0 }">
            ¥{{ order.unpaidAmount ? order.unpaidAmount.toFixed(2) : '0.00' }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ order.paymentMethod || '未指定' }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ order.statusText || getStatusText(order.status) }}</el-descriptions-item>
        <el-descriptions-item label="是否需要发票">{{ order.needInvoice ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="是否已发货">
          <el-tag :type="order.isShipped ? 'success' : 'info'">
            {{ order.isShipped ? '已发货' : '未发货' }}
          </el-tag>
          <span v-if="order.isShipped && order.shippedDate">
            ({{ formatDateTime(order.shippedDate) }})
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="是否已到货">
          <el-tag :type="order.isDelivered ? 'success' : 'info'">
            {{ order.isDelivered ? '已到货' : '未到货' }}
          </el-tag>
          <span v-if="order.isDelivered && order.deliveredDate">
            ({{ formatDateTime(order.deliveredDate) }})
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="是否已出库">
          <el-tag :type="order.isOutboundConfirmed ? 'success' : 'info'">
            {{ order.isOutboundConfirmed ? '已出库' : '未出库' }}
          </el-tag>
          <span v-if="order.isOutboundConfirmed && order.outboundDate">
            ({{ formatDateTime(order.outboundDate) }})
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="订单日期">{{ formatDateTime(order.orderDate) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(order.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDateTime(order.updatedAt) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ order.remarks || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>客户信息</span>
          <el-button v-if="isEditMode" type="primary" size="small" @click="viewCustomer">查看客户详情</el-button>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="客户ID">{{ order.customer?.id || order.customerId }}</el-descriptions-item>
        <el-descriptions-item label="客户编码">{{ order.customer?.code }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ order.customer?.name }}</el-descriptions-item>
        <el-descriptions-item label="客户类型">{{ order.customer?.type }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ order.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ order.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ order.deliveryAddress || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>订单明细</span>
        </div>
      </template>

      <el-table :data="orderItems" border style="width: 100%">
        <el-table-column type="index" label="#" width="50" />
        <el-table-column label="产品编码" width="120">
          <template #default="scope">
            {{ scope.row.product?.code }}
          </template>
        </el-table-column>
        <el-table-column label="产品名称" min-width="150">
          <template #default="scope">
            {{ scope.row.product?.name }}
          </template>
        </el-table-column>
        <el-table-column label="规格" width="120">
          <template #default="scope">
            {{ scope.row.product?.specification }}
          </template>
        </el-table-column>
        <el-table-column label="单位" width="80">
          <template #default="scope">
            {{ scope.row.product?.unit }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column label="单价" width="100">
          <template #default="scope">
            ¥{{ scope.row.unitPrice ? scope.row.unitPrice.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column label="金额" width="120">
          <template #default="scope">
            ¥{{ scope.row.amount ? scope.row.amount.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column v-if="isEditMode" label="操作" width="240">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" type="primary" @click="viewProduct(scope.row)">查看</el-button>
              <el-button size="small" type="warning" @click="handleEditItem(scope.row, scope.$index)" :disabled="Number(order.status) === 6 || Number(order.status) === 7">编辑</el-button>
              <el-button size="small" type="danger" @click="handleRemoveItem(scope.$index)" :disabled="Number(order.status) === 6 || Number(order.status) === 7">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer" v-if="isEditMode">
        <el-button type="primary" @click="handleAddItem" :disabled="Number(order.status) === 6 || Number(order.status) === 7">
          <el-icon><Plus /></el-icon>添加产品
        </el-button>
      </div>

      <div class="order-summary">
        <div class="summary-item">
          <span>商品总数:</span>
          <span>{{ getTotalQuantity() }} 件</span>
        </div>
        <div class="summary-item">
          <span>商品总金额:</span>
          <span class="amount">¥{{ getTotalAmount() ? getTotalAmount().toFixed(2) : '0.00' }}</span>
        </div>
        <div class="summary-item" v-if="order.totalAmount && getTotalAmount() && Math.abs(order.totalAmount - getTotalAmount()) > 0.01">
          <span>订单金额:</span>
          <span class="amount">¥{{ order.totalAmount.toFixed(2) }}</span>
          <el-tooltip content="订单金额与明细总金额不一致，可能存在折扣或其他调整" placement="top">
            <el-icon><Warning /></el-icon>
          </el-tooltip>
        </div>
      </div>
    </el-card>

    <!-- 支付记录 -->
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>支付记录</span>
          <el-button v-if="isEditMode" type="primary" size="small" @click="handleAddPayment">添加付款</el-button>
        </div>
      </template>

      <el-table :data="order.payments || []" border style="width: 100%">
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="amount" label="支付金额" width="120">
          <template #default="scope">
            ¥{{ scope.row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethodText" label="支付方式" width="120" />
        <el-table-column prop="paymentDate" label="支付日期" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.paymentDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" min-width="150" />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>

      <div v-if="!order.payments || order.payments.length === 0" class="empty-data">
        <el-empty description="暂无支付记录" />
      </div>
    </el-card>

    <!-- 物流信息 -->
    <el-card class="detail-card" v-if="Number(order.status) >= 4">
      <template #header>
        <div class="card-header">
          <span>物流信息</span>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="物流公司">{{ order.shipmentInfo?.company || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="物流单号">{{ order.shipmentInfo?.trackingNumber || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="发货时间">{{ formatDateTime(order.shipmentInfo?.shipmentTime) }}</el-descriptions-item>
        <el-descriptions-item label="发货人">{{ order.shipmentInfo?.shipper || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="收货地址" :span="2">{{ order.shipmentInfo?.address || order.deliveryAddress || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ order.shipmentInfo?.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 发货对话框 -->
    <el-dialog v-model="shipDialogVisible" title="订单发货" width="500px">
      <el-form :model="shipForm" :rules="shipRules" ref="shipFormRef" label-width="100px">
        <el-form-item label="物流公司" prop="company">
          <el-input v-model="shipForm.company" placeholder="请输入物流公司" />
        </el-form-item>
        <el-form-item label="物流单号" prop="trackingNumber">
          <el-input v-model="shipForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="发货人" prop="shipper">
          <el-input v-model="shipForm.shipper" placeholder="请输入发货人" />
        </el-form-item>
        <el-form-item label="收货地址" prop="address">
          <el-input v-model="shipForm.address" placeholder="请输入收货地址" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="shipForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitShipment" :loading="submitting">确认发货</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加付款对话框 -->
    <el-dialog v-model="paymentDialogVisible" title="添加付款记录" width="500px">
      <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="100px">
        <el-form-item label="支付金额" prop="amount">
          <el-input-number v-model="paymentForm.amount" :precision="2" :min="0.01" :max="order.unpaidAmount || 999999" style="width: 100%" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="paymentForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="对公" value="corporate" />
            <el-option label="对私" value="personal" />
            <el-option label="微信" value="wechat" />
            <el-option label="支付宝" value="alipay" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付日期" prop="paymentDate">
          <el-date-picker v-model="paymentForm.paymentDate" type="date" placeholder="选择日期" style="width: 100%" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="paymentForm.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="paymentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPayment" :loading="submitting">确认添加</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑订单对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑订单" width="600px">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="editForm.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="editForm.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="收货地址" prop="deliveryAddress">
          <el-input v-model="editForm.deliveryAddress" placeholder="请输入收货地址" />
        </el-form-item>
        <el-form-item label="交付日期" prop="deliveryDate">
          <el-date-picker v-model="editForm.deliveryDate" type="date" placeholder="选择日期" style="width: 100%" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="是否需要发票" prop="needInvoice">
          <el-switch v-model="editForm.needInvoice" />
        </el-form-item>
        <el-form-item label="公司" prop="company" v-if="editForm.needInvoice">
          <el-input v-model="editForm.company" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="editForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="对公" value="corporate" />
            <el-option label="对私" value="personal" />
            <el-option label="微信" value="wechat" />
            <el-option label="支付宝" value="alipay" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="editForm.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="submitting">保存修改</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑产品对话框 -->
    <el-dialog v-model="editItemDialogVisible" title="编辑产品" width="500px">
      <el-form :model="editItemForm" :rules="editItemRules" ref="editItemFormRef" label-width="100px">
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="editItemForm.productName" disabled />
        </el-form-item>
        <el-form-item label="规格" prop="specification">
          <el-input v-model="editItemForm.specification" disabled />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="editItemForm.unit" disabled />
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input-number v-model="editItemForm.quantity" :min="1" :precision="0" style="width: 100%" @change="calculateItemAmount" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input-number v-model="editItemForm.unitPrice" :min="0.01" :precision="2" style="width: 100%" @change="calculateItemAmount" />
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input-number v-model="editItemForm.amount" :min="0.01" :precision="2" style="width: 100%" disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditItem" :loading="submitting">保存修改</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加产品对话框 -->
    <el-dialog v-model="addItemDialogVisible" title="添加产品" width="600px">
      <el-form :model="addItemForm" :rules="addItemRules" ref="addItemFormRef" label-width="100px">
        <el-form-item label="产品" prop="productId">
          <el-select
            v-model="addItemForm.productId"
            filterable
            remote
            reserve-keyword
            placeholder="请输入产品名称或编码搜索"
            :remote-method="searchProductItems"
            :loading="searchLoading"
            style="width: 100%"
            @change="handleProductSelected"
          >
            <el-option
              v-for="item in productOptions"
              :key="item.id"
              :label="item.name + ' (' + item.code + ')'"
              :value="item.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ item.name }}</span>
                <span style="color: #999; font-size: 12px">{{ item.code }}</span>
              </div>
              <div style="font-size: 12px; color: #666">
                规格: {{ item.specification }} | 单位: {{ item.unit }} | 价格: ¥{{ item.price }} |
                <span :style="{ color: item.currentStock > 0 ? '#67C23A' : '#F56C6C' }">
                  库存: {{ item.currentStock || 0 }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="addItemForm.quantity"
            :min="1"
            :precision="0"
            style="width: 100%"
            @change="calculateAddItemAmount"
          />
          <div v-if="addItemForm.productId" style="font-size: 12px; margin-top: 5px;" :style="{ color: getSelectedProductStock() <= 0 ? '#F56C6C' : '#67C23A' }">
            当前库存: {{ getSelectedProductStock() }}{{ getSelectedProductStock() <= 0 ? ' (注意: 库存不足)' : '' }}
          </div>
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input-number v-model="addItemForm.unitPrice" :min="0.01" :precision="2" style="width: 100%" @change="calculateAddItemAmount" />
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input-number v-model="addItemForm.amount" :min="0.01" :precision="2" style="width: 100%" disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddItem" :loading="submitting">添加产品</el-button>
        </span>
      </template>
    </el-dialog>
  </div>


</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Printer, Warning, Van, Check, Box, Money, Edit, Close, Plus } from '@element-plus/icons-vue'
import { getOrderById, shipOrder, cancelOrder, confirmDelivery, confirmOutbound, updateOrder, createPayment } from '../../api/order'
import { getProducts, getProductInventory } from '../../api/product'

const router = useRouter()
const route = useRoute()
const loading = ref(true)
const submitting = ref(false)
const searchLoading = ref(false)
const shipDialogVisible = ref(false)
const paymentDialogVisible = ref(false)
const editDialogVisible = ref(false)
const editItemDialogVisible = ref(false)
const addItemDialogVisible = ref(false)
const shipFormRef = ref(null)
const paymentFormRef = ref(null)
const editFormRef = ref(null)
const editItemFormRef = ref(null)
const addItemFormRef = ref(null)
const productOptions = ref([])
const currentEditingItemIndex = ref(-1)

// 根据URL参数判断是否为编辑模式
const isEditMode = computed(() => {
  return route.query.edit === 'true'
})
const order = ref({
  id: '',
  orderNumber: '',
  customerId: '',
  customer: {
    id: '',
    code: '',
    name: '',
    type: ''
  },
  totalAmount: 0,
  paidAmount: 0,
  unpaidAmount: 0,
  status: 0,
  statusText: '',
  orderDate: '',
  deliveryDate: null,
  deliveryAddress: '',
  contactPerson: '',
  contactPhone: '',
  needInvoice: false,
  remarks: '',
  isShipped: false,
  shippedDate: null,
  isDelivered: false,
  deliveredDate: null,
  isOutboundConfirmed: false,
  outboundDate: null,
  paymentMethod: '',
  createdAt: '',
  updatedAt: '',
  shipmentInfo: null,
  items: [],
  payments: []
})
const orderItems = ref([])

// 发货表单
const shipForm = reactive({
  company: '',
  trackingNumber: '',
  shipper: '',
  address: '',
  remark: ''
})

// 支付表单
const paymentForm = reactive({
  orderId: '',
  amount: 0,
  paymentMethod: '',
  paymentDate: '',
  remarks: ''
})

// 编辑表单
const editForm = reactive({
  contactPerson: '',
  contactPhone: '',
  deliveryAddress: '',
  deliveryDate: '',
  needInvoice: false,
  company: '',
  paymentMethod: '',
  remarks: ''
})

// 编辑产品表单
const editItemForm = reactive({
  id: '',
  productId: '',
  productName: '',
  specification: '',
  unit: '',
  quantity: 1,
  unitPrice: 0,
  amount: 0
})

// 添加产品表单
const addItemForm = reactive({
  productId: '',
  quantity: 1,
  unitPrice: 0,
  amount: 0
})

// 表单验证规则
const shipRules = {
  company: [
    { required: true, message: '请输入物流公司', trigger: 'blur' }
  ],
  trackingNumber: [
    { required: true, message: '请输入物流单号', trigger: 'blur' }
  ],
  shipper: [
    { required: true, message: '请输入发货人', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入收货地址', trigger: 'blur' }
  ]
}

// 支付表单验证规则
const paymentRules = {
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  paymentDate: [
    { required: true, message: '请选择支付日期', trigger: 'change' }
  ]
}

// 编辑表单验证规则
const editRules = {
  contactPerson: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  deliveryAddress: [
    { required: true, message: '请输入收货地址', trigger: 'blur' }
  ]
}

// 编辑产品验证规则
const editItemRules = {
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'blur' }
  ]
}

// 添加产品验证规则
const addItemRules = {
  productId: [
    { required: true, message: '请选择产品', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'blur' }
  ]
}

// 获取订单详情
const fetchOrderDetail = async () => {
  loading.value = true
  try {
    const orderId = route.params.id
    console.log('正在获取订单详情，ID:', orderId)

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('请求超时')), 10000)
    );

    const fetchPromise = getOrderById(orderId);

    // 使用Promise.race来处理可能的超时
    const res = await Promise.race([fetchPromise, timeoutPromise]);

    console.log('订单详情API响应:', res)

    // 打印完整的响应数据结构
    console.log('API响应数据结构:', JSON.stringify(res, null, 2))

    // 安全地设置数据 - 检查API返回的数据结构
    // 如果res本身就是订单对象
    if (res && res.id && res.orderNumber) {
      order.value = res;
      orderItems.value = res.items || [];
    }
    // 如果res包含data字段
    else if (res && res.data) {
      order.value = res.data;
      orderItems.value = res.data.items || [];
    }
    // 如果都不是，使用空对象
    else {
      console.warn('API返回的数据结构不符合预期');
      order.value = {};
      orderItems.value = [];
    }

    console.log('订单数据:', order.value)
    console.log('订单明细:', orderItems.value)

    // 检查关键字段
    if (!order.value.id) {
      console.warn('警告: 订单ID为空')
    }

    if (!order.value.orderNumber) {
      console.warn('警告: 订单编号为空')
    }

    if (typeof order.value.status !== 'number') {
      console.warn('警告: 订单状态不是数字类型', order.value.status)
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    console.error('错误详情:', error.message)
    if (error.response) {
      console.error('错误响应:', error.response)
    }
    ElMessage.error(`获取订单详情失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 格式化日期时间为北京时间，精确到秒
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  // 使用toLocaleString设置为zh-CN区域，显示北京时间
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 查看客户详情
const viewCustomer = () => {
  const customerId = order.value.customer?.id || order.value.customerId
  if (customerId) {
    router.push(`/customers/${customerId}`)
  } else {
    ElMessage.warning('无法获取客户ID')
  }
}

// 查看产品详情
const viewProduct = (item) => {
  if (item.productId || item.product?.id) {
    router.push(`/products/${item.productId || item.product.id}`)
  } else {
    ElMessage.warning('无法获取产品ID')
  }
}

// 处理发货
const handleShip = () => {
  // 状态2(已确认)和3(处理中)可以发货
  const status = Number(order.value.status)
  if (status !== 2 && status !== 3) {
    ElMessage.warning('当前订单状态不允许发货')
    return
  }

  // 预填地址
  shipForm.address = order.value.deliveryAddress || ''
  shipDialogVisible.value = true
}

// 提交发货
const submitShipment = async () => {
  if (!shipFormRef.value) return

  await shipFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        console.log('提交发货数据:', {
          orderId: order.value.id,
          shipmentData: shipForm
        })

        await shipOrder(order.value.id, {
          company: shipForm.company,
          trackingNumber: shipForm.trackingNumber,
          shipper: shipForm.shipper,
          address: shipForm.address,
          remark: shipForm.remark,
          shipmentTime: new Date().toISOString()
        })

        ElMessage.success('发货成功')
        shipDialogVisible.value = false

        // 刷新订单数据
        await fetchOrderDetail()
      } catch (error) {
        console.error('Failed to ship order:', error)
        ElMessage.error('发货失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 处理确认到货 - 已移至下方使用async/await风格实现

// 处理确认出库 - 已移至下方使用async/await风格实现

// 处理添加付款
const handleAddPayment = () => {
  if (order.value.unpaidAmount <= 0) {
    ElMessage.warning('该订单已全额支付，无需添加付款')
    return
  }

  // 初始化付款表单
  paymentForm.orderId = order.value.id
  paymentForm.amount = order.value.unpaidAmount
  paymentForm.paymentMethod = order.value.paymentMethod || 'corporate'
  paymentForm.paymentDate = new Date().toISOString().split('T')[0]
  paymentForm.remarks = ''

  paymentDialogVisible.value = true
}

// 提交付款
const submitPayment = async () => {
  if (!paymentFormRef.value) return

  await paymentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        console.log('提交付款数据:', paymentForm)

        await createPayment({
          orderId: paymentForm.orderId,
          amount: paymentForm.amount,
          paymentMethod: paymentForm.paymentMethod,
          paymentDate: paymentForm.paymentDate,
          remarks: paymentForm.remarks
        })

        ElMessage.success('付款记录已添加')
        paymentDialogVisible.value = false

        // 刷新订单数据
        await fetchOrderDetail()
      } catch (error) {
        console.error('Failed to add payment:', error)
        ElMessage.error('添加付款记录失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 处理编辑订单
const handleEdit = () => {
  // 检查订单状态，如果是"已完成"或"已取消"，则不允许编辑
  const status = Number(order.value.status)
  if (status === 6 || status === 7) {
    ElMessage.warning('已完成或已取消的订单不能编辑')
    return
  }

  // 初始化编辑表单
  editForm.contactPerson = order.value.contactPerson || ''
  editForm.contactPhone = order.value.contactPhone || ''
  editForm.deliveryAddress = order.value.deliveryAddress || ''
  editForm.deliveryDate = order.value.deliveryDate || ''
  editForm.needInvoice = order.value.needInvoice || false
  editForm.company = order.value.company || ''
  editForm.paymentMethod = order.value.paymentMethod || ''
  editForm.remarks = order.value.remarks || ''

  editDialogVisible.value = true
}

// 提交编辑
const submitEdit = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        console.log('提交编辑数据:', editForm)

        // 计算订单总金额
        const totalAmount = getTotalAmount()

        // 准备订单项数据
        const items = orderItems.value.map(item => ({
          id: item.id,
          productId: item.productId || item.product?.id,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          amount: item.amount
        }))

        await updateOrder(order.value.id, {
          contactPerson: editForm.contactPerson,
          contactPhone: editForm.contactPhone,
          deliveryAddress: editForm.deliveryAddress,
          deliveryDate: editForm.deliveryDate,
          needInvoice: editForm.needInvoice,
          company: editForm.company,
          paymentMethod: editForm.paymentMethod,
          remarks: editForm.remarks,
          totalAmount: totalAmount,
          items: items // 包含更新后的订单项
        })

        ElMessage.success('订单已更新')
        editDialogVisible.value = false

        // 刷新订单数据
        await fetchOrderDetail()
      } catch (error) {
        console.error('更新订单失败:', error)
        ElMessage.error('更新订单失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 处理取消订单
const handleCancel = () => {
  // 状态1(已创建)和2(已确认)可以取消
  const status = Number(order.value.status)
  if (status !== 1 && status !== 2) {
    ElMessage.warning('当前订单状态不允许取消')
    return
  }

  ElMessageBox.confirm(
    `确定要取消订单 "${order.value.orderNumber}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await cancelOrder(order.value.id)
      ElMessage.success('订单已取消')

      // 刷新订单数据
      await fetchOrderDetail()
    } catch (error) {
      console.error('Failed to cancel order:', error)
      ElMessage.error('取消订单失败')
    }
  }).catch(() => {})
}

// 打印订单
const handlePrint = () => {
  window.print()
}

// 获取订单状态文本
const getStatusText = (status) => {
  if (typeof status === 'number') {
    switch (status) {
      case 1: return '已创建'
      case 2: return '已确认'
      case 3: return '处理中'
      case 4: return '已发货'
      case 5: return '已交付'
      case 6: return '已完成'
      case 7: return '已取消'
      default: return '未知状态'
    }
  }
  return status || '未知状态'
}

// 获取订单状态对应的标签类型
const getOrderStatusType = (status) => {
  // 如果status是数字，使用数字映射
  if (typeof status === 'number') {
    switch (status) {
      case 1: return ''          // 已创建
      case 2: return 'info'      // 已确认
      case 3: return 'warning'   // 处理中
      case 4: return 'warning'   // 已发货
      case 5: return 'warning'   // 已交付
      case 6: return 'success'   // 已完成
      case 7: return 'danger'    // 已取消
      default: return ''
    }
  }

  // 如果status是字符串，使用字符串映射
  const statusMap = {
    '已创建': '',
    '已确认': 'info',
    '处理中': 'warning',
    '已发货': 'warning',
    '已交付': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

// 计算商品总数
const getTotalQuantity = () => {
  if (!orderItems.value || orderItems.value.length === 0) {
    return 0;
  }
  return orderItems.value.reduce((total, item) => {
    const quantity = Number(item.quantity) || 0;
    return total + quantity;
  }, 0);
}

// 计算商品总金额
const getTotalAmount = () => {
  if (!orderItems.value || orderItems.value.length === 0) {
    return 0;
  }
  return orderItems.value.reduce((total, item) => {
    const amount = Number(item.amount) || 0;
    return total + amount;
  }, 0);
}



// 打印出库单
const handlePrintShipment = async () => {
  try {
    // 创建一个新窗口用于预览
    const printWindow = window.open('', '_blank', 'width=800,height=600')

    if (!printWindow) {
      ElMessage.error('预览窗口被浏览器阻止，请允许弹出窗口')
      return
    }

    // 获取当前订单数据
    const currentOrder = order.value
    const currentOrderItems = orderItems.value

    // 添加调试信息
    console.log('打印出库单 - 订单数据:', currentOrder)
    console.log('打印出库单 - 订单项数据:', currentOrderItems)

    // 生成表格行HTML
    let tableRowsHtml = ''

    // 添加订单项行
    if (currentOrderItems && currentOrderItems.length > 0) {
      currentOrderItems.forEach((item, index) => {
        tableRowsHtml += `
          <tr>
            <td>${index + 1}</td>
            <td>${item.product?.name || ''}</td>
            <td>${item.product?.specification || ''}</td>
            <td>${item.product?.unit || ''}</td>
            <td>${item.quantity || ''}</td>
            <td></td>
          </tr>
        `
      })
    }

    // 不再添加固定的空行，只显示实际的产品行

    // 获取当前日期时间（北京时间）
    const now = new Date()
    const dateTimeString = now.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })

    // 设置预览窗口内容
    printWindow.document.open()
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>销售出库单预览</title>
        <meta charset="utf-8">
        <style>
          /* 基本样式 */
          body {
            margin: 0;
            padding: 20px;
            font-family: SimSun, "宋体", sans-serif;
            font-size: 14px;
          }

          /* 预览控制按钮 */
          .preview-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          .preview-controls button {
            margin-left: 10px;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          }

          .print-btn {
            background-color: #409eff;
            color: white;
          }

          .close-btn {
            background-color: #f56c6c;
            color: white;
          }

          /* 出库单样式 */
          .shipment-form {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
          }

          .company-title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 15px;
            width: 100%;
          }

          .form-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            letter-spacing: 8px;
          }

          .form-header {
            margin-bottom: 20px;
          }

          .form-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }

          .form-field {
            display: flex;
            align-items: flex-start;
          }

          .field-label {
            font-weight: bold;
            min-width: 120px;
          }

          .field-value {
            flex: 1;
          }

          /* 表格样式 */
          .shipment-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
          }

          .shipment-table th, .shipment-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 14px;
            height: 30px;
            overflow: hidden;
            word-break: break-all;
          }

          /* 列宽设置 */
          .col-seq {
            width: 8%;
          }

          .col-name {
            width: 40%;
          }

          .col-spec {
            width: 12%;
          }

          .col-unit {
            width: 10%;
          }

          .col-quantity {
            width: 10%;
          }

          .col-remark {
            width: 20%;
          }

          /* 底部样式 */
          .form-footer {
            margin-top: 30px;
            font-size: 14px;
          }

          .warning-text {
            font-weight: bold;
            margin-bottom: 20px;
          }

          .address-text {
            margin-bottom: 20px;
          }

          .signature-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
          }

          .signature-field {
            display: flex;
            align-items: center;
          }

          /* 打印样式 */
          @media print {
            body {
              padding: 0;
            }

            .preview-controls {
              display: none;
            }

            .shipment-form {
              padding: 0;
            }

            /* 确保表格在打印时不会被分页 */
            .shipment-table {
              page-break-inside: avoid;
            }

            /* 确保页脚在打印时不会被分页 */
            .form-footer {
              page-break-inside: avoid;
            }

            /* 隐藏浏览器默认的页眉页脚 */
            @page {
              margin: 0;
              size: auto;
            }
          }
        </style>
      </head>
      <body>
        <div class="preview-controls">
          <button class="print-btn" onclick="printDocument()">打印</button>
          <button class="close-btn" onclick="window.close()">关闭</button>
        </div>

        <div class="shipment-form">
          <div class="company-title">${currentOrder?.company || '具体公司'}</div>
          <h1 class="form-title">销 售 出 库 单</h1>

          <div class="form-header">
            <div class="form-row">
              <div class="form-field">
                <span class="field-label">客户名称：</span>
                <span class="field-value">${currentOrder?.customer?.name || '无'}</span>
              </div>
              <div class="form-field">
                <span class="field-label">联系人姓名/电话：</span>
                <span class="field-value">${currentOrder?.contactPerson || '无'} / ${currentOrder?.contactPhone || '无'}</span>
              </div>
            </div>

            <div class="form-row">
              <div class="form-field">
                <span class="field-label">客户地址：</span>
                <span class="field-value">${currentOrder?.deliveryAddress || '无'}</span>
              </div>
              <div class="form-field">
                <span class="field-label">发货日期：</span>
                <span class="field-value">${dateTimeString}</span>
              </div>
            </div>

            <div class="form-row">
              <div class="form-field">
                <span class="field-label">检测报告数量：</span>
                <span class="field-value">_______份</span>
              </div>
            </div>
          </div>

          <table class="shipment-table">
            <thead>
              <tr>
                <th class="col-seq">序号</th>
                <th class="col-name">商品名称</th>
                <th class="col-spec">规格</th>
                <th class="col-unit">单位</th>
                <th class="col-quantity">数量</th>
                <th class="col-remark">备注</th>
              </tr>
            </thead>
            <tbody>
              ${tableRowsHtml}
            </tbody>
          </table>

          <div class="form-footer">
            <p class="warning-text">如有质量问题或者型号，数量不对请勿损坏。一经损坏，恕不接受退换货，请知悉！</p>
            <p class="address-text">发货地址：四川省宜宾市翠屏区宜宾东方雨虹(宜宾)仓储批发中心</p>
            <div class="signature-row">
              <div class="signature-field">
                <span class="field-label">业务员/发货人：</span>
                <span class="field-value">邓雄丽</span>
              </div>
              <div class="signature-field">
                <span class="field-label">联系电话：</span>
                <span class="field-value">18408241074</span>
              </div>
            </div>
            <div class="signature-row">
              <div class="signature-field">
                <span class="field-label">收货人签字：</span>
                <span class="field-value"></span>
              </div>
            </div>
          </div>
        </div>

        <script>
          function printDocument() {
            // 打印前隐藏控制按钮
            document.querySelector('.preview-controls').style.display = 'none';
            // 执行打印
            window.print();
            // 打印后显示控制按钮
            setTimeout(function() {
              document.querySelector('.preview-controls').style.display = 'block';
            }, 100);
          }
        <\/script>
      <\/body>
      <\/html>
    `)
    printWindow.document.close()

  } catch (error) {
    console.error('打印出库单时出错:', error)
    ElMessage.error('打印出库单时出错: ' + error.message)
  }
}

// 处理确认到货
const handleConfirmDelivery = async () => {
  // 添加检查逻辑
  if (!order.value.isShipped) {
    ElMessage.warning('订单尚未发货，无法确认到货')
    return
  }

  if (order.value.isDelivered) {
    ElMessage.warning('订单已经确认到货')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要确认订单 "${order.value.orderNumber}" 已到货吗？`,
      '确认到货',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    submitting.value = true
    await confirmDelivery(order.value.id)
    ElMessage.success('已确认到货')
    await fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认到货失败:', error)
      ElMessage.error('确认到货失败')
    }
  } finally {
    submitting.value = false
  }
}

// 处理确认出库
const handleConfirmOutbound = async () => {
  if (order.value.isOutboundConfirmed) {
    ElMessage.warning('订单已经确认出库')
    return
  }

  const message = order.value.status === 1
    ? `确定要确认订单 "${order.value.orderNumber}" 并标记为已出库吗？`
    : `确定要确认订单 "${order.value.orderNumber}" 已出库吗？`

  try {
    await ElMessageBox.confirm(
      message,
      '确认出库',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    submitting.value = true
    await confirmOutbound(order.value.id)
    ElMessage.success(order.value.status === 1 ? '订单已确认并标记为已出库' : '已确认出库')
    await fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认出库失败:', error)
      ElMessage.error('确认出库失败')
    }
  } finally {
    submitting.value = false
  }
}

// 处理确认出库 - 已在上方定义

// 处理添加付款 - 已在上方定义

// 提交付款 - 已在上方定义

// 处理编辑订单 - 已在上方定义

// 提交编辑 - 已在上方定义

// 处理编辑产品项
const handleEditItem = (item, index) => {
  // 检查订单状态，如果是"已完成"或"已取消"，则不允许编辑
  const status = Number(order.value.status)
  if (status === 6 || status === 7) {
    ElMessage.warning('已完成或已取消的订单不能编辑产品')
    return
  }

  currentEditingItemIndex.value = index

  // 初始化编辑表单
  editItemForm.id = item.id || ''
  editItemForm.productId = item.productId || item.product?.id || ''
  editItemForm.productName = item.product?.name || ''
  editItemForm.specification = item.product?.specification || ''
  editItemForm.unit = item.product?.unit || ''
  editItemForm.quantity = Number(item.quantity) || 1
  editItemForm.unitPrice = Number(item.unitPrice) || 0
  editItemForm.amount = Number(item.amount) || 0

  editItemDialogVisible.value = true
}

// 处理删除产品项
const handleRemoveItem = (index) => {
  // 检查订单状态，如果是"已完成"或"已取消"，则不允许删除
  const status = Number(order.value.status)
  if (status === 6 || status === 7) {
    ElMessage.warning('已完成或已取消的订单不能删除产品')
    return
  }

  ElMessageBox.confirm(
    '确定要删除该产品吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    submitting.value = true
    try {
      // 删除产品项
      orderItems.value.splice(index, 1)

      // 计算订单总金额
      const totalAmount = getTotalAmount()

      // 准备订单项数据
      const items = orderItems.value.map(item => ({
        id: item.id,
        productId: item.productId || item.product?.id,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        amount: item.amount
      }))

      // 更新订单总金额
      await updateOrder(order.value.id, {
        totalAmount: totalAmount,
        items: items // 包含更新后的订单项
      })

      ElMessage.success('产品已删除')

      // 刷新订单数据
      await fetchOrderDetail()
    } catch (error) {
      console.error('删除产品失败:', error)
      ElMessage.error('删除产品失败')
    } finally {
      submitting.value = false
    }
  }).catch(() => {})
}

// 处理添加产品
const handleAddItem = () => {
  // 检查订单状态，如果是"已完成"或"已取消"，则不允许添加
  const status = Number(order.value.status)
  if (status === 6 || status === 7) {
    ElMessage.warning('已完成或已取消的订单不能添加产品')
    return
  }

  // 重置添加产品表单
  addItemForm.productId = ''
  addItemForm.quantity = 1
  addItemForm.unitPrice = 0
  addItemForm.amount = 0
  productOptions.value = []

  addItemDialogVisible.value = true
}

// 计算编辑产品金额
const calculateItemAmount = () => {
  editItemForm.amount = editItemForm.quantity * editItemForm.unitPrice
}

// 计算添加产品金额
const calculateAddItemAmount = () => {
  addItemForm.amount = addItemForm.quantity * addItemForm.unitPrice
}

// 获取选中产品的库存
const getSelectedProductStock = () => {
  if (!addItemForm.productId) return 0

  const selectedProduct = productOptions.value.find(p => p.id === addItemForm.productId)
  return selectedProduct ? selectedProduct.currentStock || 0 : 0
}

// 搜索产品
const searchProductItems = async (query) => {
  if (query.length < 2) return

  searchLoading.value = true
  try {
    const res = await getProducts({
      query: query,
      pageSize: 10
    })

    if (res && res.data) {
      // 临时存储产品列表
      const products = res.data.map(product => ({
        ...product,
        currentStock: 0 // 默认库存为0
      }))

      // 获取每个产品的库存信息
      const productInventoryPromises = products.map(async (product) => {
        try {
          const inventoryData = await getProductInventory(product.id)
          // 计算总库存（可能有多个仓库）
          const totalStock = inventoryData.reduce((sum, inv) => sum + inv.quantity, 0)
          product.currentStock = totalStock
          return product
        } catch (error) {
          console.error(`获取产品 ${product.id} 库存失败:`, error)
          return product // 返回原产品，保持库存为0
        }
      })

      // 等待所有库存信息获取完成
      productOptions.value = await Promise.all(productInventoryPromises)

      console.log('带库存的产品列表:', productOptions.value)
    } else {
      productOptions.value = []
    }
  } catch (error) {
    console.error('搜索产品失败:', error)
    ElMessage.error('搜索产品失败')
    productOptions.value = []
  } finally {
    searchLoading.value = false
  }
}

// 处理产品选择
const handleProductSelected = (productId) => {
  const selectedProduct = productOptions.value.find(p => p.id === productId)
  if (selectedProduct) {
    addItemForm.unitPrice = selectedProduct.price || 0

    // 设置数量为1，不考虑库存限制
    addItemForm.quantity = 1

    // 如果库存不足，显示提示信息但仍允许添加
    if (selectedProduct.currentStock <= 0) {
      ElMessage.warning(`${selectedProduct.name} 库存为 ${selectedProduct.currentStock}，请注意库存状态`)
    }

    // 更新金额
    calculateAddItemAmount()
  }
}

// 提交编辑产品
const submitEditItem = async () => {
  if (!editItemFormRef.value) return

  await editItemFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 更新产品项
        if (currentEditingItemIndex.value >= 0 && currentEditingItemIndex.value < orderItems.value.length) {
          const item = orderItems.value[currentEditingItemIndex.value]

          // 更新数量和价格
          item.quantity = editItemForm.quantity
          item.unitPrice = editItemForm.unitPrice
          item.amount = editItemForm.amount

          // 计算订单总金额
          const totalAmount = getTotalAmount()

          // 准备订单项数据
          const items = orderItems.value.map(item => ({
            id: item.id,
            productId: item.productId || item.product?.id,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            amount: item.amount
          }))

          // 更新订单总金额
          await updateOrder(order.value.id, {
            totalAmount: totalAmount,
            items: items // 包含更新后的订单项
          })

          ElMessage.success('产品已更新')
          editItemDialogVisible.value = false

          // 刷新订单数据
          await fetchOrderDetail()
        }
      } catch (error) {
        console.error('更新产品失败:', error)
        ElMessage.error('更新产品失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 提交添加产品
const submitAddItem = async () => {
  if (!addItemFormRef.value) return

  await addItemFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const selectedProduct = productOptions.value.find(p => p.id === addItemForm.productId)
        if (!selectedProduct) {
          ElMessage.error('请选择有效的产品')
          return
        }

        // 不再检查库存是否足够，允许添加任何库存状态的商品
        // 但如果库存不足，显示提示信息
        if (selectedProduct.currentStock < addItemForm.quantity) {
          ElMessage.warning(`注意: 所需数量(${addItemForm.quantity})大于当前库存(${selectedProduct.currentStock})，请确认是否继续添加`)
        }

        // 创建新产品项
        const newItem = {
          productId: selectedProduct.id,
          product: {
            id: selectedProduct.id,
            code: selectedProduct.code,
            name: selectedProduct.name,
            specification: selectedProduct.specification,
            unit: selectedProduct.unit
          },
          quantity: addItemForm.quantity,
          unitPrice: addItemForm.unitPrice,
          amount: addItemForm.amount
        }

        // 添加到订单项列表
        orderItems.value.push(newItem)

        // 计算订单总金额
        const totalAmount = getTotalAmount()

        // 准备订单项数据
        const items = orderItems.value.map(item => ({
          id: item.id,
          productId: item.productId || item.product?.id,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          amount: item.amount
        }))

        // 更新订单总金额
        await updateOrder(order.value.id, {
          totalAmount: totalAmount,
          items: items // 包含更新后的订单项
        })

        ElMessage.success(`已添加 ${selectedProduct.name}，当前库存: ${selectedProduct.currentStock}`)
        addItemDialogVisible.value = false

        // 刷新订单数据
        await fetchOrderDetail()
      } catch (error) {
        console.error('添加产品失败:', error)
        ElMessage.error('添加产品失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-summary {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.summary-item {
  margin-top: 10px;
  font-size: 14px;
}

.amount {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}

@media print {
  .page-actions, .el-button {
    display: none;
  }

  /* 打印订单时隐藏出库单 */
  .print-shipment-container {
    display: none !important;
  }
}

.unpaid-amount {
  color: #f56c6c;
  font-weight: bold;
}

.empty-data {
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  margin: 2px 0;
}

.table-footer {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}
</style>
