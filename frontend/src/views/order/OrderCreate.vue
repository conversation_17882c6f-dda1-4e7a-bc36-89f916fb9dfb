<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">创建订单</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
      </div>
    </div>

    <el-card class="form-card">
      <el-steps :active="activeStep" finish-status="success" simple>
        <el-step title="选择客户" />
        <el-step title="添加商品" />
        <el-step title="确认订单" />
      </el-steps>

      <!-- 步骤1：选择客户 -->
      <div v-if="activeStep === 0" class="step-content">
        <div class="step-header">
          <h3>选择客户</h3>
          <div class="search-bar">
            <el-input
              v-model="customerSearchQuery"
              placeholder="搜索客户..."
              clearable
              @keyup.enter="searchCustomers"
            >
              <template #suffix>
                <el-icon class="el-input__icon" @click="searchCustomers">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <el-table
          v-loading="loadingCustomers"
          :data="customerList"
          border
          highlight-current-row
          @current-change="handleCustomerSelect"
          class="data-table"
        >
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="code" label="客户编码" width="120" />
          <el-table-column prop="name" label="客户名称" min-width="150" />
          <el-table-column prop="contactPerson" label="联系人" width="120" />
          <el-table-column prop="contactPhone" label="联系电话" width="150" />
          <el-table-column prop="type" label="客户类型" width="120" />
          <el-table-column prop="isActive" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
                {{ scope.row.isActive ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="customerCurrentPage"
            v-model:page-size="customerPageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="customerTotal"
            @size-change="handleCustomerSizeChange"
            @current-change="handleCustomerCurrentChange"
          />
        </div>

        <div class="selected-customer" v-if="selectedCustomer.id">
          <h4>已选择客户</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="客户名称">{{ selectedCustomer.name }}</el-descriptions-item>
            <el-descriptions-item label="客户编码">{{ selectedCustomer.code }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ selectedCustomer.contactPerson }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ selectedCustomer.contactPhone }}</el-descriptions-item>
            <el-descriptions-item label="地址" :span="2">{{ selectedCustomer.address || '无' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="step-actions">
          <el-button type="primary" @click="nextStep" :disabled="!selectedCustomer.id">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2：添加商品 -->
      <div v-if="activeStep === 1" class="step-content">
        <div class="step-header">
          <h3>添加商品</h3>
          <div class="search-bar">
            <el-input
              v-model="productSearchQuery"
              placeholder="搜索产品..."
              clearable
              @keyup.enter="searchProducts"
            >
              <template #suffix>
                <el-icon class="el-input__icon" @click="searchProducts">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <el-table
          v-loading="loadingProducts"
          :data="productList"
          border
          class="data-table"
        >
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="code" label="产品编码" width="120" />
          <el-table-column prop="name" label="产品名称" min-width="120" />
          <el-table-column prop="specification" label="规格" width="120" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="price" label="价格" width="100">
            <template #default="scope">
              ¥{{ scope.row.price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="库存" width="100">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.currentStock <= 0 }">{{ scope.row.currentStock }}</span>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  <div>最小库存: {{ scope.row.minStock || 0 }}</div>
                  <div>最大库存: {{ scope.row.maxStock || 0 }}</div>
                </template>
                <el-icon class="ml-1"><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="addToCart(scope.row)"
                :disabled="isProductInCart(scope.row.id)"
              >
                {{ isProductInCart(scope.row.id) ? '已添加' : '添加' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="productCurrentPage"
            v-model:page-size="productPageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="productTotal"
            @size-change="handleProductSizeChange"
            @current-change="handleProductCurrentChange"
          />
        </div>

        <div class="cart-items">
          <h4>已选商品</h4>
          <el-table
            :data="cartItems"
            border
            class="data-table"
          >
            <el-table-column type="index" label="#" width="50" />
            <el-table-column prop="productCode" label="产品编码" width="120" />
            <el-table-column prop="productName" label="产品名称" min-width="150" />
            <el-table-column prop="specification" label="规格" width="120" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column label="单价" width="150">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.price"
                  :min="0.01"
                  :precision="2"
                  :step="0.1"
                  size="small"
                  @change="updateCartItem(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="数量" width="150">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.quantity"
                  :min="1"
                  size="small"
                  @change="updateCartItem(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" width="120">
              <template #default="scope">
                ¥{{ scope.row.amount.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button size="small" type="danger" @click="removeFromCart(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="cart-summary" v-if="cartItems.length > 0">
            <div class="summary-item">
              <span>商品总数:</span>
              <span>{{ getTotalQuantity() }} 件</span>
            </div>
            <div class="summary-item">
              <span>商品总金额:</span>
              <span class="amount">¥{{ getTotalAmount().toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="nextStep" :disabled="cartItems.length === 0">下一步</el-button>
        </div>
      </div>

      <!-- 步骤3：确认订单 -->
      <div v-if="activeStep === 2" class="step-content">
        <div class="step-header">
          <h3>确认订单</h3>
        </div>

        <h4>客户信息</h4>
        <el-form :model="deliveryInfo" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户名称">
                <el-input v-model="selectedCustomer.name" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户编码">
                <el-input v-model="selectedCustomer.code" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系人">
                <el-input v-model="deliveryInfo.contactPerson" placeholder="请输入联系人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话">
                <el-input v-model="deliveryInfo.contactPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="收货地址">
            <el-input v-model="deliveryInfo.address" type="textarea" :rows="2" placeholder="请输入详细地址" />
          </el-form-item>
        </el-form>

        <h4>订单商品</h4>
        <el-table
          :data="cartItems"
          border
          class="data-table"
        >
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="productCode" label="产品编码" width="120" />
          <el-table-column prop="productName" label="产品名称" min-width="150" />
          <el-table-column prop="specification" label="规格" width="120" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column prop="price" label="单价" width="100">
            <template #default="scope">
              ¥{{ scope.row.price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="scope">
              ¥{{ scope.row.amount.toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>

        <div class="cart-summary">
          <div class="summary-item">
            <span>商品总数:</span>
            <span>{{ getTotalQuantity() }} 件</span>
          </div>
          <div class="summary-item">
            <span>商品总金额:</span>
            <span class="amount">¥{{ getTotalAmount().toFixed(2) }}</span>
          </div>
        </div>

        <h4>订单选项</h4>
        <el-form>
          <el-form-item label="是否需要发票">
            <el-switch v-model="needInvoice" />
          </el-form-item>
          <el-form-item label="公司">
            <el-select v-model="company" placeholder="选择公司" filterable>
              <el-option
                v-for="company in companyOptions"
                :key="company.value"
                :label="company.label"
                :value="company.value"
              />
            </el-select>
            <div class="form-item-help">选择出库单上显示的公司</div>
          </el-form-item>
        </el-form>

        <h4>订单备注</h4>
        <el-input
          v-model="orderRemark"
          type="textarea"
          :rows="3"
          placeholder="请输入订单备注"
        />

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="submitOrder" :loading="submitting">提交订单</el-button>
        </div>
      </div>
    </el-card>


  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Search, InfoFilled } from '@element-plus/icons-vue'
import { getCustomers } from '../../api/customer'
import { getProducts, getProductInventory } from '../../api/product'
import { createOrder } from '../../api/order'

const router = useRouter()
const activeStep = ref(0)
const submitting = ref(false)
const orderRemark = ref('')
const needInvoice = ref(false)
const company = ref('宜宾市翠屏区固砂建材经营部(工商户)') // 默认选择第一个公司
const companyOptions = ref([
  { value: '宜宾市翠屏区固砂建材经营部(工商户)', label: '宜宾市翠屏区固砂建材经营部(工商户)' },
  { value: '宜宾防甲建设工程有限公司', label: '宜宾防甲建设工程有限公司' }
])

// 地址信息
const deliveryInfo = ref({
  contactPerson: '',
  contactPhone: '',
  address: ''
})

// 客户相关
const loadingCustomers = ref(false)
const customerList = ref([])
const customerTotal = ref(0)
const customerCurrentPage = ref(1)
const customerPageSize = ref(10)
const customerSearchQuery = ref('')
const selectedCustomer = ref({})

// 产品相关
const loadingProducts = ref(false)
const productList = ref([])
const productTotal = ref(0)
const productCurrentPage = ref(1)
const productPageSize = ref(10)
const productSearchQuery = ref('')
const cartItems = ref([])

// 获取客户列表
const fetchCustomers = async () => {
  loadingCustomers.value = true
  try {
    const params = {
      page: customerCurrentPage.value,
      pageSize: customerPageSize.value,
      query: customerSearchQuery.value,
      isActive: true
    }

    const res = await getCustomers(params)
    customerList.value = res.data
    customerTotal.value = res.total
  } catch (error) {
    console.error('Failed to fetch customers:', error)
    ElMessage.error('获取客户列表失败')
  } finally {
    loadingCustomers.value = false
  }
}

// 获取产品列表
const fetchProducts = async () => {
  loadingProducts.value = true
  try {
    const params = {
      page: productCurrentPage.value,
      pageSize: productPageSize.value,
      query: productSearchQuery.value,
      isActive: true
    }

    const res = await getProducts(params)

    // 临时存储产品列表
    const products = res.data.map(product => ({
      ...product,
      currentStock: 0, // 默认库存为0
      minStock: product.minStock || 0,
      maxStock: product.maxStock || 0
    }))

    // 获取每个产品的库存信息
    const productInventoryPromises = products.map(async (product) => {
      try {
        const inventoryData = await getProductInventory(product.id)
        // 计算总库存（可能有多个仓库）
        const totalStock = inventoryData.reduce((sum, inv) => sum + inv.quantity, 0)
        product.currentStock = totalStock
        return product
      } catch (error) {
        console.error(`Failed to get inventory for product ${product.id}:`, error)
        return product // 返回原产品，保持库存为0
      }
    })

    // 等待所有库存信息获取完成
    productList.value = await Promise.all(productInventoryPromises)
    productTotal.value = res.total

    // 打印日志，帮助调试
    console.log('Products with inventory:', productList.value)
  } catch (error) {
    console.error('Failed to fetch products:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loadingProducts.value = false
  }
}

// 搜索客户
const searchCustomers = () => {
  customerCurrentPage.value = 1
  fetchCustomers()
}

// 搜索产品
const searchProducts = () => {
  productCurrentPage.value = 1
  fetchProducts()
}

// 选择客户
const handleCustomerSelect = (customer) => {
  selectedCustomer.value = customer || {}

  // 初始化配送信息为客户信息
  if (customer) {
    deliveryInfo.value = {
      contactPerson: customer.contactPerson || '',
      contactPhone: customer.contactPhone || '',
      address: customer.address || ''
    }
  }
}

// 检查产品是否已在购物车中
const isProductInCart = (productId) => {
  return cartItems.value.some(item => item.productId === productId)
}

// 添加商品到购物车
const addToCart = (product) => {
  // 检查是否已经添加过该商品
  const existingItem = cartItems.value.find(item => item.productId === product.id)

  if (existingItem) {
    // 如果已经添加过，增加数量
    existingItem.quantity += 1
    existingItem.amount = existingItem.price * existingItem.quantity

    // 显示库存提示信息
    let stockMessage = `${product.name} 数量已增加`
    if (product.currentStock <= 0) {
      stockMessage += `，注意：当前库存为 ${product.currentStock}`
    } else {
      stockMessage += `，当前库存: ${product.currentStock}`
    }
    ElMessage.success(stockMessage)
  } else {
    // 如果是新商品，添加到购物车
    cartItems.value.push({
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      specification: product.specification,
      unit: product.unit,
      price: product.price,
      quantity: 1,
      maxStock: 9999, // 设置一个很大的值，不限制数量
      amount: product.price
    })

    // 显示库存提示信息
    let stockMessage = `已添加 ${product.name} 到订单`
    if (product.currentStock <= 0) {
      stockMessage += `，注意：当前库存为 ${product.currentStock}`
    } else {
      stockMessage += `，当前库存: ${product.currentStock}`
    }
    ElMessage.success(stockMessage)
  }
}

// 更新购物车商品
const updateCartItem = (item) => {
  item.amount = item.price * item.quantity
}

// 从购物车移除商品
const removeFromCart = (index) => {
  cartItems.value.splice(index, 1)
  // 移除后刷新产品列表状态
  ElMessage.success('商品已从订单中移除')
}

// 计算商品总数
const getTotalQuantity = () => {
  return cartItems.value.reduce((total, item) => total + item.quantity, 0)
}

// 计算商品总金额
const getTotalAmount = () => {
  return cartItems.value.reduce((total, item) => total + item.amount, 0)
}

// 客户分页处理
const handleCustomerSizeChange = (val) => {
  customerPageSize.value = val
  fetchCustomers()
}

const handleCustomerCurrentChange = (val) => {
  customerCurrentPage.value = val
  fetchCustomers()
}

// 产品分页处理
const handleProductSizeChange = (val) => {
  productPageSize.value = val
  fetchProducts()
}

const handleProductCurrentChange = (val) => {
  productCurrentPage.value = val
  fetchProducts()
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value -= 1
  }
}

// 下一步
const nextStep = () => {
  if (activeStep.value < 2) {
    activeStep.value += 1

    // 如果进入第二步，加载产品列表
    if (activeStep.value === 1) {
      fetchProducts()
    }
  }
}



// 提交订单
const submitOrder = async () => {
  if (cartItems.value.length === 0) {
    ElMessage.warning('请至少添加一件商品')
    return
  }

  submitting.value = true
  try {
    // 获取当前日期，格式为 YYYY-MM-DD
    const today = new Date().toISOString().split('T')[0]

    const orderData = {
      customerId: selectedCustomer.value.id,
      orderDate: today, // 添加订单日期字段
      needInvoice: needInvoice.value, // 添加是否需要发票字段
      company: company.value, // 添加公司字段
      // 添加地址信息
      contactPerson: deliveryInfo.value.contactPerson || selectedCustomer.value.contactPerson || '',
      contactPhone: deliveryInfo.value.contactPhone || selectedCustomer.value.contactPhone || '',
      deliveryAddress: deliveryInfo.value.address || selectedCustomer.value.address || '',
      items: cartItems.value.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.price // 使用unitPrice字段名与后端匹配
      })),
      remarks: orderRemark.value // 使用remarks字段名与后端匹配
    }

    console.log('提交订单数据:', orderData) // 添加日志，帮助调试

    const res = await createOrder(orderData)
    ElMessage.success('订单创建成功')
    // 直接跳转到订单管理页面
    router.push('/orders')
  } catch (error) {
    console.error('Failed to create order:', error)
    ElMessage.error('创建订单失败')
  } finally {
    submitting.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchCustomers()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-card {
  margin-bottom: 20px;
}

.step-content {
  margin-top: 30px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  width: 300px;
}

.data-table {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.selected-customer {
  margin: 20px 0;
}

.cart-items {
  margin: 20px 0;
}

.cart-summary {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.summary-item {
  margin-top: 10px;
  font-size: 14px;
}

.amount {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

h4 {
  margin: 20px 0 10px;
  font-size: 16px;
  color: #606266;
}

.ml-1 {
  margin-left: 5px;
  cursor: pointer;
  color: #909399;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.form-item-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
}
</style>
