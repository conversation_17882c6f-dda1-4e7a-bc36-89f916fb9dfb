<template>
  <div class="page-container">
    <div class="page-title">订单管理</div>

    <div class="action-bar">
      <div class="left-actions">
        <el-button type="primary" @click="handleCreateOrder">
          <el-icon><Plus /></el-icon>新建订单
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>导出
        </el-button>
      </div>

      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索订单号或客户..."
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="el-input__icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select v-model="filterStatus" placeholder="状态" clearable @change="handleSearch">
          <el-option label="全部状态" value="" />
          <el-option label="已创建" value="1" />
          <el-option label="已确认" value="2" />
          <el-option label="处理中" value="3" />
          <el-option label="已发货" value="4" />
          <el-option label="已交付" value="5" />
          <el-option label="已完成" value="6" />
          <el-option label="已取消" value="7" />
        </el-select>

        <el-select v-model="filterCustomerId" placeholder="客户" clearable @change="handleSearch">
          <el-option label="全部客户" value="" />
          <el-option
            v-for="customer in customerOptions"
            :key="customer.id"
            :label="customer.name"
            :value="customer.id"
          />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="handleSearch"
        />
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="orderList"
      border
      class="data-table"
    >

      <el-table-column prop="orderNumber" label="订单号" width="150" />
      <el-table-column label="客户" width="120">
        <template #default="scope">
          <div>{{ scope.row.customer.name }}</div>
          <div class="text-muted">{{ scope.row.customer.code }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单日期" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.orderDate) }}
        </template>
      </el-table-column>
      <el-table-column label="金额" width="120">
        <template #default="scope">
          <div>总额: ¥{{ scope.row.totalAmount.toFixed(2) }}</div>
          <div class="text-muted">已付: ¥{{ scope.row.paidAmount.toFixed(2) }}</div>
          <div v-if="scope.row.unpaidAmount > 0" class="unpaid-amount">
            欠款: ¥{{ scope.row.unpaidAmount.toFixed(2) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" width="90">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ scope.row.statusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="出库状态" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.isOutboundConfirmed ? 'success' : 'info'" size="small">
            {{ scope.row.isOutboundConfirmed ? '已出库' : '未出库' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="350" fixed="right">
        <template #default="scope">
          <div class="operation-buttons">
            <!-- 按钮顺序: 出库确认, 收款, 编辑, 查看, 取消 -->
            <el-button
              v-if="!scope.row.isOutboundConfirmed && scope.row.status < 7"
              size="small"
              type="success"
              @click="handleConfirmOutbound(scope.row)"
            >出库确认</el-button>
            <el-button
              v-if="scope.row.unpaidAmount > 0 && scope.row.status !== 7"
              size="small"
              type="primary"
              @click="handleAddPayment(scope.row)"
            >收款</el-button>
            <el-button
              size="small"
              type="warning"
              @click="handleEdit(scope.row)"
              :disabled="scope.row.status === 6 || scope.row.status === 7"
            >编辑</el-button>
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button
              v-if="scope.row.status < 3"
              size="small"
              type="danger"
              @click="handleCancel(scope.row)"
            >取消</el-button>
            <el-button
              v-if="scope.row.isOutboundConfirmed && !scope.row.isShipped"
              size="small"
              type="primary"
              @click="handlePrintOutbound(scope.row)"
            >出库单打印</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加付款对话框 -->
    <el-dialog v-model="paymentDialogVisible" title="添加付款记录" width="500px">
      <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="paymentForm.orderNumber" disabled />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="paymentForm.customerName" disabled />
        </el-form-item>
        <el-form-item label="欠款金额">
          <el-input v-model="paymentForm.unpaidAmount" disabled>
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="支付金额" prop="amount">
          <el-input-number v-model="paymentForm.amount" :precision="2" :min="0.01" :max="paymentForm.unpaidAmount" style="width: 100%" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="paymentForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="对公" value="corporate" />
            <el-option label="对私" value="personal" />
            <el-option label="微信" value="wechat" />
            <el-option label="支付宝" value="alipay" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付日期" prop="paymentDate">
          <el-date-picker v-model="paymentForm.paymentDate" type="date" placeholder="选择日期" style="width: 100%" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="paymentForm.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="paymentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPayment" :loading="submitting">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Download, Search, Edit, Box, Printer, Money } from '@element-plus/icons-vue'
import { getOrders, getOrderById, cancelOrder, confirmOutbound, createPayment } from '../../api/order'
import { getCustomers } from '../../api/customer'

const router = useRouter()
const loading = ref(false)
const orderList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filterStatus = ref('')
const filterCustomerId = ref('')
const dateRange = ref([])
const customerOptions = ref([])
const paymentDialogVisible = ref(false)
const submitting = ref(false)
const paymentFormRef = ref(null)

// 付款表单
const paymentForm = reactive({
  orderId: '',
  orderNumber: '',
  customerName: '',
  unpaidAmount: 0,
  amount: 0,
  paymentMethod: 'corporate',
  paymentDate: '',
  remarks: ''
})

// 付款表单验证规则
const paymentRules = {
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  paymentDate: [
    { required: true, message: '请选择支付日期', trigger: 'change' }
  ]
}

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      query: searchQuery.value,
      status: filterStatus.value,
      customerId: filterCustomerId.value
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const res = await getOrders(params)
    orderList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('Failed to fetch orders:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取客户选项
const fetchCustomerOptions = async () => {
  try {
    const res = await getCustomers({ pageSize: 100 })
    customerOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch customers:', error)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchOrders()
}

// 处理刷新
const handleRefresh = () => {
  searchQuery.value = ''
  filterStatus.value = ''
  filterCustomerId.value = ''
  dateRange.value = []
  currentPage.value = 1
  fetchOrders()
}

// 处理导出
const handleExport = () => {
  ElMessage.success('导出功能待实现')
}

// 处理创建订单
const handleCreateOrder = () => {
  router.push('/orders/create')
}

// 处理查看
const handleView = (row) => {
  console.log('查看订单，ID:', row.id)
  console.log('订单数据:', row)
  router.push(`/orders/${row.id}`)
}



// 处理取消订单
const handleCancel = (row) => {
  ElMessageBox.confirm('确定要取消此订单?', '取消订单', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await cancelOrder(row.id)
      ElMessage.success('订单已取消')
      fetchOrders()
    } catch (error) {
      console.error('Failed to cancel order:', error)

      // 提供更具体的错误提示
      let errorMessage = '取消订单失败'

      // 检查是否有响应数据
      if (error.response && error.response.data) {
        const responseData = error.response.data

        if (responseData.message) {
          // 使用后端返回的错误信息
          errorMessage = `取消订单失败: ${responseData.message}`
        }
      }

      ElMessage.error(errorMessage)
    }
  }).catch(() => {})
}

// 处理编辑订单
const handleEdit = (row) => {
  // 检查订单状态，如果是已完成或已取消状态，则不允许编辑
  if (row.status === 6 || row.status === 7) {
    ElMessage.warning('已完成或已取消的订单不能编辑')
    return
  }
  router.push(`/orders/${row.id}?edit=true`)
}

// 处理确认出库
const handleConfirmOutbound = (row) => {
  const message = row.status === 1
    ? `确定要确认订单 "${row.orderNumber}" 并标记为已出库吗？`
    : `确定要确认订单 "${row.orderNumber}" 已出库吗？`

  ElMessageBox.confirm(message, '确认出库', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'info'
  }).then(async () => {
    try {
      await confirmOutbound(row.id)
      ElMessage.success(row.status === 1 ? '订单已确认并标记为已出库' : '已确认出库')
      fetchOrders()
    } catch (error) {
      console.error('确认出库失败:', error)

      // 提供更具体的错误提示
      let errorMessage = '确认出库失败'

      // 检查是否有响应数据
      if (error.response && error.response.data) {
        const responseData = error.response.data

        // 处理特定错误类型
        if (responseData.message && responseData.message.includes('not found in warehouse')) {
          // 产品不在仓库中的错误
          const productId = responseData.message.match(/ID (\d+)/)?.[1]
          errorMessage = `确认出库失败: 产品ID ${productId || '未知'} 在仓库中不存在，请先添加库存`
        } else if (responseData.message && responseData.message.includes('Insufficient inventory')) {
          // 库存不足的错误
          const productId = responseData.message.match(/ID (\d+)/)?.[1]
          errorMessage = `确认出库失败: 产品ID ${productId || '未知'} 库存不足，请先补充库存`
        } else if (responseData.message) {
          // 其他后端返回的错误信息
          errorMessage = `确认出库失败: ${responseData.message}`
        }
      }

      ElMessage.error(errorMessage)
    }
  }).catch(() => {})
}

// 处理打印出库单
const handlePrintOutbound = async (row) => {
  try {
    // 创建一个新窗口用于预览
    const printWindow = window.open('', '_blank', 'width=800,height=600')

    if (!printWindow) {
      ElMessage.error('预览窗口被浏览器阻止，请允许弹出窗口')
      return
    }

    // 获取当前订单数据
    let currentOrder = row
    let currentOrderItems = row.items || []

    // 如果订单项为空，则需要获取完整的订单详情
    if (!currentOrderItems || currentOrderItems.length === 0) {
      console.log('订单项为空，获取完整订单详情')
      try {
        const orderDetail = await getOrderById(row.id)
        if (orderDetail && orderDetail.items) {
          currentOrder = orderDetail
          currentOrderItems = orderDetail.items
          console.log('获取到完整订单详情:', currentOrder)
        }
      } catch (error) {
        console.error('获取订单详情失败:', error)
        ElMessage.error('获取订单详情失败，无法打印出库单')
        printWindow.close()
        return
      }
    }

    // 添加调试信息
    console.log('打印出库单 - 订单数据:', currentOrder)
    console.log('打印出库单 - 订单项数据:', currentOrderItems)

    // 生成表格行HTML
    let tableRowsHtml = ''

    // 添加订单项行
    if (currentOrderItems && currentOrderItems.length > 0) {
      currentOrderItems.forEach((item, index) => {
        tableRowsHtml += `
          <tr>
            <td>${index + 1}</td>
            <td>${item.product?.name || ''}</td>
            <td>${item.product?.specification || ''}</td>
            <td>${item.product?.unit || ''}</td>
            <td>${item.quantity || ''}</td>
            <td></td>
          </tr>
        `
      })
    }

    // 使用订单的出库日期（而不是当前时间）
    let dateTimeString = '无'
    if (currentOrder.outboundDate) {
      // 使用formatDateTime函数格式化出库日期
      dateTimeString = formatDateTime(currentOrder.outboundDate)
    }

    // 设置预览窗口内容
    printWindow.document.open()
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>销售出库单预览</title>
        <meta charset="utf-8">
        <style>
          /* 基本样式 */
          body {
            margin: 0;
            padding: 20px;
            font-family: SimSun, "宋体", sans-serif;
            font-size: 14px;
          }

          /* 预览控制按钮 */
          .preview-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          .preview-controls button {
            margin-left: 10px;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          }

          .print-btn {
            background-color: #409eff;
            color: white;
          }

          .close-btn {
            background-color: #f56c6c;
            color: white;
          }

          /* 出库单样式 */
          .shipment-form {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
          }

          .company-title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 15px;
            width: 100%;
          }

          .form-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            letter-spacing: 8px;
          }

          .form-header {
            margin-bottom: 20px;
          }

          .form-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }

          .form-field {
            display: flex;
            align-items: flex-start;
          }

          .field-label {
            font-weight: bold;
            min-width: 120px;
          }

          .field-value {
            flex: 1;
          }

          /* 表格样式 */
          .shipment-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
          }

          .shipment-table th, .shipment-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 14px;
            height: 30px;
            overflow: hidden;
            word-break: break-all;
          }

          /* 列宽设置 */
          .col-seq {
            width: 8%;
          }

          .col-name {
            width: 40%;
          }

          .col-spec {
            width: 12%;
          }

          .col-unit {
            width: 10%;
          }

          .col-quantity {
            width: 10%;
          }

          .col-remark {
            width: 20%;
          }

          /* 底部样式 */
          .form-footer {
            margin-top: 30px;
            font-size: 14px;
          }

          .warning-text {
            font-weight: bold;
            margin-bottom: 20px;
          }

          .address-text {
            margin-bottom: 20px;
          }

          .signature-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
          }

          .signature-field {
            display: flex;
            align-items: center;
          }

          /* 打印样式 */
          @media print {
            body {
              padding: 0;
            }

            .preview-controls {
              display: none;
            }

            .shipment-form {
              padding: 0;
            }

            /* 确保表格在打印时不会被分页 */
            .shipment-table {
              page-break-inside: avoid;
            }

            /* 确保页脚在打印时不会被分页 */
            .form-footer {
              page-break-inside: avoid;
            }

            /* 隐藏浏览器默认的页眉页脚 */
            @page {
              margin: 0;
              size: auto;
            }
          }
        </style>
      </head>
      <body>
        <div class="preview-controls">
          <button class="print-btn" onclick="printDocument()">打印</button>
          <button class="close-btn" onclick="window.close()">关闭</button>
        </div>

        <div class="shipment-form">
          <div class="company-title">${currentOrder?.company || '具体公司'}</div>
          <h1 class="form-title">销 售 出 库 单</h1>

          <div class="form-header">
            <div class="form-row">
              <div class="form-field">
                <span class="field-label">客户名称：</span>
                <span class="field-value">${currentOrder?.customer?.name || '无'}</span>
              </div>
              <div class="form-field">
                <span class="field-label">联系人姓名/电话：</span>
                <span class="field-value">${currentOrder?.contactPerson || '无'} / ${currentOrder?.contactPhone || '无'}</span>
              </div>
            </div>

            <div class="form-row">
              <div class="form-field">
                <span class="field-label">客户地址：</span>
                <span class="field-value">${currentOrder?.deliveryAddress || '无'}</span>
              </div>
              <div class="form-field">
                <span class="field-label">出库日期：</span>
                <span class="field-value">${dateTimeString}</span>
              </div>
            </div>

            <div class="form-row">
              <div class="form-field">
                <span class="field-label">检测报告数量：</span>
                <span class="field-value">_______份</span>
              </div>
            </div>
          </div>

          <table class="shipment-table">
            <thead>
              <tr>
                <th class="col-seq">序号</th>
                <th class="col-name">商品名称</th>
                <th class="col-spec">规格</th>
                <th class="col-unit">单位</th>
                <th class="col-quantity">数量</th>
                <th class="col-remark">备注</th>
              </tr>
            </thead>
            <tbody>
              ${tableRowsHtml}
            </tbody>
          </table>

          <div class="form-footer">
            <p class="warning-text">如有质量问题或者型号，数量不对请勿损坏。一经损坏，恕不接受退换货，请知悉！</p>
            <p class="address-text">发货地址：四川省宜宾市翠屏区宜宾东方雨虹(宜宾)仓储批发中心</p>
            <div class="signature-row">
              <div class="signature-field">
                <span class="field-label">业务员/发货人：</span>
                <span class="field-value">邓雄丽</span>
              </div>
              <div class="signature-field">
                <span class="field-label">联系电话：</span>
                <span class="field-value">18408241074</span>
              </div>
            </div>
            <div class="signature-row">
              <div class="signature-field">
                <span class="field-label">收货人签字：</span>
                <span class="field-value"></span>
              </div>
            </div>
          </div>
        </div>

        <script>
          function printDocument() {
            // 打印前隐藏控制按钮
            document.querySelector('.preview-controls').style.display = 'none';
            // 执行打印
            window.print();
            // 打印后显示控制按钮
            setTimeout(function() {
              document.querySelector('.preview-controls').style.display = 'block';
            }, 100);
          }
        <\/script>
      <\/body>
      <\/html>
    `)
    printWindow.document.close()

  } catch (error) {
    console.error('打印出库单时出错:', error)
    ElMessage.error('打印出库单时出错: ' + error.message)
  }
}

// 处理添加付款
const handleAddPayment = (row) => {
  paymentForm.orderId = row.id
  paymentForm.orderNumber = row.orderNumber
  paymentForm.customerName = row.customer.name
  paymentForm.unpaidAmount = row.unpaidAmount || 0
  paymentForm.amount = row.unpaidAmount || 0
  paymentForm.paymentMethod = row.paymentMethod || 'corporate'
  paymentForm.paymentDate = new Date().toISOString().split('T')[0]
  paymentForm.remarks = ''

  paymentDialogVisible.value = true
}

// 提交付款
const submitPayment = async () => {
  if (!paymentFormRef.value) return

  await paymentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await createPayment({
          orderId: paymentForm.orderId,
          amount: paymentForm.amount,
          paymentMethod: paymentForm.paymentMethod,
          paymentDate: paymentForm.paymentDate,
          remarks: paymentForm.remarks
        })

        ElMessage.success('付款记录添加成功')
        paymentDialogVisible.value = false

        // 刷新订单数据
        await fetchOrders()
      } catch (error) {
        console.error('添加付款记录失败:', error)

        // 提供更具体的错误提示
        let errorMessage = '添加付款记录失败'

        // 检查是否有响应数据
        if (error.response && error.response.data) {
          const responseData = error.response.data

          if (responseData.message) {
            // 使用后端返回的错误信息
            errorMessage = `添加付款记录失败: ${responseData.message}`
          }
        }

        ElMessage.error(errorMessage)
      } finally {
        submitting.value = false
      }
    }
  })
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 1: return ''          // 已创建
    case 2: return 'info'      // 已确认
    case 3: return 'warning'   // 处理中
    case 4: return 'warning'   // 已发货
    case 5: return 'warning'   // 已交付
    case 6: return 'success'   // 已完成
    case 7: return 'danger'    // 已取消
    default: return ''
  }
}

// 格式化日期时间为北京时间，精确到秒
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  // 使用toLocaleString设置为zh-CN区域，显示北京时间
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchOrders()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchOrders()
}

onMounted(() => {
  fetchOrders()
  fetchCustomerOptions()
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  gap: 10px;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.left-actions {
  display: flex;
  gap: 10px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: flex-start;
}

.operation-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  margin: 2px 0;
}

.unpaid-amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 12px;
}
</style>
