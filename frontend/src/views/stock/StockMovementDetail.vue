<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">出入库记录详情</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
        <el-button type="primary" @click="printRecord">
          <el-icon><Printer /></el-icon>打印
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="movement.movementType === 1 ? 'success' : 'danger'">
            {{ movement.movementTypeText }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ movement.id }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ movement.movementTypeText }}</el-descriptions-item>
        <el-descriptions-item label="操作数量">{{ movement.quantity }}</el-descriptions-item>
        <el-descriptions-item label="单价">¥{{ movement.unitPrice?.toFixed(2) || '0.00' }}</el-descriptions-item>
        <el-descriptions-item label="批次号">{{ movement.batchNumber || '无' }}</el-descriptions-item>
        <el-descriptions-item label="操作日期">{{ movement.movementDate }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(movement.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ movement.remarks || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>产品信息</span>
          <el-button type="primary" size="small" @click="viewProduct">查看产品详情</el-button>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品ID">{{ movement.productId }}</el-descriptions-item>
        <el-descriptions-item label="产品编码">{{ movement.product?.code }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ movement.product?.name }}</el-descriptions-item>
        <el-descriptions-item label="产品类别">{{ movement.product?.category }}</el-descriptions-item>
        <el-descriptions-item label="规格">{{ movement.product?.specification }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ movement.product?.unit }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>仓库信息</span>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="源仓库">{{ movement.sourceWarehouse?.name }}</el-descriptions-item>
        <el-descriptions-item label="目标仓库" v-if="movement.destinationWarehouse">{{ movement.destinationWarehouse?.name }}</el-descriptions-item>
        <el-descriptions-item label="目标仓库" v-else>无</el-descriptions-item>
        <el-descriptions-item label="原因">{{ movement.reason || '无' }}</el-descriptions-item>
        <el-descriptions-item label="参考">{{ movement.reference || '无' }}</el-descriptions-item>
        <el-descriptions-item label="公司" v-if="movement.company">{{ movement.company }}</el-descriptions-item>
        <el-descriptions-item label="公司" v-else>未设置</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card" v-if="movement.customer">
      <template #header>
        <div class="card-header">
          <span>客户信息</span>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="客户ID">{{ movement.customerId }}</el-descriptions-item>
        <el-descriptions-item label="客户编码">{{ movement.customer?.code }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ movement.customer?.name }}</el-descriptions-item>
        <el-descriptions-item label="客户类型">{{ movement.customer?.type }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Printer } from '@element-plus/icons-vue'
import { getMovementById } from '../../api/stockMovement'

const router = useRouter()
const route = useRoute()
const loading = ref(true)
const movement = ref({
  id: '',
  productId: '',
  sourceWarehouseId: '',
  destinationWarehouseId: null,
  quantity: 0,
  unitPrice: 0,
  movementType: 0,
  movementTypeText: '',
  reason: '',
  reference: '',
  remarks: '',
  batchNumber: '',
  movementDate: '',
  customerId: null,
  company: '',
  createdAt: '',
  updatedAt: '',

  // 关联对象
  product: null,
  sourceWarehouse: null,
  destinationWarehouse: null,
  customer: null
})

// 获取出入库记录详情
const fetchMovementDetail = async () => {
  loading.value = true
  try {
    const movementId = route.params.id
    const res = await getMovementById(movementId)
    console.log('获取到的出入库记录详情:', res.data)

    // 直接使用后端返回的数据结构，保持嵌套对象
    const data = res.data
    movement.value = {
      id: data.id,
      productId: data.productId,
      sourceWarehouseId: data.sourceWarehouseId,
      destinationWarehouseId: data.destinationWarehouseId,
      quantity: data.quantity || 0,
      unitPrice: data.unitPrice || 0,
      movementType: data.movementType,
      movementTypeText: data.movementTypeText || '未知',
      reason: data.reason || '',
      reference: data.reference || '',
      remarks: data.remarks || '',
      batchNumber: data.batchNumber || '',
      movementDate: data.movementDate,
      customerId: data.customerId,
      company: data.company || '',
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,

      // 关联对象
      product: data.product || null,
      sourceWarehouse: data.sourceWarehouse || null,
      destinationWarehouse: data.destinationWarehouse || null,
      customer: data.customer || null
    }
  } catch (error) {
    console.error('Failed to fetch movement details:', error)
    ElMessage.error('获取出入库记录详情失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 查看产品详情
const viewProduct = () => {
  router.push(`/products/${movement.value.productId}`)
}

// 打印记录
const printRecord = () => {
  window.print()
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchMovementDetail()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media print {
  .page-actions, .el-button {
    display: none;
  }
}
</style>
