<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">出入库记录详情</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
        <el-button type="primary" @click="printRecord">
          <el-icon><Printer /></el-icon>打印
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="movement.type === '入库' ? 'success' : 'danger'">
            {{ movement.type }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ movement.id }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ movement.type }}</el-descriptions-item>
        <el-descriptions-item label="操作数量">{{ movement.quantity }} {{ movement.unit }}</el-descriptions-item>
        <el-descriptions-item label="操作前库存">{{ movement.beforeStock }} {{ movement.unit }}</el-descriptions-item>
        <el-descriptions-item label="操作后库存">{{ movement.afterStock }} {{ movement.unit }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ formatDateTime(movement.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ movement.operator }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ movement.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>产品信息</span>
          <el-button type="primary" size="small" @click="viewProduct">查看产品详情</el-button>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品ID">{{ movement.productId }}</el-descriptions-item>
        <el-descriptions-item label="产品编码">{{ movement.productCode }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ movement.productName }}</el-descriptions-item>
        <el-descriptions-item label="产品类别">{{ movement.category }}</el-descriptions-item>
        <el-descriptions-item label="规格">{{ movement.specification }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ movement.unit }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>批次信息</span>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="批次号">
          <el-tag v-if="movement.batchNumber" type="success">{{ movement.batchNumber }}</el-tag>
          <el-tag v-else type="info">无批次</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作日期">{{ formatDateTime(movement.movementDate) }}</el-descriptions-item>
        <el-descriptions-item label="单价" v-if="movement.unitPrice">¥{{ movement.unitPrice.toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="单价" v-else>未设置</el-descriptions-item>
        <el-descriptions-item label="总金额" v-if="movement.unitPrice && movement.quantity">
          ¥{{ (movement.unitPrice * movement.quantity).toFixed(2) }}
        </el-descriptions-item>
        <el-descriptions-item label="总金额" v-else>未设置</el-descriptions-item>
        <el-descriptions-item label="公司" v-if="movement.company">{{ movement.company }}</el-descriptions-item>
        <el-descriptions-item label="公司" v-else>未设置</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card" v-if="movement.relatedOrderId">
      <template #header>
        <div class="card-header">
          <span>关联订单信息</span>
          <el-button type="primary" size="small" @click="viewOrder">查看订单详情</el-button>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单ID">{{ movement.relatedOrderId }}</el-descriptions-item>
        <el-descriptions-item label="订单编号">{{ movement.orderCode }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ movement.customerName }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ movement.orderStatus }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">¥{{ movement.orderAmount?.toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(movement.orderCreatedAt) }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Printer } from '@element-plus/icons-vue'
import { getMovementById } from '../../api/stockMovement'

const router = useRouter()
const route = useRoute()
const loading = ref(true)
const movement = ref({
  id: '',
  type: '',
  quantity: 0,
  beforeStock: 0,
  afterStock: 0,
  createdAt: '',
  operator: '',
  remark: '',

  productId: '',
  productCode: '',
  productName: '',
  category: '',
  specification: '',
  unit: '',

  batchId: '',
  batchCode: '',
  batchType: '',
  batchCreatedAt: '',
  batchCreator: '',
  batchRemark: '',

  company: '',

  relatedOrderId: '',
  orderCode: '',
  customerName: '',
  orderStatus: '',
  orderAmount: 0,
  orderCreatedAt: ''
})

// 获取出入库记录详情
const fetchMovementDetail = async () => {
  loading.value = true
  try {
    const movementId = route.params.id
    const res = await getMovementById(movementId)
    movement.value = res.data
  } catch (error) {
    console.error('Failed to fetch movement details:', error)
    ElMessage.error('获取出入库记录详情失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 查看产品详情
const viewProduct = () => {
  router.push(`/products/${movement.value.productId}`)
}

// 不再需要查看批次详情功能

// 查看订单详情
const viewOrder = () => {
  router.push(`/orders/${movement.value.relatedOrderId}`)
}

// 打印记录
const printRecord = () => {
  window.print()
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchMovementDetail()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media print {
  .page-actions, .el-button {
    display: none;
  }
}
</style>
