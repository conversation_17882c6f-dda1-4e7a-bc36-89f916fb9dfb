<template>
  <div class="page-container">
    <div class="page-title">出入库记录</div>

    <div class="action-bar">
      <div class="left-actions">
        <el-button type="primary" @click="handleStockIn">
          <el-icon><Plus /></el-icon>入库
        </el-button>
        <el-button type="warning" @click="handleStockOut">
          <el-icon><Minus /></el-icon>出库
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
      </div>

      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索产品或仓库..."
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="el-input__icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select v-model="filterType" placeholder="类型" clearable @change="handleSearch">
          <el-option label="全部类型" value="" />
          <el-option label="入库" value="1" />
          <el-option label="出库" value="2" />
          <el-option label="调拨" value="3" />
          <el-option label="调整" value="4" />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="handleSearch"
        />
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="stockMovementList"
      border
      class="data-table"
    >

      <el-table-column label="类型" width="100">
        <template #default="scope">
          <el-tag :type="getTypeTagType(scope.row.movementType)">
            {{ scope.row.movementTypeText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="产品" min-width="120">
        <template #default="scope">
          <div>{{ scope.row.product.name }}</div>
          <div class="text-muted">{{ scope.row.product.code }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="quantity" label="数量" width="100">
        <template #default="scope">
          <span>{{ scope.row.quantity }} {{ scope.row.product.unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批次号" width="120">
        <template #default="scope">
          <span>{{ scope.row.batchNumber || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="movementDate" label="日期" width="120" />
      <el-table-column prop="reason" label="原因" min-width="150" />
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 入库表单对话框 -->
    <el-dialog
      v-model="stockInDialogVisible"
      title="入库"
      width="600px"
    >
      <el-form :model="stockInForm" :rules="stockInRules" ref="stockInFormRef" label-width="100px">
        <el-form-item label="产品" required>
          <el-select v-model="stockInForm.productId" placeholder="选择产品" filterable>
            <el-option
              v-for="product in productOptions"
              :key="product.id"
              :label="product.code + '-' + product.name"
              :value="product.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="仓库" required>
          <el-select v-model="stockInForm.sourceWarehouseId" placeholder="选择仓库">
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数量" required>
          <el-input-number v-model="stockInForm.quantity" :min="0.01" :precision="2" />
        </el-form-item>
        <el-form-item label="单价">
          <el-input-number v-model="stockInForm.unitPrice" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="批次号">
          <el-input v-model="stockInForm.batchNumber" placeholder="请输入批次号">
            <template #append>
              <el-button @click="generateAndSetBatchNumber" size="small">
                <el-tooltip content="点击生成当前日期格式的批次号(年月日)">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </el-button>
            </template>
          </el-input>
          <div class="form-item-help">批次号可手动输入或点击右侧按钮生成，格式建议为年月日</div>
        </el-form-item>
        <el-form-item label="入库日期" required>
          <el-date-picker v-model="stockInForm.movementDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="入库原因">
          <el-input v-model="stockInForm.reason" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="stockInForm.remarks" type="textarea" rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="stockInDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStockIn">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 出库表单对话框 -->
    <el-dialog
      v-model="stockOutDialogVisible"
      title="出库"
      width="600px"
    >
      <el-form :model="stockOutForm" label-width="100px">
        <el-form-item label="产品" required>
          <el-select v-model="stockOutForm.productId" placeholder="选择产品" filterable @change="handleProductChange">
            <el-option
              v-for="product in productOptions"
              :key="product.id"
              :label="product.code + '-' + product.name"
              :value="product.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="仓库" required>
          <el-select v-model="stockOutForm.sourceWarehouseId" placeholder="选择仓库" @change="handleWarehouseChange">
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="当前库存">
          <span>{{ currentStock }} {{ selectedProduct?.unit || '' }}</span>
        </el-form-item>
        <el-form-item label="数量" required>
          <el-input-number
            v-model="stockOutForm.quantity"
            :min="0.01"
            :max="currentStock > 0 ? currentStock : 999999"
            :precision="2"
            :disabled="currentStock <= 0"
          />
          <div v-if="currentStock <= 0" class="form-item-help" style="color: #F56C6C;">
            当前库存不足，请先选择产品和仓库
          </div>
        </el-form-item>
        <el-form-item label="单价">
          <el-input-number v-model="stockOutForm.unitPrice" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="客户">
          <el-select v-model="stockOutForm.customerId" placeholder="选择客户" filterable clearable>
            <el-option
              v-for="customer in customerOptions"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公司">
          <el-select v-model="stockOutForm.company" placeholder="选择公司" filterable>
            <el-option
              v-for="company in companyOptions"
              :key="company.value"
              :label="company.label"
              :value="company.value"
            />
          </el-select>
          <div class="form-item-help">选择出库单上显示的公司</div>
        </el-form-item>
        <el-form-item label="批次号">
          <el-select v-model="stockOutForm.batchNumber" placeholder="选择批次" filterable clearable>
            <el-option
              v-for="batch in availableBatches"
              :key="batch.batchNumber"
              :label="batch.batchNumber"
              :value="batch.batchNumber"
            >
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ batch.batchNumber }}</span>
                <span style="color: #8492a6; font-size: 13px">
                  库存: {{ batch.quantity }} {{ selectedProduct?.unit || '' }}
                </span>
              </div>
            </el-option>
          </el-select>
          <div class="form-item-help">选择要出库的批次，如不选择则系统自动选择最早入库的批次</div>
        </el-form-item>
        <el-form-item label="出库日期" required>
          <el-date-picker v-model="stockOutForm.movementDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="出库原因">
          <el-input v-model="stockOutForm.reason" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="stockOutForm.remarks" type="textarea" rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="stockOutDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStockOut">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Minus, Refresh, Search, InfoFilled } from '@element-plus/icons-vue'
import { getStockMovements, createStockIn, createStockOut } from '../../api/stockMovement'
import { getProducts } from '../../api/product'
import { getWarehouses } from '../../api/warehouse'
import { getCustomers } from '../../api/customer'
import { getProductInventory } from '../../api/inventory'

const router = useRouter()
const loading = ref(false)
const stockMovementList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filterType = ref('')
const dateRange = ref([])
const productOptions = ref([])
const warehouseOptions = ref([])
const customerOptions = ref([])
const companyOptions = ref([
  { value: '宜宾市翠屏区固砂建材经营部(工商户)', label: '宜宾市翠屏区固砂建材经营部(工商户)' },
  { value: '宜宾防甲建设工程有限公司', label: '宜宾防甲建设工程有限公司' }
])
const currentStock = ref(0)
const selectedProduct = ref(null)
const availableBatches = ref([])

// 表单引用
const stockInFormRef = ref(null)
const stockOutFormRef = ref(null)

// 入库表单
const stockInDialogVisible = ref(false)
const stockInForm = reactive({
  productId: '',
  sourceWarehouseId: '',
  quantity: 1,
  unitPrice: 0,
  batchNumber: '',
  movementDate: new Date().toISOString().split('T')[0],
  reason: '采购入库',
  remarks: ''
})

// 出库表单
const stockOutDialogVisible = ref(false)
const stockOutForm = reactive({
  productId: '',
  sourceWarehouseId: '',
  customerId: '',
  company: '宜宾市翠屏区固砂建材经营部(工商户)', // 默认选择第一个公司
  quantity: 1,
  unitPrice: 0,
  batchNumber: '',
  movementDate: new Date().toISOString().split('T')[0],
  reason: '销售出库',
  remarks: ''
})

// 获取出入库记录列表
const fetchStockMovements = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      query: searchQuery.value,
      movementType: filterType.value
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const res = await getStockMovements(params)
    stockMovementList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('Failed to fetch stock movements:', error)
    ElMessage.error('获取出入库记录失败')
  } finally {
    loading.value = false
  }
}

// 获取产品选项
const fetchProductOptions = async () => {
  try {
    const res = await getProducts({ pageSize: 100 })
    productOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch products:', error)
  }
}

// 获取仓库选项
const fetchWarehouseOptions = async () => {
  try {
    const res = await getWarehouses({ pageSize: 100 })
    warehouseOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch warehouses:', error)
  }
}

// 获取客户选项
const fetchCustomerOptions = async () => {
  try {
    const res = await getCustomers({ pageSize: 100 })
    customerOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch customers:', error)
  }
}

// 处理产品变更
const handleProductChange = () => {
  console.log('产品变更:', stockOutForm.productId)

  // 设置单价
  const product = productOptions.value.find(p => p.id === stockOutForm.productId)
  if (product) {
    selectedProduct.value = product
    stockOutForm.unitPrice = product.price
    console.log('选中产品:', product.name, '单价:', product.price)
  } else {
    selectedProduct.value = null
    console.log('未找到选中产品')
  }

  // 如果同时选择了产品和仓库，则检查库存
  if (stockOutForm.productId && stockOutForm.sourceWarehouseId) {
    checkInventory()
  } else {
    // 重置库存和批次信息
    currentStock.value = 0
    availableBatches.value = []
  }
}

// 处理仓库变更
const handleWarehouseChange = () => {
  console.log('仓库变更:', stockOutForm.sourceWarehouseId)

  // 如果同时选择了产品和仓库，则检查库存
  if (stockOutForm.productId && stockOutForm.sourceWarehouseId) {
    checkInventory()
  } else {
    // 重置库存和批次信息
    currentStock.value = 0
    availableBatches.value = []
  }
}

// 检查库存
const checkInventory = async () => {
  console.log('检查库存 - 产品ID:', stockOutForm.productId, '仓库ID:', stockOutForm.sourceWarehouseId)

  if (stockOutForm.productId && stockOutForm.sourceWarehouseId) {
    try {
      console.log('开始获取产品库存')
      const res = await getProductInventory(stockOutForm.productId)
      console.log('获取产品库存结果:', res)

      const inventories = res.filter(i => i.warehouseId === stockOutForm.sourceWarehouseId)
      console.log('过滤后的库存记录:', inventories)

      // 计算总库存
      currentStock.value = inventories.reduce((total, inv) => total + inv.quantity, 0)
      console.log('计算总库存:', currentStock.value)

      // 获取可用批次
      availableBatches.value = inventories.map(inv => ({
        batchNumber: inv.batchNumber || '默认批次',
        quantity: inv.quantity
      })).filter(batch => batch.quantity > 0)

      // 按批次号排序（通常年月日格式的批次号可以按字符串排序）
      availableBatches.value.sort((a, b) => a.batchNumber.localeCompare(b.batchNumber))

      console.log('可用批次:', availableBatches.value)

      // 如果库存为0，显示警告
      if (currentStock.value <= 0) {
        ElMessage.warning('当前产品在选择的仓库中没有库存')
      }

      // 如果当前设置的出库数量大于库存，调整为库存量
      if (stockOutForm.quantity > currentStock.value && currentStock.value > 0) {
        stockOutForm.quantity = currentStock.value
        console.log('调整出库数量为当前库存:', currentStock.value)
      }
    } catch (error) {
      console.error('检查库存失败:', error)
      if (error.response) {
        console.error('错误响应:', error.response.data)
      }
      currentStock.value = 0
      availableBatches.value = []
      ElMessage.error('获取库存信息失败')
    }
  } else {
    console.log('产品ID或仓库ID未设置，重置库存信息')
    currentStock.value = 0
    availableBatches.value = []
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchStockMovements()
}

// 处理刷新
const handleRefresh = () => {
  searchQuery.value = ''
  filterType.value = ''
  dateRange.value = []
  currentPage.value = 1
  fetchStockMovements()
}

// 处理出库
const handleStockOut = () => {
  console.log('出库按钮被点击')
  // 先重置当前库存和选中产品
  currentStock.value = 0
  selectedProduct.value = null
  availableBatches.value = []

  const today = new Date().toISOString().split('T')[0]
  Object.assign(stockOutForm, {
    productId: '',
    sourceWarehouseId: '',
    customerId: '',
    company: '宜宾市翠屏区固砂建材经营部(工商户)', // 默认选择第一个公司
    quantity: 1,
    unitPrice: 0,
    batchNumber: '',
    movementDate: today,
    reason: '销售出库',
    remarks: ''
  })

  // 确保对话框显示
  stockOutDialogVisible.value = true
  console.log('出库对话框显示状态:', stockOutDialogVisible.value)
}

// 生成批次号（年月日格式）
const generateBatchNumber = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

// 生成并设置批次号
const generateAndSetBatchNumber = () => {
  stockInForm.batchNumber = generateBatchNumber()
}

// 处理入库
const handleStockIn = () => {
  const today = new Date().toISOString().split('T')[0]
  Object.assign(stockInForm, {
    productId: '',
    sourceWarehouseId: '',
    quantity: 1,
    unitPrice: 0,
    batchNumber: '', // 初始为空，用户可以手动输入或点击生成
    movementDate: today,
    reason: '采购入库',
    remarks: ''
  })
  stockInDialogVisible.value = true
}

// 提交入库
const submitStockIn = async () => {
  if (!stockInForm.productId || !stockInForm.sourceWarehouseId || !stockInForm.quantity || !stockInForm.movementDate) {
    ElMessage.warning('请填写必填项')
    return
  }

  try {
    await createStockIn(stockInForm)
    ElMessage.success('入库成功')
    stockInDialogVisible.value = false
    fetchStockMovements()
  } catch (error) {
    console.error('Failed to create stock in:', error)
    ElMessage.error('入库失败')
  }
}

// 提交出库
const submitStockOut = async () => {
  console.log('提交出库表单:', stockOutForm)

  if (!stockOutForm.productId || !stockOutForm.sourceWarehouseId || !stockOutForm.quantity || !stockOutForm.movementDate) {
    ElMessage.warning('请填写必填项')
    return
  }

  if (currentStock.value <= 0) {
    ElMessage.warning('当前库存不足，无法出库')
    return
  }

  if (stockOutForm.quantity > currentStock.value) {
    ElMessage.warning(`出库数量不能大于当前库存 (${currentStock.value})`)
    return
  }

  try {
    console.log('开始调用出库API')
    const response = await createStockOut(stockOutForm)
    console.log('出库API响应:', response)
    ElMessage.success('出库成功')
    stockOutDialogVisible.value = false
    fetchStockMovements()
  } catch (error) {
    console.error('出库失败:', error)
    if (error.response) {
      console.error('错误响应:', error.response.data)
      ElMessage.error(`出库失败: ${error.response.data.message || '未知错误'}`)
    } else if (error.request) {
      console.error('无响应:', error.request)
      ElMessage.error('出库失败: 服务器无响应')
    } else {
      console.error('请求配置错误:', error.message)
      ElMessage.error(`出库失败: ${error.message}`)
    }
  }
}

// 处理查看
const handleView = (row) => {
  router.push(`/stock-movements/${row.id}`)
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  switch (type) {
    case 1: return 'success' // 入库
    case 2: return 'danger'  // 出库
    case 3: return 'warning' // 调拨
    case 4: return 'info'    // 调整
    default: return ''
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchStockMovements()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchStockMovements()
}

// 注释掉自动更新批次号的监听器，现在由用户手动控制
// watch(() => stockInForm.movementDate, (newDate) => {
//   if (newDate) {
//     // 从日期字符串中提取年月日
//     const [year, month, day] = newDate.split('-')
//     stockInForm.batchNumber = `${year}${month}${day}`
//   }
// })

onMounted(() => {
  fetchStockMovements()
  fetchProductOptions()
  fetchWarehouseOptions()
  fetchCustomerOptions()
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  gap: 10px;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.left-actions {
  display: flex;
  gap: 10px;
}

.form-item-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}
</style>
