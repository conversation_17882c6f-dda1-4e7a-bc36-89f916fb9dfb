<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">客户详情</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
        <el-button type="danger" @click="handleDelete">
          <el-icon><Delete /></el-icon>删除
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="customer.isActive ? 'success' : 'danger'">
            {{ customer.isActive ? '启用' : '禁用' }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="客户ID">{{ customer.id }}</el-descriptions-item>
        <el-descriptions-item label="客户编码">{{ customer.code }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ customer.name }}</el-descriptions-item>
        <el-descriptions-item label="客户类型">{{ customer.type }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ customer.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ customer.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="电子邮箱">{{ customer.email || '无' }}</el-descriptions-item>
        <el-descriptions-item label="销售">{{ customer.salesPerson || '无' }}</el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ customer.address || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ customer.remark || '无' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(customer.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(customer.updatedAt) }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>订单统计</span>
          <el-button type="primary" size="small" @click="viewAllOrders">查看全部订单</el-button>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">总订单数</div>
            <div class="stat-value">{{ stats.totalOrders }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">总金额</div>
            <div class="stat-value">¥{{ stats.totalAmount.toFixed(2) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">本月订单</div>
            <div class="stat-value">{{ stats.monthOrders }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">本月金额</div>
            <div class="stat-value">¥{{ stats.monthAmount.toFixed(2) }}</div>
          </div>
        </el-col>
      </el-row>

      <div class="chart-container">
        <div ref="orderChartRef" class="chart"></div>
      </div>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>最近订单</span>
        </div>
      </template>

      <el-table :data="recentOrders" stripe style="width: 100%">
        <el-table-column prop="id" label="订单号" width="100" />
        <el-table-column prop="code" label="订单编号" width="150" />
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="scope">
            ¥{{ scope.row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建日期" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewOrder(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Edit, Delete } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getCustomerById, deleteCustomer } from '../../api/customer'
import { getCustomerOrders, getCustomerStats } from '../../api/order'

const router = useRouter()
const route = useRoute()
const loading = ref(true)
const customer = ref({})
const recentOrders = ref([])
const stats = ref({
  totalOrders: 0,
  totalAmount: 0,
  monthOrders: 0,
  monthAmount: 0
})
const orderChartRef = ref(null)
let orderChart = null

// 获取客户详情
const fetchCustomerDetail = async () => {
  loading.value = true
  try {
    const customerId = route.params.id
    const res = await getCustomerById(customerId)
    customer.value = res.data
  } catch (error) {
    console.error('Failed to fetch customer details:', error)
    ElMessage.error('获取客户详情失败')
  } finally {
    loading.value = false
  }
}

// 获取客户订单
const fetchCustomerOrders = async () => {
  try {
    const customerId = route.params.id
    const res = await getCustomerOrders(customerId, { limit: 5 })
    recentOrders.value = res.data
  } catch (error) {
    console.error('Failed to fetch customer orders:', error)
    ElMessage.error('获取客户订单失败')
  }
}

// 获取客户统计数据
const fetchCustomerStats = async () => {
  try {
    const customerId = route.params.id
    const res = await getCustomerStats(customerId)
    stats.value = res.data
    initOrderChart(res.data.monthlyStats)
  } catch (error) {
    console.error('Failed to fetch customer stats:', error)
    ElMessage.error('获取客户统计数据失败')
  }
}

// 初始化订单图表
const initOrderChart = (monthlyData) => {
  if (orderChart) {
    orderChart.dispose()
  }

  orderChart = echarts.init(orderChartRef.value)

  // 处理月度数据
  const months = monthlyData.map(item => item.month)
  const orderCounts = monthlyData.map(item => item.count)
  const orderAmounts = monthlyData.map(item => item.amount)

  const option = {
    title: {
      text: '近6个月订单统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['订单数量', '订单金额'],
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: '订单数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '订单金额',
        position: 'right',
        axisLabel: {
          formatter: '¥{value}'
        }
      }
    ],
    series: [
      {
        name: '订单数量',
        type: 'bar',
        data: orderCounts,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '订单金额',
        type: 'line',
        yAxisIndex: 1,
        data: orderAmounts,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }

  orderChart.setOption(option)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 获取订单状态对应的标签类型
const getOrderStatusType = (status) => {
  const statusMap = {
    '已完成': 'success',
    '处理中': 'primary',
    '待付款': 'warning',
    '已发货': 'info',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

// 编辑客户
const handleEdit = () => {
  router.push(`/customers/${customer.value.id}/edit`)
}

// 删除客户
const handleDelete = () => {
  ElMessageBox.confirm(
    `确定要删除客户 "${customer.value.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteCustomer(customer.value.id)
      ElMessage.success('删除成功')
      router.push('/customers')
    } catch (error) {
      console.error('Failed to delete customer:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 查看所有订单
const viewAllOrders = () => {
  router.push(`/orders?customerId=${customer.value.id}`)
}

// 查看订单详情
const viewOrder = (order) => {
  router.push(`/orders/${order.id}`)
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 监听窗口大小变化，重新调整图表大小
window.addEventListener('resize', () => {
  orderChart?.resize()
})

onMounted(() => {
  fetchCustomerDetail()
  fetchCustomerOrders()
  fetchCustomerStats()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-card {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.chart-container {
  margin-top: 20px;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
