<template>
  <div class="page-container">
    <div class="page-title">客户管理</div>

    <div class="action-bar">
      <div class="left-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>添加客户
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>导出
        </el-button>
      </div>

      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户..."
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="el-input__icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select v-model="filterStatus" placeholder="状态" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="启用" value="true" />
          <el-option label="禁用" value="false" />
        </el-select>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="customerList"
      border
      class="data-table"
    >

      <el-table-column prop="code" label="客户编码" width="120" />
      <el-table-column prop="name" label="客户名称" min-width="150" />
      <el-table-column prop="contactPerson" label="联系人" width="120" />
      <el-table-column prop="contactPhone" label="联系电话" width="150" />
      <el-table-column prop="salesPerson" label="销售" width="120" />
      <el-table-column prop="type" label="客户类型" width="120" />
      <el-table-column prop="isActive" label="状态" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
            {{ scope.row.isActive ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Download, Search } from '@element-plus/icons-vue'
import { getCustomers, deleteCustomer } from '../../api/customer'

const router = useRouter()
const loading = ref(false)
const customerList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filterStatus = ref('')

// 获取客户列表
const fetchCustomers = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      query: searchQuery.value,
      isActive: filterStatus.value
    }

    const res = await getCustomers(params)
    customerList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('Failed to fetch customers:', error)
    ElMessage.error('获取客户列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchCustomers()
}

// 处理刷新
const handleRefresh = () => {
  searchQuery.value = ''
  filterStatus.value = ''
  currentPage.value = 1
  fetchCustomers()
}

// 处理添加
const handleAdd = () => {
  router.push('/customers/create')
}

// 处理编辑
const handleEdit = (row) => {
  router.push(`/customers/${row.id}/edit`)
}

// 处理查看
const handleView = (row) => {
  router.push(`/customers/${row.id}`)
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除客户 "${row.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteCustomer(row.id)
      ElMessage.success('删除成功')
      fetchCustomers()
    } catch (error) {
      console.error('Failed to delete customer:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 处理导出
const handleExport = () => {
  ElMessage.success('导出功能待实现')
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchCustomers()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchCustomers()
}

onMounted(() => {
  fetchCustomers()
})
</script>

<style scoped>
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.left-actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  display: flex;
  gap: 10px;
  width: 400px;
}

.data-table {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}
</style>
