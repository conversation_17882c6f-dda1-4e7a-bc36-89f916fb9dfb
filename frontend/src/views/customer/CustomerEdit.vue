<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">编辑客户</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
      </div>
    </div>

    <el-card class="form-card" v-loading="loading">
      <el-form
        ref="customerFormRef"
        :model="customerForm"
        :rules="customerRules"
        label-width="100px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户编码" prop="code">
              <el-input v-model="customerForm.code" placeholder="请输入客户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="name">
              <el-input v-model="customerForm.name" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户类型" prop="type">
              <el-select v-model="customerForm.type" placeholder="请选择客户类型" style="width: 100%">
                <el-option label="企业" value="企业" />
                <el-option label="个人" value="个人" />
                <el-option label="政府" value="政府" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="customerForm.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="customerForm.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="customerForm.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="销售人员" prop="salesPersonId">
              <el-select v-model="customerForm.salesPersonId" placeholder="请选择销售人员" style="width: 100%">
                <el-option
                  v-for="user in salesPersonOptions"
                  :key="user.id"
                  :label="user.realName"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="isActive">
              <el-switch
                v-model="customerForm.isActive"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地址" prop="address">
          <el-input v-model="customerForm.address" placeholder="请输入地址" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="customerForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存修改</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import { getCustomerById, updateCustomer } from '../../api/customer'
import { getUsers } from '../../api/user'

const router = useRouter()
const route = useRoute()
const customerFormRef = ref(null)
const loading = ref(true)
const submitting = ref(false)
const salesPersonOptions = ref([])

// 客户表单数据
const customerForm = reactive({
  id: '',
  code: '',
  name: '',
  type: '',
  contactPerson: '',
  contactPhone: '',
  email: '',
  salesPerson: '',
  salesPersonId: null,
  address: '',
  isActive: true,
  remark: ''
})

// 获取销售人员列表
const fetchSalesPersons = async () => {
  try {
    const res = await getUsers()
    salesPersonOptions.value = res.data || []
  } catch (error) {
    console.error('Failed to fetch sales persons:', error)
    ElMessage.error('获取销售人员列表失败')
  }
}

// 表单验证规则
const customerRules = {
  code: [
    { required: true, message: '请输入客户编码', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择客户类型', trigger: 'change' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { pattern: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 获取客户详情
const fetchCustomerDetail = async () => {
  loading.value = true
  try {
    const customerId = route.params.id
    const res = await getCustomerById(customerId)

    // 填充表单数据
    Object.assign(customerForm, res.data)
  } catch (error) {
    console.error('Failed to fetch customer details:', error)
    ElMessage.error('获取客户详情失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!customerFormRef.value) return

  await customerFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await updateCustomer(customerForm.id, customerForm)
        ElMessage.success('更新客户成功')
        router.push(`/customers/${customerForm.id}`)
      } catch (error) {
        console.error('Failed to update customer:', error)
        ElMessage.error('更新客户失败')
      } finally {
        submitting.value = false
      }
    } else {
      ElMessage.warning('请正确填写表单')
    }
  })
}

// 重置表单
const resetForm = () => {
  fetchCustomerDetail()
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchSalesPersons()
  fetchCustomerDetail()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-card {
  margin-bottom: 20px;
}
</style>
