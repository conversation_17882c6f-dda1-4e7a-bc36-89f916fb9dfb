<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">产品详情</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
        <el-button type="danger" @click="handleDelete">
          <el-icon><Delete /></el-icon>删除
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="product.isActive ? 'success' : 'danger'">
            {{ product.isActive ? '启用' : '禁用' }}
          </el-tag>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品ID">{{ product.id }}</el-descriptions-item>
        <el-descriptions-item label="产品编码">{{ product.code }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ product.name }}</el-descriptions-item>
        <el-descriptions-item label="产品类别">{{ product.category }}</el-descriptions-item>
        <el-descriptions-item label="规格">{{ product.specification }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ product.unit }}</el-descriptions-item>
        <el-descriptions-item label="价格">¥{{ product.price?.toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="条形码">{{ product.barcode || '无' }}</el-descriptions-item>
        <el-descriptions-item label="最低库存">{{ product.minStock }}</el-descriptions-item>
        <el-descriptions-item label="最高库存">{{ product.maxStock }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(product.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(product.updatedAt) }}</el-descriptions-item>
        <el-descriptions-item label="产品描述" :span="2">
          {{ product.description || '无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>库存信息</span>
          <el-button type="primary" size="small" @click="viewInventory">查看库存记录</el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="inventory-stat">
            <div class="inventory-stat-title">当前库存</div>
            <div class="inventory-stat-value" :class="getInventoryStatusClass(inventory.current, product.minStock, product.maxStock)">
              {{ inventory.current }}
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="inventory-stat">
            <div class="inventory-stat-title">入库总量</div>
            <div class="inventory-stat-value inventory-in">{{ inventory.totalIn }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="inventory-stat">
            <div class="inventory-stat-title">出库总量</div>
            <div class="inventory-stat-value inventory-out">{{ inventory.totalOut }}</div>
          </div>
        </el-col>
      </el-row>
      
      <div class="inventory-chart-container">
        <div ref="inventoryChartRef" class="inventory-chart"></div>
      </div>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>最近出入库记录</span>
          <el-button type="primary" size="small" @click="viewAllMovements">查看全部记录</el-button>
        </div>
      </template>
      
      <el-table :data="recentMovements" stripe style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === '入库' ? 'success' : 'danger'">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="date" label="日期" width="180" />
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column prop="remark" label="备注" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Edit, Delete } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getProductById, deleteProduct } from '../../api/product'
import { getProductInventory } from '../../api/inventory'
import { getProductMovements } from '../../api/stockMovement'

const router = useRouter()
const route = useRoute()
const loading = ref(true)
const product = ref({})
const inventory = ref({
  current: 0,
  totalIn: 0,
  totalOut: 0
})
const recentMovements = ref([])
const inventoryChartRef = ref(null)
let inventoryChart = null

// 获取产品详情
const fetchProductDetail = async () => {
  loading.value = true
  try {
    const productId = route.params.id
    const res = await getProductById(productId)
    product.value = res.data
  } catch (error) {
    console.error('Failed to fetch product details:', error)
    ElMessage.error('获取产品详情失败')
  } finally {
    loading.value = false
  }
}

// 获取库存信息
const fetchInventoryInfo = async () => {
  try {
    const productId = route.params.id
    const res = await getProductInventory(productId)
    inventory.value = res.data
    initInventoryChart()
  } catch (error) {
    console.error('Failed to fetch inventory info:', error)
    ElMessage.error('获取库存信息失败')
  }
}

// 获取最近出入库记录
const fetchRecentMovements = async () => {
  try {
    const productId = route.params.id
    const res = await getProductMovements(productId, { limit: 5 })
    recentMovements.value = res.data
  } catch (error) {
    console.error('Failed to fetch recent movements:', error)
    ElMessage.error('获取出入库记录失败')
  }
}

// 初始化库存图表
const initInventoryChart = () => {
  if (inventoryChart) {
    inventoryChart.dispose()
  }
  
  inventoryChart = echarts.init(inventoryChartRef.value)
  
  // 模拟过去6个月的数据
  const months = ['1月', '2月', '3月', '4月', '5月', '6月']
  const inData = [12, 19, 15, 23, 18, 10]
  const outData = [8, 14, 13, 19, 23, 12]
  
  const option = {
    title: {
      text: '近6个月出入库趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['入库', '出库'],
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '入库',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: inData,
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '出库',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: outData,
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  }
  
  inventoryChart.setOption(option)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 获取库存状态样式
const getInventoryStatusClass = (current, min, max) => {
  if (current <= min) return 'inventory-low'
  if (current >= max) return 'inventory-high'
  return 'inventory-normal'
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 编辑产品
const handleEdit = () => {
  router.push(`/products/${product.value.id}/edit`)
}

// 删除产品
const handleDelete = () => {
  ElMessageBox.confirm(
    `确定要删除产品 "${product.value.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteProduct(product.value.id)
      ElMessage.success('删除成功')
      router.push('/products')
    } catch (error) {
      console.error('Failed to delete product:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 查看库存记录
const viewInventory = () => {
  router.push(`/inventory?productId=${product.value.id}`)
}

// 查看全部出入库记录
const viewAllMovements = () => {
  router.push(`/stock-movements?productId=${product.value.id}`)
}

// 监听窗口大小变化，重新调整图表大小
window.addEventListener('resize', () => {
  inventoryChart?.resize()
})

onMounted(() => {
  fetchProductDetail()
  fetchInventoryInfo()
  fetchRecentMovements()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inventory-stat {
  text-align: center;
  padding: 20px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.inventory-stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.inventory-stat-value {
  font-size: 28px;
  font-weight: bold;
}

.inventory-normal {
  color: #409EFF;
}

.inventory-low {
  color: #F56C6C;
}

.inventory-high {
  color: #E6A23C;
}

.inventory-in {
  color: #67C23A;
}

.inventory-out {
  color: #F56C6C;
}

.inventory-chart-container {
  margin-top: 20px;
  height: 300px;
}

.inventory-chart {
  width: 100%;
  height: 100%;
}
</style>
