<template>
  <div class="page-container">
    <div class="page-title">产品管理</div>

    <div class="action-bar">
      <div class="left-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>添加产品
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>导出
        </el-button>
      </div>

      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索产品..."
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="el-input__icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select v-model="filterStatus" placeholder="状态" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="启用" value="true" />
          <el-option label="禁用" value="false" />
        </el-select>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="productList"
      border
      class="data-table"
    >

      <el-table-column prop="code" label="产品编码" width="120" />
      <el-table-column prop="name" label="产品名称" min-width="180" />
      <el-table-column prop="specification" label="规格" width="120" />
      <el-table-column prop="unit" label="单位" width="80" />
      <el-table-column prop="category" label="类别" width="120" />
      <el-table-column prop="minStock" label="最小库存" width="100">
        <template #default="scope">
          {{ scope.row.minStock.toFixed(2) }} {{ scope.row.unit }}
        </template>
      </el-table-column>
      <el-table-column prop="maxStock" label="最大库存" width="100">
        <template #default="scope">
          {{ scope.row.maxStock.toFixed(2) }} {{ scope.row.unit }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格" width="100">
        <template #default="scope">
          {{ scope.row.price.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="isActive" label="状态" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
            {{ scope.row.isActive ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 产品表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加产品' : '编辑产品'"
      width="600px"
    >
      <product-form
        ref="productFormRef"
        :product="currentProduct"
        :type="dialogType"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Download, Search } from '@element-plus/icons-vue'
import { getProducts, deleteProduct } from '../../api/product'
import ProductForm from '../../components/product/ProductForm.vue'

const router = useRouter()
const loading = ref(false)
const productList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filterStatus = ref('')
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' or 'edit'
const currentProduct = ref({})
const productFormRef = ref(null)

// 获取产品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      query: searchQuery.value,
      isActive: filterStatus.value
    }

    const res = await getProducts(params)
    productList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('Failed to fetch products:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchProducts()
}

// 处理刷新
const handleRefresh = () => {
  searchQuery.value = ''
  filterStatus.value = ''
  currentPage.value = 1
  fetchProducts()
}

// 处理添加
const handleAdd = () => {
  dialogType.value = 'add'
  currentProduct.value = {
    code: '',
    name: '',
    description: '',
    specification: '',
    unit: '',
    minStock: 0,
    maxStock: 0,
    isActive: true,
    barcode: '',
    category: '',
    price: 0
  }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  dialogType.value = 'edit'
  currentProduct.value = { ...row }
  dialogVisible.value = true
}

// 处理查看
const handleView = (row) => {
  router.push(`/products/${row.id}`)
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除产品 "${row.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteProduct(row.id)
      ElMessage.success('删除成功')
      fetchProducts()
    } catch (error) {
      console.error('Failed to delete product:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 处理保存
const handleSave = async () => {
  if (!productFormRef.value) return

  const valid = await productFormRef.value.validate()
  if (valid) {
    const success = await productFormRef.value.save()
    if (success) {
      dialogVisible.value = false
      fetchProducts()
    }
  }
}

// 处理导出
const handleExport = () => {
  ElMessage.success('导出功能待实现')
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchProducts()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchProducts()
}

onMounted(() => {
  fetchProducts()
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
