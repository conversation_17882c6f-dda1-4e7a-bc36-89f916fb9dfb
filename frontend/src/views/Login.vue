<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-title">仓库管理系统</div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            prefix-icon="User"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            :type="passwordVisible ? 'text' : 'password'"
            placeholder="密码"
            prefix-icon="Lock"
            @keyup.enter="handleLogin"
          >
            <template #suffix>
              <el-tooltip
                :content="passwordVisible ? '隐藏密码' : '显示密码'"
                placement="top"
              >
                <div
                  class="password-eye-container"
                  @click="passwordVisible = !passwordVisible"
                >
                  <el-icon
                    class="password-eye"
                  >
                    <component :is="passwordVisible ? 'View' : 'Hide'" />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>

        <!-- 加密测试区域 -->
        <div v-if="showEncryptTest" class="encrypt-test-area">
          <el-divider>密码加密测试</el-divider>
          <div class="test-result">
            <p><strong>原始密码:</strong> {{ loginForm.password }}</p>
            <p><strong>加密结果:</strong> {{ encryptedPassword }}</p>
          </div>
          <el-button size="small" @click="testEncryption">测试加密</el-button>
        </div>
      </el-form>

      <!-- 开发模式切换 -->
      <div class="dev-mode">
        <el-switch
          v-model="showEncryptTest"
          active-text="显示加密测试"
          inactive-text="隐藏加密测试"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user'
import { ElMessage } from 'element-plus'
import { User, Lock, View, Hide } from '@element-plus/icons-vue'
import { encryptPassword } from '../utils/crypto'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref(null)
const loading = ref(false)
const showEncryptTest = ref(false)
const encryptedPassword = ref('')
const passwordVisible = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

// 测试密码加密
const testEncryption = () => {
  if (!loginForm.password) {
    ElMessage.warning('请先输入密码')
    return
  }

  try {
    encryptedPassword.value = encryptPassword(loginForm.password)
    ElMessage.success('加密成功')
  } catch (error) {
    console.error('加密失败:', error)
    ElMessage.error('加密失败: ' + error.message)
  }
}

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        console.log('登录表单数据:', loginForm)

        // 使用Pinia store进行登录
        const result = await userStore.loginUser(
          loginForm.username,
          loginForm.password
        )

        if (result) {
          ElMessage.success('登录成功')
          console.log('登录成功，准备跳转到首页')
          // 确保token已保存
          console.log('当前token:', localStorage.getItem('token'))
          console.log('当前认证状态:', userStore.isAuthenticated)

          // 强制跳转到首页
          window.location.href = '/'
        } else {
          ElMessage.error('登录失败，请检查用户名和密码')
        }
      } catch (error) {
        console.error('Login error:', error)
        ElMessage.error('登录失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.login-card {
  width: 400px;
  padding: 40px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 30px;
  color: #303133;
}

.login-form {
  margin-top: 20px;
}

.login-button {
  width: 100%;
}

.encrypt-test-area {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.test-result {
  margin: 10px 0;
  font-size: 12px;
  word-break: break-all;
}

.dev-mode {
  margin-top: 20px;
  text-align: center;
  font-size: 12px;
}

.password-eye-container {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 0;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
  background-color: #f5f7fa;
}

.password-eye-container:hover {
  background-color: #f0f0f0;
  border-color: #c0c4cc;
}

.password-eye {
  font-size: 20px;
  color: #303133;
}

.password-eye:hover {
  color: #409EFF;
}
</style>
