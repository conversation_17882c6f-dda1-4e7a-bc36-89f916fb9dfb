<template>
  <div class="page-container">
    <div class="page-header">
      <div class="page-title">库存详情</div>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
        <el-button type="primary" @click="handleStockIn">
          <el-icon><Plus /></el-icon>入库
        </el-button>
        <el-button type="warning" @click="handleStockOut">
          <el-icon><Minus /></el-icon>出库
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>产品信息</span>
          <el-button type="primary" size="small" @click="viewProduct">查看产品详情</el-button>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品ID">{{ inventory.productId }}</el-descriptions-item>
        <el-descriptions-item label="产品编码">{{ inventory.productCode }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ inventory.productName }}</el-descriptions-item>
        <el-descriptions-item label="产品类别">{{ inventory.category }}</el-descriptions-item>
        <el-descriptions-item label="规格">{{ inventory.specification }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ inventory.unit }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>批次信息</span>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="批次号">
          <el-tag v-if="inventory.batchNumber" type="success">{{ inventory.batchNumber }}</el-tag>
          <el-tag v-else type="info">无批次</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="库位">{{ inventory.location || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="最后检查日期">{{ inventory.lastCheckDate || '未检查' }}</el-descriptions-item>
        <el-descriptions-item label="过期日期">{{ inventory.expiryDate || '未设置' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>库存状态</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="inventory-stat">
            <div class="inventory-stat-title">当前库存</div>
            <div class="inventory-stat-value" :class="getInventoryStatusClass(inventory.currentStock, inventory.minStock, inventory.maxStock)">
              {{ inventory.currentStock }}
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="inventory-stat">
            <div class="inventory-stat-title">最低库存</div>
            <div class="inventory-stat-value inventory-min">{{ inventory.minStock }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="inventory-stat">
            <div class="inventory-stat-title">最高库存</div>
            <div class="inventory-stat-value inventory-max">{{ inventory.maxStock }}</div>
          </div>
        </el-col>
      </el-row>

      <el-progress
        :percentage="getInventoryPercentage(inventory.currentStock, inventory.maxStock)"
        :status="getInventoryStatus(inventory.currentStock, inventory.minStock, inventory.maxStock)"
        :stroke-width="20"
        class="inventory-progress"
      />

      <div class="inventory-status-text">
        库存状态:
        <el-tag :type="getInventoryTagType(inventory.currentStock, inventory.minStock, inventory.maxStock)">
          {{ getInventoryStatusText(inventory.currentStock, inventory.minStock, inventory.maxStock) }}
        </el-tag>
      </div>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>出入库记录</span>
          <div>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
            <el-button type="primary" @click="fetchMovements">查询</el-button>
          </div>
        </div>
      </template>

      <el-table :data="movements" stripe style="width: 100%" v-loading="loadingMovements">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === '入库' ? 'success' : 'danger'">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="beforeStock" label="操作前库存" width="120" />
        <el-table-column prop="afterStock" label="操作后库存" width="120" />
        <el-table-column prop="date" label="日期" width="180" />
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column prop="remark" label="备注" min-width="200" />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 入库对话框 -->
    <el-dialog v-model="stockInDialogVisible" title="入库操作" width="500px">
      <el-form :model="stockForm" :rules="stockRules" ref="stockFormRef" label-width="100px">
        <el-form-item label="入库数量" prop="quantity">
          <el-input-number v-model="stockForm.quantity" :min="1" style="width: 100%" />
        </el-form-item>
        <el-form-item label="操作人" prop="operator">
          <el-input v-model="stockForm.operator" placeholder="请输入操作人" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="stockForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="stockInDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStockIn" :loading="submitting">确认入库</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 出库对话框 -->
    <el-dialog v-model="stockOutDialogVisible" title="出库操作" width="500px">
      <el-form :model="stockForm" :rules="stockRules" ref="stockFormRef" label-width="100px">
        <el-form-item label="出库数量" prop="quantity">
          <el-input-number v-model="stockForm.quantity" :min="1" :max="inventory.currentStock" style="width: 100%" />
        </el-form-item>
        <el-form-item label="操作人" prop="operator">
          <el-input v-model="stockForm.operator" placeholder="请输入操作人" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="stockForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="stockOutDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStockOut" :loading="submitting">确认出库</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Plus, Minus } from '@element-plus/icons-vue'
import { getInventoryById } from '../../api/inventory'
import { getInventoryMovements, createStockIn, createStockOut } from '../../api/stockMovement'

const router = useRouter()
const route = useRoute()
const loading = ref(true)
const loadingMovements = ref(false)
const submitting = ref(false)
const stockInDialogVisible = ref(false)
const stockOutDialogVisible = ref(false)
const stockFormRef = ref(null)
const inventory = ref({
  productId: '',
  productCode: '',
  productName: '',
  category: '',
  specification: '',
  unit: '',
  currentStock: 0,
  minStock: 0,
  maxStock: 0
})
const movements = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dateRange = ref([])

// 出入库表单
const stockForm = reactive({
  quantity: 1,
  operator: '',
  remark: ''
})

// 表单验证规则
const stockRules = {
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  operator: [
    { required: true, message: '请输入操作人', trigger: 'blur' }
  ]
}

// 获取库存详情
const fetchInventoryDetail = async () => {
  loading.value = true
  try {
    const inventoryId = route.params.id
    const res = await getInventoryById(inventoryId)
    inventory.value = res.data
  } catch (error) {
    console.error('Failed to fetch inventory details:', error)
    ElMessage.error('获取库存详情失败')
  } finally {
    loading.value = false
  }
}

// 获取出入库记录
const fetchMovements = async () => {
  loadingMovements.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      productId: inventory.value.productId
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const res = await getInventoryMovements(params)
    movements.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('Failed to fetch movements:', error)
    ElMessage.error('获取出入库记录失败')
  } finally {
    loadingMovements.value = false
  }
}

// 处理入库
const handleStockIn = () => {
  stockForm.quantity = 1
  stockForm.operator = ''
  stockForm.remark = ''
  stockInDialogVisible.value = true
}

// 处理出库
const handleStockOut = () => {
  if (inventory.value.currentStock <= 0) {
    ElMessage.warning('当前库存为0，无法出库')
    return
  }

  stockForm.quantity = 1
  stockForm.operator = ''
  stockForm.remark = ''
  stockOutDialogVisible.value = true
}

// 提交入库
const submitStockIn = async () => {
  if (!stockFormRef.value) return

  await stockFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await createStockIn({
          productId: inventory.value.productId,
          quantity: stockForm.quantity,
          operator: stockForm.operator,
          remark: stockForm.remark
        })

        ElMessage.success('入库成功')
        stockInDialogVisible.value = false

        // 刷新数据
        await fetchInventoryDetail()
        await fetchMovements()
      } catch (error) {
        console.error('Failed to stock in:', error)
        ElMessage.error('入库失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 提交出库
const submitStockOut = async () => {
  if (!stockFormRef.value) return

  await stockFormRef.value.validate(async (valid) => {
    if (valid) {
      if (stockForm.quantity > inventory.value.currentStock) {
        ElMessage.warning(`当前库存只有 ${inventory.value.currentStock} ${inventory.value.unit}，无法出库 ${stockForm.quantity} ${inventory.value.unit}`)
        return
      }

      submitting.value = true
      try {
        await createStockOut({
          productId: inventory.value.productId,
          quantity: stockForm.quantity,
          operator: stockForm.operator,
          remark: stockForm.remark
        })

        ElMessage.success('出库成功')
        stockOutDialogVisible.value = false

        // 刷新数据
        await fetchInventoryDetail()
        await fetchMovements()
      } catch (error) {
        console.error('Failed to stock out:', error)
        ElMessage.error('出库失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 获取库存状态百分比
const getInventoryPercentage = (current, max) => {
  if (max <= 0) return 0
  const percentage = (current / max) * 100
  return Math.min(percentage, 100)
}

// 获取库存状态
const getInventoryStatus = (current, min, max) => {
  if (current <= min) return 'exception'
  if (current >= max) return 'warning'
  return 'success'
}

// 获取库存状态文本
const getInventoryStatusText = (current, min, max) => {
  if (current <= min) return '库存不足'
  if (current >= max) return '库存过多'
  return '库存正常'
}

// 获取库存状态标签类型
const getInventoryTagType = (current, min, max) => {
  if (current <= min) return 'danger'
  if (current >= max) return 'warning'
  return 'success'
}

// 获取库存状态样式
const getInventoryStatusClass = (current, min, max) => {
  if (current <= min) return 'inventory-low'
  if (current >= max) return 'inventory-high'
  return 'inventory-normal'
}

// 查看产品详情
const viewProduct = () => {
  router.push(`/products/${inventory.value.productId}`)
}

// 处理日期变化
const handleDateChange = () => {
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchMovements()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchMovements()
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchInventoryDetail()
  fetchMovements()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inventory-stat {
  text-align: center;
  padding: 20px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.inventory-stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.inventory-stat-value {
  font-size: 28px;
  font-weight: bold;
}

.inventory-normal {
  color: #409EFF;
}

.inventory-low {
  color: #F56C6C;
}

.inventory-high {
  color: #E6A23C;
}

.inventory-min {
  color: #F56C6C;
}

.inventory-max {
  color: #E6A23C;
}

.inventory-progress {
  margin-top: 20px;
}

.inventory-status-text {
  margin-top: 10px;
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
