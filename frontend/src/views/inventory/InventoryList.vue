<template>
  <div class="page-container">
    <div class="page-title">库存管理</div>

    <div class="action-bar">
      <div class="left-actions">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>导出
        </el-button>
      </div>

      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索产品或仓库..."
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="el-input__icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select v-model="filterProductId" placeholder="产品" clearable @change="handleSearch">
          <el-option label="全部产品" value="" />
          <el-option
            v-for="product in productOptions"
            :key="product.id"
            :label="product.name"
            :value="product.id"
          />
        </el-select>

        <el-select v-model="filterWarehouseId" placeholder="仓库" clearable @change="handleSearch">
          <el-option label="全部仓库" value="" />
          <el-option
            v-for="warehouse in warehouseOptions"
            :key="warehouse.id"
            :label="warehouse.name"
            :value="warehouse.id"
          />
        </el-select>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="inventoryList"
      border
      class="data-table"
    >

      <el-table-column label="产品" min-width="180">
        <template #default="scope">
          <div>{{ scope.row.product.name }}</div>
          <div class="text-muted">{{ scope.row.product.code }}</div>
        </template>
      </el-table-column>
      <el-table-column label="仓库" width="150">
        <template #default="scope">
          <div>{{ scope.row.warehouse.name }}</div>
          <div class="text-muted">{{ scope.row.warehouse.code }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="quantity" label="数量" width="120">
        <template #default="scope">
          <span>{{ scope.row.quantity }} {{ scope.row.product.unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批次号" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.batchNumber" size="small" type="success">
            {{ scope.row.batchNumber }}
          </el-tag>
          <el-tag v-else size="small" type="info">无批次</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row)">
            {{ getStatusText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleAdjust(scope.row)">调整</el-button>
          <el-button size="small" type="success" @click="handleMove(scope.row)">移动</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 库存调整对话框 -->
    <el-dialog
      v-model="adjustDialogVisible"
      title="库存调整"
      width="500px"
    >
      <el-form :model="adjustForm" label-width="100px">
        <el-form-item label="产品">
          <div>{{ currentInventory.product?.name }}</div>
          <div class="text-muted">{{ currentInventory.product?.code }}</div>
        </el-form-item>
        <el-form-item label="仓库">
          <div>{{ currentInventory.warehouse?.name }}</div>
        </el-form-item>
        <el-form-item label="当前数量">
          <div>{{ currentInventory.quantity }} {{ currentInventory.product?.unit }}</div>
        </el-form-item>
        <el-form-item label="新数量" required>
          <el-input-number v-model="adjustForm.quantity" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="调整原因">
          <el-input v-model="adjustForm.reason" type="textarea" rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="adjustDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdjust">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 库存移动对话框 -->
    <el-dialog
      v-model="moveDialogVisible"
      title="库存移动"
      width="500px"
    >
      <el-form :model="moveForm" label-width="120px">
        <el-form-item label="产品">
          <div>{{ currentInventory.product?.name }}</div>
          <div class="text-muted">{{ currentInventory.product?.code }}</div>
        </el-form-item>
        <el-form-item label="源仓库">
          <div>{{ currentInventory.warehouse?.name }}</div>
        </el-form-item>
        <el-form-item label="目标仓库" required>
          <el-select v-model="moveForm.destinationWarehouseId" placeholder="选择目标仓库">
            <el-option
              v-for="warehouse in warehouseOptions.filter(w => w.id !== currentInventory.warehouseId)"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="当前数量">
          <div>{{ currentInventory.quantity }} {{ currentInventory.product?.unit }}</div>
        </el-form-item>
        <el-form-item label="移动数量" required>
          <el-input-number v-model="moveForm.quantity" :min="0.01" :max="currentInventory.quantity" :precision="2" />
        </el-form-item>
        <el-form-item label="移动原因">
          <el-input v-model="moveForm.reason" type="textarea" rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitMove">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, Download, Search } from '@element-plus/icons-vue'
import { getInventories } from '../../api/inventory'
import { getProducts } from '../../api/product'
import { getWarehouses } from '../../api/warehouse'
import { createStockAdjustment, createStockTransfer } from '../../api/stockMovement'

const router = useRouter()
const loading = ref(false)
const inventoryList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filterProductId = ref('')
const filterWarehouseId = ref('')
const productOptions = ref([])
const warehouseOptions = ref([])
// 库存调整相关
const adjustDialogVisible = ref(false)
const currentInventory = ref({})
const adjustForm = reactive({
  quantity: 0,
  reason: ''
})

// 库存移动相关
const moveDialogVisible = ref(false)
const moveForm = reactive({
  destinationWarehouseId: '',
  quantity: 0,
  reason: ''
})

// 获取库存列表
const fetchInventories = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      query: searchQuery.value,
      productId: filterProductId.value,
      warehouseId: filterWarehouseId.value
    }

    const res = await getInventories(params)
    inventoryList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('Failed to fetch inventories:', error)
    ElMessage.error('获取库存列表失败')
  } finally {
    loading.value = false
  }
}

// 获取产品选项
const fetchProductOptions = async () => {
  try {
    const res = await getProducts({ pageSize: 100 })
    productOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch products:', error)
  }
}

// 获取仓库选项
const fetchWarehouseOptions = async () => {
  try {
    const res = await getWarehouses({ pageSize: 100 })
    warehouseOptions.value = res.data
  } catch (error) {
    console.error('Failed to fetch warehouses:', error)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchInventories()
}

// 处理刷新
const handleRefresh = () => {
  searchQuery.value = ''
  filterProductId.value = ''
  filterWarehouseId.value = ''
  currentPage.value = 1
  fetchInventories()
}

// 处理导出
const handleExport = () => {
  ElMessage.success('导出功能待实现')
}

// 处理查看
const handleView = (row) => {
  router.push(`/inventory/${row.id}`)
}

// 处理库存调整
const handleAdjust = (row) => {
  currentInventory.value = row
  adjustForm.quantity = row.quantity
  adjustForm.reason = ''
  adjustDialogVisible.value = true
}

// 提交库存调整
const submitAdjust = async () => {
  try {
    await createStockAdjustment({
      productId: currentInventory.value.productId,
      sourceWarehouseId: currentInventory.value.warehouseId,
      quantity: adjustForm.quantity,
      reason: adjustForm.reason || '手动调整',
      movementDate: new Date().toISOString().split('T')[0]
    })

    ElMessage.success('库存调整成功')
    adjustDialogVisible.value = false
    fetchInventories()
  } catch (error) {
    console.error('Failed to adjust inventory:', error)
    ElMessage.error('库存调整失败')
  }
}

// 处理库存移动
const handleMove = (row) => {
  currentInventory.value = row
  moveForm.destinationWarehouseId = ''
  moveForm.quantity = row.quantity
  moveForm.reason = ''
  moveDialogVisible.value = true
}

// 提交库存移动
const submitMove = async () => {
  if (!moveForm.destinationWarehouseId) {
    ElMessage.warning('请选择目标仓库')
    return
  }

  try {
    await createStockTransfer({
      productId: currentInventory.value.productId,
      sourceWarehouseId: currentInventory.value.warehouseId,
      destinationWarehouseId: moveForm.destinationWarehouseId,
      quantity: moveForm.quantity,
      reason: moveForm.reason || '库存调拨',
      movementDate: new Date().toISOString().split('T')[0]
    })

    ElMessage.success('库存移动成功')
    moveDialogVisible.value = false
    fetchInventories()
  } catch (error) {
    console.error('Failed to move inventory:', error)
    ElMessage.error('库存移动失败')
  }
}

// 获取状态类型
const getStatusType = (inventory) => {
  if (!inventory.product) return ''

  if (inventory.quantity <= 0) {
    return 'danger'
  } else if (inventory.quantity < inventory.product.minStock) {
    return 'warning'
  } else if (inventory.quantity > inventory.product.maxStock) {
    return 'info'
  } else {
    return 'success'
  }
}

// 获取状态文本
const getStatusText = (inventory) => {
  if (!inventory.product) return ''

  if (inventory.quantity <= 0) {
    return '无库存'
  } else if (inventory.quantity < inventory.product.minStock) {
    return '库存不足'
  } else if (inventory.quantity > inventory.product.maxStock) {
    return '库存过多'
  } else {
    return '正常'
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchInventories()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchInventories()
}

onMounted(() => {
  fetchInventories()
  fetchProductOptions()
  fetchWarehouseOptions()
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  gap: 10px;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.inner-table {
  margin: 10px;
}

.no-batches {
  padding: 20px;
  text-align: center;
}
</style>
