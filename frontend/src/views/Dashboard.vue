<template>
  <div class="page-container">
    <div class="page-title">仪表盘</div>

    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6" v-for="(card, index) in statCards" :key="index">
        <el-card class="stat-card" :body-style="{ padding: '20px' }">
          <div class="stat-card-content">
            <div class="stat-card-icon" :style="{ backgroundColor: card.color }">
              <el-icon><component :is="card.icon" /></el-icon>
            </div>
            <div class="stat-card-info">
              <div class="stat-card-title">{{ card.title }}</div>
              <div class="stat-card-value">{{ card.value }}</div>
            </div>
          </div>
          <div class="stat-card-footer" @click="handleCardClick(card.type)">
            <span>{{ card.footer }}</span>
            <el-icon><ArrowRight /></el-icon>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <!-- 销售趋势图 -->
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>销售趋势</span>
              <el-radio-group v-model="salesTimeRange" size="small">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <div ref="salesChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>

      <!-- 库存状态图 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>库存状态</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="inventoryChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="data-row">
      <!-- 最近订单 -->
      <el-col :span="12">
        <el-card class="data-card">
          <template #header>
            <div class="data-header">
              <span>最近订单</span>
              <el-button text @click="viewAllOrders">查看全部</el-button>
            </div>
          </template>
          <el-table :data="recentOrders" stripe style="width: 100%">
            <el-table-column prop="id" label="订单号" width="100" />
            <el-table-column prop="customer" label="客户" width="120" />
            <el-table-column prop="amount" label="金额" width="100">
              <template #default="scope">
                ¥{{ scope.row.amount.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="日期" />
          </el-table>
        </el-card>
      </el-col>

      <!-- 库存预警 -->
      <el-col :span="12">
        <el-card class="data-card">
          <template #header>
            <div class="data-header">
              <span>库存预警</span>
              <el-button text @click="viewAllInventory">查看全部</el-button>
            </div>
          </template>
          <el-table :data="inventoryAlerts" stripe style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="产品名称" min-width="120" />
            <el-table-column prop="current" label="当前库存" width="100" />
            <el-table-column prop="min" label="最低库存" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === '紧急' ? 'danger' : 'warning'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import {
  Goods,
  ShoppingCart,
  User,
  Money,
  ArrowRight
} from '@element-plus/icons-vue'
import { getDashboardOverview, getInventoryAlert, getRecentOrders } from '../api/dashboard'
import { ElMessage } from 'element-plus'

const router = useRouter()
const salesChartRef = ref(null)
const inventoryChartRef = ref(null)
const salesTimeRange = ref('month')
let salesChart = null
let inventoryChart = null

// 统计卡片数据
const statCards = ref([
  {
    title: '总产品数',
    value: '0',
    icon: 'Goods',
    color: '#409EFF',
    footer: '查看所有产品',
    type: 'products'
  },
  {
    title: '本月订单',
    value: '0',
    icon: 'ShoppingCart',
    color: '#67C23A',
    footer: '查看所有订单',
    type: 'orders'
  },
  {
    title: '活跃客户',
    value: '0',
    icon: 'User',
    color: '#E6A23C',
    footer: '查看所有客户',
    type: 'customers'
  },
  {
    title: '本月销售额',
    value: '¥0',
    icon: 'Money',
    color: '#F56C6C',
    footer: '查看销售报告',
    type: 'reports'
  }
])

// 最近订单数据
const recentOrders = ref([])

// 库存预警数据
const inventoryAlerts = ref([])

// 获取订单状态对应的标签类型
const getOrderStatusType = (status) => {
  const statusMap = {
    '已完成': 'success',
    '处理中': 'primary',
    '待付款': 'warning',
    '已发货': 'info'
  }
  return statusMap[status] || 'info'
}

// 初始化销售趋势图表
const initSalesChart = () => {
  if (salesChart) {
    salesChart.dispose()
  }

  salesChart = echarts.init(salesChartRef.value)

  // 根据时间范围获取不同的数据
  let xData = []
  let yData = []

  if (salesTimeRange.value === 'week') {
    xData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    yData = [5600, 4800, 6200, 5100, 7800, 8500, 6900]
  } else if (salesTimeRange.value === 'month') {
    xData = Array.from({length: 30}, (_, i) => i + 1)
    yData = Array.from({length: 30}, () => Math.floor(Math.random() * 5000) + 3000)
  } else {
    xData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    yData = [45600, 38500, 52300, 61000, 49800, 58700, 65200, 59800, 62100, 68500, 71200, 78500]
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br />销售额: ¥{c}'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: '销售额',
        type: 'line',
        smooth: true,
        data: yData,
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.7)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  salesChart.setOption(option)
}

// 初始化库存状态图表
const initInventoryChart = () => {
  if (inventoryChart) {
    inventoryChart.dispose()
  }

  inventoryChart = echarts.init(inventoryChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['充足', '正常', '偏低', '紧急']
    },
    series: [
      {
        name: '库存状态',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 45, name: '充足', itemStyle: { color: '#67C23A' } },
          { value: 30, name: '正常', itemStyle: { color: '#409EFF' } },
          { value: 15, name: '偏低', itemStyle: { color: '#E6A23C' } },
          { value: 10, name: '紧急', itemStyle: { color: '#F56C6C' } }
        ]
      }
    ]
  }

  inventoryChart.setOption(option)
}

// 查看所有订单
const viewAllOrders = () => {
  router.push('/orders')
}

// 查看所有库存
const viewAllInventory = () => {
  router.push('/inventory')
}

// 处理卡片点击事件
const handleCardClick = (type) => {
  switch (type) {
    case 'products':
      router.push('/products')
      break
    case 'orders':
      router.push('/orders')
      break
    case 'customers':
      router.push('/customers')
      break
    case 'reports':
      router.push('/reports')
      break
    default:
      console.warn('未知的卡片类型:', type)
  }
}

// 监听销售时间范围变化
watch(salesTimeRange, () => {
  initSalesChart()
})

// 监听窗口大小变化，重新调整图表大小
window.addEventListener('resize', () => {
  salesChart?.resize()
  inventoryChart?.resize()
})

// 获取仪表盘概览数据
const fetchDashboardOverview = async () => {
  try {
    const res = await getDashboardOverview()

    // 更新统计卡片数据
    statCards.value[0].value = res.totalProducts.toString()
    statCards.value[1].value = res.monthlyOrders.toString()
    statCards.value[2].value = res.activeCustomers.toString()
    statCards.value[3].value = `¥${res.monthlySales.toFixed(2)}`
  } catch (error) {
    console.error('Failed to fetch dashboard overview:', error)
    ElMessage.error('获取仪表盘概览数据失败')
  }
}

// 获取库存预警数据
const fetchInventoryAlert = async () => {
  try {
    const res = await getInventoryAlert()
    inventoryAlerts.value = res

    // 更新库存状态图表数据
    updateInventoryChart(res)
  } catch (error) {
    console.error('Failed to fetch inventory alert:', error)
    ElMessage.error('获取库存预警数据失败')
  }
}

// 获取最近订单
const fetchRecentOrders = async () => {
  try {
    const res = await getRecentOrders()
    recentOrders.value = res
  } catch (error) {
    console.error('Failed to fetch recent orders:', error)
    ElMessage.error('获取最近订单失败')
  }
}

// 更新库存状态图表
const updateInventoryChart = (alerts) => {
  if (!inventoryChart) return

  // 统计各状态的数量
  let urgent = 0
  let warning = 0
  let normal = 0
  let sufficient = 0

  // 遍历所有产品库存
  alerts.forEach(alert => {
    if (alert.current <= 0) {
      urgent++
    } else if (alert.current < alert.min) {
      urgent++
    } else if (alert.current > alert.max) {
      warning++
    } else {
      normal++
    }
  })

  // 假设还有一些产品库存充足
  sufficient = Math.max(10, alerts.length)

  const option = {
    series: [{
      data: [
        { value: sufficient, name: '充足', itemStyle: { color: '#67C23A' } },
        { value: normal, name: '正常', itemStyle: { color: '#409EFF' } },
        { value: warning, name: '偏低', itemStyle: { color: '#E6A23C' } },
        { value: urgent, name: '紧急', itemStyle: { color: '#F56C6C' } }
      ]
    }]
  }

  inventoryChart.setOption(option)
}

onMounted(() => {
  initSalesChart()
  initInventoryChart()

  // 检查用户是否已认证，只有认证用户才获取数据
  const token = localStorage.getItem('token')
  if (token) {
    // 获取真实数据
    fetchDashboardOverview()
    fetchInventoryAlert()
    fetchRecentOrders()
  }
})
</script>

<style scoped>
.chart-row, .data-row {
  margin-top: 20px;
}

.stat-card {
  height: 120px;
  margin-bottom: 20px;
}

.stat-card-content {
  display: flex;
  align-items: center;
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.stat-card-icon .el-icon {
  font-size: 30px;
  color: white;
}

.stat-card-info {
  flex: 1;
}

.stat-card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-card-footer {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #409EFF;
  font-size: 13px;
  cursor: pointer;
}

.chart-card, .data-card {
  margin-bottom: 20px;
  height: 400px;
}

.chart-header, .data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
