import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, logout, getUserInfo } from '../api/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref({})
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value)

  async function loginUser(username, password) {
    loading.value = true
    try {
      console.log('Attempting login with:', { username, password })
      const response = await login(username, password)
      console.log('Login response:', response)

      // 处理成功但没有数据的情况
      if (response.success === true) {
        console.log('Login successful but no token returned')
        return true
      }

      // 检查响应中是否直接包含token
      if (response && response.token) {
        console.log('Token found directly in response')
        token.value = response.token
        localStorage.setItem('token', token.value)

        // 如果响应中包含用户信息，直接使用
        if (response.user) {
          userInfo.value = response.user
        } else {
          // 否则获取用户信息
          await fetchUserInfo()
        }
        return true
      }

      // 检查response.data中是否包含token
      if (response && response.data && response.data.token) {
        console.log('Token found in response.data')
        token.value = response.data.token
        localStorage.setItem('token', token.value)

        // 如果响应中包含用户信息，直接使用
        if (response.data.user) {
          userInfo.value = response.data.user
        } else {
          // 否则获取用户信息
          await fetchUserInfo()
        }
        return true
      }

      console.error('Login response missing token:', response)
      return false
    } catch (error) {
      console.error('Login failed:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  async function logoutUser() {
    try {
      await logout()
    } catch (error) {
      // 即使后端请求失败（例如404），我们仍然要清除本地的token和用户信息
      console.error('Logout error:', error)
    } finally {
      // 无论后端请求成功与否，都要清除本地的token和用户信息
      token.value = ''
      userInfo.value = {}
      localStorage.removeItem('token')
    }
  }

  async function fetchUserInfo() {
    if (!token.value) return

    loading.value = true
    try {
      const response = await getUserInfo()
      userInfo.value = response.data
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      token.value = ''
      localStorage.removeItem('token')
    } finally {
      loading.value = false
    }
  }

  // 重置store状态的方法
  function $reset() {
    token.value = ''
    userInfo.value = {}
    loading.value = false
  }

  return {
    token,
    userInfo,
    loading,
    isAuthenticated,
    loginUser,
    logoutUser,
    fetchUserInfo,
    $reset
  }
})
