<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>仓库管理系统</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
      background-color: #f5f7fa;
      color: #303133;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .app-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    
    .sidebar {
      width: 220px;
      background-color: #304156;
      color: #bfcbd9;
      overflow-y: auto;
    }
    
    .logo {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #2b2f3a;
      color: #fff;
      font-size: 18px;
      font-weight: bold;
    }
    
    .menu {
      list-style: none;
    }
    
    .menu-item {
      padding: 14px 20px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .menu-item:hover {
      background-color: #263445;
    }
    
    .menu-item.active {
      background-color: #1890ff;
      color: #fff;
    }
    
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .header {
      height: 60px;
      background-color: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
    }
    
    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
    
    .footer {
      height: 40px;
      background-color: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #606266;
    }
    
    .card {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .card-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 10px;
    }
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .stat-card {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;
      text-align: center;
      transition: all 0.3s;
    }
    
    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
      color: #409EFF;
    }
    
    .stat-title {
      font-size: 14px;
      color: #606266;
    }
    
    .table {
      width: 100%;
      border-collapse: collapse;
    }
    
    .table th, .table td {
      padding: 12px 8px;
      text-align: left;
      border-bottom: 1px solid #ebeef5;
    }
    
    .table th {
      background-color: #f5f7fa;
      font-weight: bold;
    }
    
    .table tr:hover td {
      background-color: #f5f7fa;
    }
    
    .button {
      display: inline-block;
      padding: 8px 16px;
      background-color: #409EFF;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .button:hover {
      background-color: #66b1ff;
    }
    
    .button.danger {
      background-color: #F56C6C;
    }
    
    .button.danger:hover {
      background-color: #f78989;
    }
    
    .button.success {
      background-color: #67C23A;
    }
    
    .button.success:hover {
      background-color: #85ce61;
    }
    
    .button.warning {
      background-color: #E6A23C;
    }
    
    .button.warning:hover {
      background-color: #ebb563;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <div class="sidebar">
      <div class="logo">仓库管理系统</div>
      <ul class="menu">
        <li class="menu-item active">仪表盘</li>
        <li class="menu-item">订单管理</li>
        <li class="menu-item">产品管理</li>
        <li class="menu-item">库存管理</li>
        <li class="menu-item">出入库管理</li>
        <li class="menu-item">客户管理</li>
        <li class="menu-item">报表管理</li>
        <li class="menu-item">系统设置</li>
      </ul>
    </div>
    <div class="main-content">
      <div class="header">
        <div class="breadcrumb">首页 / 仪表盘</div>
        <div class="user-info">管理员</div>
      </div>
      <div class="content">
        <div class="stats-container">
          <div class="stat-card">
            <div class="stat-title">产品总数</div>
            <div class="stat-value">128</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">库存总量</div>
            <div class="stat-value">5,280</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">本月出库</div>
            <div class="stat-value">1,024</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">本月入库</div>
            <div class="stat-value">2,048</div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-title">库存预警</div>
          <table class="table">
            <thead>
              <tr>
                <th>产品编码</th>
                <th>产品名称</th>
                <th>当前库存</th>
                <th>最低库存</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>P001</td>
                <td>产品A</td>
                <td>10</td>
                <td>20</td>
                <td style="color: #F56C6C;">库存不足</td>
                <td><button class="button">补货</button></td>
              </tr>
              <tr>
                <td>P002</td>
                <td>产品B</td>
                <td>5</td>
                <td>15</td>
                <td style="color: #F56C6C;">库存不足</td>
                <td><button class="button">补货</button></td>
              </tr>
              <tr>
                <td>P003</td>
                <td>产品C</td>
                <td>25</td>
                <td>20</td>
                <td style="color: #67C23A;">库存正常</td>
                <td><button class="button">查看</button></td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="card">
          <div class="card-title">最近出入库记录</div>
          <table class="table">
            <thead>
              <tr>
                <th>时间</th>
                <th>类型</th>
                <th>产品</th>
                <th>数量</th>
                <th>操作人</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>2023-05-15 10:30</td>
                <td>入库</td>
                <td>产品A</td>
                <td>100</td>
                <td>张三</td>
                <td>采购入库</td>
              </tr>
              <tr>
                <td>2023-05-14 15:20</td>
                <td>出库</td>
                <td>产品B</td>
                <td>50</td>
                <td>李四</td>
                <td>销售出库</td>
              </tr>
              <tr>
                <td>2023-05-13 09:15</td>
                <td>调拨</td>
                <td>产品C</td>
                <td>30</td>
                <td>王五</td>
                <td>仓库间调拨</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="footer">
        仓库管理系统 &copy; 2023
      </div>
    </div>
  </div>
</body>
</html>
